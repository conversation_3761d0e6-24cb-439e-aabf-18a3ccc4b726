#!/usr/bin/env python3
"""
Test Enhanced AI Matching Caching Fix
Verifies that skills are properly cached and not extracted repeatedly.
"""

import os
import sys
import json
import logging
import time
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_caching_fix():
    """Test that skills caching works properly"""
    print("🧪 TESTING ENHANCED AI MATCHING CACHING FIX")
    print("=" * 80)
    
    try:
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize components
        print("🔧 Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False
        
        print("✅ AI connection successful")
        
        # Clear cache to start fresh
        print("🗑️ Clearing skills cache to start fresh...")
        matcher.skills_cache = {}
        
        # Load consultants
        consultants = load_test_consultants()
        if not consultants:
            print("❌ No test consultants loaded")
            return False
        
        print(f"📋 Loaded {len(consultants)} test consultants")
        
        # Test caching with a simple job description
        test_job_description = """
        We are looking for a Java Developer with Spring Boot experience.
        
        Required Skills:
        - 5+ years of Java development experience
        - Strong experience with Spring Boot and Spring Framework
        - Experience with REST APIs
        - SQL database experience
        
        Location: Remote
        Experience Level: Senior (5+ years)
        """
        
        print(f"\n🔍 CACHING TEST:")
        print("="*80)
        
        # First run - should extract skills
        print(f"🚀 FIRST RUN - Should extract skills from resumes...")
        start_time = time.time()
        
        selected_consultants_1, ai_message_1, ai_response_1 = matcher.enhanced_match_consultant_to_job(
            test_job_description, consultants
        )
        
        first_run_time = time.time() - start_time
        print(f"⏱️ First run completed in {first_run_time:.2f} seconds")
        
        if selected_consultants_1:
            print(f"✅ First run found {len(selected_consultants_1)} consultant(s)")
            for consultant in selected_consultants_1:
                print(f"   - {consultant.get('name', 'Unknown')}")
        else:
            print(f"❌ First run found no matches")
        
        # Check cache size
        cache_size = len(matcher.skills_cache)
        print(f"📊 Skills cache now contains {cache_size} entries")
        
        # Brief pause
        print(f"\n⏱️ Waiting 3 seconds before second run...")
        time.sleep(3)
        
        # Second run - should use cached skills
        print(f"\n🚀 SECOND RUN - Should use cached skills...")
        start_time = time.time()
        
        selected_consultants_2, ai_message_2, ai_response_2 = matcher.enhanced_match_consultant_to_job(
            test_job_description, consultants
        )
        
        second_run_time = time.time() - start_time
        print(f"⏱️ Second run completed in {second_run_time:.2f} seconds")
        
        if selected_consultants_2:
            print(f"✅ Second run found {len(selected_consultants_2)} consultant(s)")
            for consultant in selected_consultants_2:
                print(f"   - {consultant.get('name', 'Unknown')}")
        else:
            print(f"❌ Second run found no matches")
        
        # Analyze caching performance
        print(f"\n📊 CACHING ANALYSIS:")
        print("="*80)
        
        print(f"First run time: {first_run_time:.2f} seconds")
        print(f"Second run time: {second_run_time:.2f} seconds")
        
        if second_run_time < first_run_time:
            speedup = (first_run_time - second_run_time) / first_run_time * 100
            print(f"✅ CACHING WORKING: {speedup:.1f}% faster on second run")
        else:
            print(f"⚠️ CACHING ISSUE: Second run was not faster")
        
        # Check if results are consistent
        if selected_consultants_1 and selected_consultants_2:
            names_1 = [c.get('name', '') for c in selected_consultants_1]
            names_2 = [c.get('name', '') for c in selected_consultants_2]
            
            if names_1 == names_2:
                print(f"✅ CONSISTENCY: Both runs selected the same consultants")
            else:
                print(f"⚠️ INCONSISTENCY: Different consultants selected")
                print(f"   First run: {', '.join(names_1)}")
                print(f"   Second run: {', '.join(names_2)}")
        
        # Check cache efficiency
        cache_size_after = len(matcher.skills_cache)
        print(f"📊 Final cache size: {cache_size_after} entries")
        
        if cache_size_after == cache_size:
            print(f"✅ CACHE EFFICIENCY: No new extractions on second run")
        else:
            print(f"⚠️ CACHE ISSUE: Cache size changed during second run")
        
        print(f"\n🎉 CACHING TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error in caching test: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_test_consultants():
    """Load consultant data from CSV"""
    consultants = []
    
    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Skip header and take first 10 consultants for faster testing
    for line in lines[1:11]:  # Only test with first 10 consultants
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]
            
            # Try to find matching resume file
            resume_path = find_resume_file(name)
            
            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)
    
    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None
    
    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')
    
    # List all resume files
    resume_files = os.listdir(resumes_dir)
    
    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')
        
        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)
    
    return None

if __name__ == "__main__":
    success = test_caching_fix()
    sys.exit(0 if success else 1)
