#!/usr/bin/env python3
"""
Real-world test of Enhanced AI Matching with actual resumes and job descriptions
"""

import os
import sys
import json
import logging
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_consultants_from_csv():
    """Load consultant data from hotlist.csv and map to resume files"""
    consultants = []
    
    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Skip header
    for line in lines[1:]:
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]
            
            # Try to find matching resume file
            resume_path = find_resume_file(name)
            
            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)
    
    print(f"✅ Loaded {len(consultants)} consultants from CSV")
    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None
    
    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')
    
    # List all resume files
    resume_files = os.listdir(resumes_dir)
    
    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')
        
        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)
    
    return None

def test_with_random_job_descriptions():
    """Test with various random job descriptions"""
    
    job_descriptions = [
        {
            "title": "Senior Java Full Stack Developer",
            "description": """
            We are seeking a Senior Java Full Stack Developer for our fintech startup.
            
            Required Skills:
            - 8+ years of Java development experience
            - Strong experience with Spring Boot and Spring Framework
            - Frontend development with React or Angular
            - Experience with microservices architecture
            - Knowledge of AWS cloud services
            - Database experience with PostgreSQL or MySQL
            - Experience with Docker and Kubernetes
            
            Location: Remote (US timezone)
            Visa: H1B sponsorship available
            Experience Level: Senior (8+ years)
            """
        },
        {
            "title": "Data Analytics Manager",
            "description": """
            Looking for an experienced Data Analytics Manager to lead our analytics team.
            
            Required Skills:
            - 10+ years in data analytics and business intelligence
            - Strong experience with SQL, Python, and R
            - Experience with data visualization tools (Tableau, Power BI)
            - Knowledge of big data technologies (Spark, Hadoop)
            - Experience with cloud platforms (AWS, Azure)
            - Team leadership and project management experience
            - Strong communication and presentation skills
            
            Location: Chicago, IL (Hybrid)
            Visa: US Citizens or Green Card holders preferred
            Experience Level: Manager (10+ years)
            """
        },
        {
            "title": ".NET Full Stack Developer",
            "description": """
            We need a skilled .NET Full Stack Developer for our enterprise applications.
            
            Required Skills:
            - 5+ years of .NET Core/.NET Framework experience
            - Strong C# programming skills
            - Experience with ASP.NET MVC and Web API
            - Frontend development with Angular or React
            - SQL Server database experience
            - Knowledge of Azure cloud services
            - Experience with DevOps practices and CI/CD
            
            Location: New York, NY
            Visa: H1B welcome
            Experience Level: Mid to Senior (5+ years)
            """
        },
        {
            "title": "DevOps Engineer",
            "description": """
            Seeking a DevOps Engineer to manage our cloud infrastructure and deployment pipelines.
            
            Required Skills:
            - 6+ years of DevOps and cloud infrastructure experience
            - Strong experience with AWS services (EC2, S3, Lambda, ECS)
            - Expertise in containerization (Docker, Kubernetes)
            - Experience with Infrastructure as Code (Terraform, CloudFormation)
            - CI/CD pipeline development (Jenkins, GitLab CI)
            - Monitoring and logging tools (CloudWatch, ELK stack)
            - Scripting skills (Python, Bash)
            
            Location: San Francisco, CA
            Visa: Any valid work authorization
            Experience Level: Senior (6+ years)
            """
        }
    ]
    
    return job_descriptions

def run_enhanced_matching_test():
    """Run comprehensive test with real data"""
    try:
        print("🚀 ENHANCED AI MATCHING - REAL WORLD TEST")
        print("=" * 60)
        
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize the matcher
        print("Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False
        
        print("✅ AI connection successful")
        
        # Load consultants
        print("\n📋 Loading consultant data...")
        consultants = load_consultants_from_csv()
        
        if not consultants:
            print("❌ No consultants loaded")
            return False
        
        # Show consultant summary
        consultants_with_resumes = [c for c in consultants if c['resume_path']]
        print(f"📊 Consultant Summary:")
        print(f"   Total consultants: {len(consultants)}")
        print(f"   With resume files: {len(consultants_with_resumes)}")
        print(f"   Resume match rate: {len(consultants_with_resumes)/len(consultants)*100:.1f}%")
        
        # Show some examples
        print(f"\n📄 Sample consultants with resumes:")
        for i, consultant in enumerate(consultants_with_resumes[:5]):
            resume_file = os.path.basename(consultant['resume_path']) if consultant['resume_path'] else 'None'
            print(f"   {i+1}. {consultant['name']} - {consultant['skills'][:50]}... -> {resume_file}")
        
        # Test with job descriptions
        job_descriptions = test_with_random_job_descriptions()
        
        print(f"\n🎯 Testing with {len(job_descriptions)} different job descriptions...")
        
        for i, job in enumerate(job_descriptions, 1):
            print(f"\n" + "="*60)
            print(f"🔍 TEST {i}: {job['title']}")
            print("="*60)
            
            print(f"📋 Job Description:")
            print(job['description'][:300] + "..." if len(job['description']) > 300 else job['description'])
            
            # Run enhanced matching
            print(f"\n🤖 Running Enhanced AI Matching...")
            consultant_name, ai_message, ai_response = matcher.enhanced_match_consultant_to_job(
                job['description'], consultants
            )
            
            if consultant_name and ai_message:
                print(f"\n✅ MATCH FOUND!")
                print(f"👤 Selected Consultant: {consultant_name}")
                
                # Find the selected consultant details
                selected_consultant = next((c for c in consultants if c['name'] == consultant_name), None)
                if selected_consultant:
                    print(f"📍 Location: {selected_consultant['location']}")
                    print(f"🛂 Visa Status: {selected_consultant['visa_status']}")
                    print(f"📅 Experience: {selected_consultant['experience']} years")
                    print(f"🏠 Relocation: {selected_consultant['relocation']}")
                    resume_file = os.path.basename(selected_consultant['resume_path']) if selected_consultant['resume_path'] else 'None'
                    print(f"📄 Resume File: {resume_file}")
                
                print(f"\n📊 AI Analysis:")
                print(f"   Confidence: {ai_response.get('match_confidence', 'Unknown')}")
                print(f"   Skill Match: {ai_response.get('skill_match_percentage', 0)}%")
                print(f"   Key Skills: {', '.join(ai_response.get('key_matching_skills', []))}")
                print(f"   Reasoning: {ai_response.get('match_reasoning', 'No reasoning provided')[:200]}...")
                
                print(f"\n📧 Generated Email Preview:")
                print("-" * 40)
                print(ai_message[:400] + "..." if len(ai_message) > 400 else ai_message)
                print("-" * 40)
                
            else:
                print(f"❌ No suitable match found for this position")
            
            print(f"\n⏱️ Waiting before next test...")
            import time
            time.sleep(2)  # Brief pause between tests
        
        print(f"\n🎉 Enhanced AI Matching test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error in enhanced matching test: {e}")
        import traceback
        traceback.print_exc()
        return False

def extract_skills_from_resumes():
    """Extract skills from actual resume files"""
    try:
        print("\n🧠 SKILL EXTRACTION FROM ACTUAL RESUMES")
        print("=" * 50)
        
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        matcher = EnhancedAIConsultantMatcher()
        consultants = load_consultants_from_csv()
        
        # Filter to consultants with resume files
        consultants_with_resumes = [c for c in consultants if c['resume_path'] and os.path.exists(c['resume_path'])]
        
        print(f"📄 Found {len(consultants_with_resumes)} consultants with actual resume files")
        
        if len(consultants_with_resumes) == 0:
            print("❌ No resume files found for skill extraction")
            return False
        
        # Extract skills from first 3 resumes as a sample
        sample_consultants = consultants_with_resumes[:3]
        
        print(f"🔍 Extracting skills from {len(sample_consultants)} sample resumes...")
        
        for consultant in sample_consultants:
            print(f"\n📋 Processing: {consultant['name']}")
            resume_file = os.path.basename(consultant['resume_path'])
            print(f"   Resume: {resume_file}")
            
            # Extract skills
            extracted_skills = matcher.extract_skills_from_resume(consultant['resume_path'])
            
            print(f"   CSV Skills: {consultant['skills'][:100]}...")
            print(f"   AI Extracted: {len(extracted_skills)} skills")
            
            if extracted_skills:
                print(f"   Sample AI Skills: {', '.join(extracted_skills[:10])}{'...' if len(extracted_skills) > 10 else ''}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in skill extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the comprehensive test"""
    print("🧪 ENHANCED AI MATCHING - COMPREHENSIVE REAL-WORLD TEST")
    print("=" * 70)
    
    # Test 1: Extract skills from actual resumes
    print("\n🔬 TEST 1: Skill Extraction from Real Resumes")
    skill_extraction_success = extract_skills_from_resumes()
    
    # Test 2: Enhanced matching with job descriptions
    print("\n🔬 TEST 2: Enhanced AI Matching with Random Job Descriptions")
    matching_success = run_enhanced_matching_test()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Skill Extraction: {'✅ PASS' if skill_extraction_success else '❌ FAIL'}")
    print(f"Enhanced Matching: {'✅ PASS' if matching_success else '❌ FAIL'}")
    
    if skill_extraction_success and matching_success:
        print("\n🎉 ALL TESTS PASSED! Enhanced AI Matching system is working perfectly!")
        print("🚀 The system can now:")
        print("   ✅ Extract skills from actual resume files using AI")
        print("   ✅ Match job descriptions against real resume content")
        print("   ✅ Provide detailed match analysis and reasoning")
        print("   ✅ Generate personalized email responses")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")
    
    return skill_extraction_success and matching_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
