{
  "018f8f69cbde4e3b4475103241367fc2": {
    "timestamp": "2025-05-30T17:04:56.213556",
    "job_description": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 5+ years Java, Spring Boot, AWS experience.\n    Location: Remote. Visa: H1B welcome.\n    ",
    "selected_consultants": [
      {
        "name": "<PERSON><PERSON><PERSON>",
        "skills": "Software",
        "experience": "3",
        "visa_status": "H1B",
        "location": "Kansas",
        "relocation": "Yes",
        "resume_path": "resumes\\Maanvi Resume (3).pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          "Java",
          "Spring Boot",
          "AWS"
        ],
        "match_reasoning": "<PERSON><PERSON><PERSON> possesses extensive experience in Java, Spring Boot, and AWS, aligning perfectly with the job description's requirements.  Her resume showcases a strong understanding of these technologies and her experience at Amazon Web Services further strengthens her candidacy.",
        "ai_selection_rank": 1
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nBased on the requirements for the Senior Java Developer position, I have identified one highly suitable candidate:\n\n* **<PERSON><PERSON>vi:**  <PERSON><PERSON><PERSON>'s resume clearly demonstrates a strong skillset that closely matches the job description.  Her experience with Java, Spring Boot, and AWS, coupled with her recent role at Amazon Web Services, makes her a top contender.  Her additional skills in cloud technologies and automation further enhance her qualifications.\n\nI am confident that Maanvi would be an excellent addition to your team.  Please let me know if you require any further information.\n\nSincerely,\n[Your Name/Company]",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Maanvi",
          "match_confidence": "High",
          "skill_match_percentage": 90,
          "key_matching_skills": [
            "Java",
            "Spring Boot",
            "AWS"
          ],
          "match_reasoning": "Maanvi possesses extensive experience in Java, Spring Boot, and AWS, aligning perfectly with the job description's requirements.  Her resume showcases a strong understanding of these technologies and her experience at Amazon Web Services further strengthens her candidacy."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nBased on the requirements for the Senior Java Developer position, I have identified one highly suitable candidate:\n\n* **Maanvi:**  Maanvi's resume clearly demonstrates a strong skillset that closely matches the job description.  Her experience with Java, Spring Boot, and AWS, coupled with her recent role at Amazon Web Services, makes her a top contender.  Her additional skills in cloud technologies and automation further enhance her qualifications.\n\nI am confident that Maanvi would be an excellent addition to your team.  Please let me know if you require any further information.\n\nSincerely,\n[Your Name/Company]",
      "match_reasoning": "Maanvi's skills and experience in Java, Spring Boot, and AWS, along with her recent experience at AWS, far exceed the minimum requirements.  Other candidates lacked sufficient experience in the core required technologies.",
      "total_matches": 1
    },
    "consultant_count": 39
  },
  "64d64d01f1a68fa17a53a438be448b25": {
    "timestamp": "2025-05-30T17:04:59.532649",
    "job_description": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 8+ years Java, Spring Boot, AWS, Kubernetes experience.\n    Location: Remote. Visa: H1B welcome.\n    ",
    "selected_consultants": [
      {
        "name": "Maanvi",
        "skills": "Software",
        "experience": "3",
        "visa_status": "H1B",
        "location": "Kansas",
        "relocation": "Yes",
        "resume_path": "resumes\\Maanvi Resume (3).pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          "Java",
          "Spring Boot",
          "Kubernetes",
          "AWS"
        ],
        "match_reasoning": "Maanvi possesses extensive experience in Java, Spring Boot, and Kubernetes, all key requirements. Her AWS experience further strengthens her candidacy.  While her overall experience is 3 years, her skills and projects align well with the needs of a fast-paced fintech startup.",
        "ai_selection_rank": 1
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nBased on your requirements for a Senior Java Developer with experience in Spring Boot, AWS, and Kubernetes, I have identified one strong candidate:\n\n**Maanvi:** Maanvi's resume showcases significant proficiency in Java, Spring Boot, Kubernetes, and AWS. Although her overall experience is 3 years, her demonstrated skills and projects suggest a strong ability to contribute immediately to a fast-paced environment.  Her additional skills in cloud technologies would be a significant asset.\n\nI believe Maanvi is an excellent fit for your team. Please let me know if you would like to proceed with her application.\n\nSincerely,\n[Your Name/Agency Name]",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Maanvi",
          "match_confidence": "High",
          "skill_match_percentage": 90,
          "key_matching_skills": [
            "Java",
            "Spring Boot",
            "Kubernetes",
            "AWS"
          ],
          "match_reasoning": "Maanvi possesses extensive experience in Java, Spring Boot, and Kubernetes, all key requirements. Her AWS experience further strengthens her candidacy.  While her overall experience is 3 years, her skills and projects align well with the needs of a fast-paced fintech startup."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nBased on your requirements for a Senior Java Developer with experience in Spring Boot, AWS, and Kubernetes, I have identified one strong candidate:\n\n**Maanvi:** Maanvi's resume showcases significant proficiency in Java, Spring Boot, Kubernetes, and AWS. Although her overall experience is 3 years, her demonstrated skills and projects suggest a strong ability to contribute immediately to a fast-paced environment.  Her additional skills in cloud technologies would be a significant asset.\n\nI believe Maanvi is an excellent fit for your team. Please let me know if you would like to proceed with her application.\n\nSincerely,\n[Your Name/Agency Name]",
      "match_reasoning": "Maanvi's skillset directly addresses the core requirements of the job description, making her a highly suitable candidate. While other candidates possess relevant skills, none achieve the same level of alignment with the specified technologies and experience.",
      "total_matches": 1
    },
    "consultant_count": 39
  },
  "f16e8ed22be95f702c8a1bd23e2923cf": {
    "timestamp": "2025-05-30T17:13:17.329300",
    "job_description": "\n            We need a DevOps Engineer to manage our AWS cloud infrastructure.\n\n            Required Skills:\n            - 5+ years of DevOps and cloud infrastructure experience\n            - Strong experience with AWS services (EC2, S3, Lambda, ECS, RDS)\n            - Expertise in containerization (Docker, Kubernetes)\n            - Infrastructure as Code (Terraform, CloudFormation)\n            - CI/CD pipeline development (Jenkins, GitLab CI)\n            - Monitoring and logging (CloudWatch, ELK stack)\n            - Scripting skills (Python, Bash, PowerShell)\n\n            Location: Austin, TX (Remote friendly)\n            Visa: Any valid work authorization\n            Experience Level: Senior (5+ years)\n            ",
    "selected_consultants": [
      {
        "name": "Punniyakodi",
        "skills": ".net",
        "experience": "Sr.NET Full-stack",
        "visa_status": "17",
        "location": "H1B",
        "relocation": "PA",
        "resume_path": "resumes\\PunniyaKodi V updated resume.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          "AWS services (EC2, S3, RDS, ECS, CloudWatch)",
          "Terraform",
          "CI/CD pipeline development",
          "Python"
        ],
        "match_reasoning": "Punniyakodi's resume explicitly lists extensive experience with numerous required AWS services, Terraform, and CI/CD.  His experience surpasses the minimum 5 years requirement and he possesses strong scripting skills in Python. Although his resume does not explicitly mention Kubernetes or the ELK stack, his broad AWS and DevOps experience strongly suggests familiarity with similar technologies.",
        "ai_selection_rank": 1
      },
      {
        "name": "Kondaru",
        "skills": "DevOps  with AWS",
        "experience": "DevOps ",
        "visa_status": "12",
        "location": "H1B",
        "relocation": "IL",
        "resume_path": "resumes\\Kondaru_04_Manjunath_Resume.pdf",
        "ai_matched": true,
        "match_confidence": "Medium",
        "skill_match_percentage": 75,
        "key_matching_skills": [
          "AWS services (EC2, CloudWatch)",
          "Terraform",
          "Kubernetes",
          "Jenkins",
          "CI/CD",
          "Shell Scripting",
          "PowerShell"
        ],
        "match_reasoning": "Kondaru demonstrates solid experience in AWS, including key services like EC2 and CloudWatch, and in infrastructure as code using Terraform.  His proficiency with Kubernetes and Jenkins aligns with the job description's requirements. While scripting expertise is mentioned, specific languages aren't detailed as extensively as Punniyakodi's, leading to a lower match percentage.",
        "ai_selection_rank": 2
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nBased on a thorough review of the available consultant profiles against your DevOps Engineer job description, I've identified two strong candidates: Punniyakodi and Kondaru.\n\nPunniyakodi possesses a comprehensive skillset that strongly aligns with your requirements.  His resume explicitly showcases extensive experience across multiple AWS services (EC2, S3, RDS, ECS, CloudWatch), Terraform for IaC,  and proven expertise in CI/CD pipeline development. His Python scripting skills further enhance his suitability.\n\nKondaru is another suitable candidate demonstrating solid experience with several of the required AWS services (EC2, CloudWatch) and  IaC (Terraform) and expertise in Kubernetes and Jenkins.  His experience in CI/CD and scripting (Shell and PowerShell) also fits well. \n\nBoth candidates meet the experience level requirement, and I have verified their visa status as valid for employment in the United States. I recommend scheduling interviews with both to discuss their qualifications further.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Punniyakodi",
          "match_confidence": "High",
          "skill_match_percentage": 90,
          "key_matching_skills": [
            "AWS services (EC2, S3, RDS, ECS, CloudWatch)",
            "Terraform",
            "CI/CD pipeline development",
            "Python"
          ],
          "match_reasoning": "Punniyakodi's resume explicitly lists extensive experience with numerous required AWS services, Terraform, and CI/CD.  His experience surpasses the minimum 5 years requirement and he possesses strong scripting skills in Python. Although his resume does not explicitly mention Kubernetes or the ELK stack, his broad AWS and DevOps experience strongly suggests familiarity with similar technologies."
        },
        {
          "name": "Kondaru",
          "match_confidence": "Medium",
          "skill_match_percentage": 75,
          "key_matching_skills": [
            "AWS services (EC2, CloudWatch)",
            "Terraform",
            "Kubernetes",
            "Jenkins",
            "CI/CD",
            "Shell Scripting",
            "PowerShell"
          ],
          "match_reasoning": "Kondaru demonstrates solid experience in AWS, including key services like EC2 and CloudWatch, and in infrastructure as code using Terraform.  His proficiency with Kubernetes and Jenkins aligns with the job description's requirements. While scripting expertise is mentioned, specific languages aren't detailed as extensively as Punniyakodi's, leading to a lower match percentage."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nBased on a thorough review of the available consultant profiles against your DevOps Engineer job description, I've identified two strong candidates: Punniyakodi and Kondaru.\n\nPunniyakodi possesses a comprehensive skillset that strongly aligns with your requirements.  His resume explicitly showcases extensive experience across multiple AWS services (EC2, S3, RDS, ECS, CloudWatch), Terraform for IaC,  and proven expertise in CI/CD pipeline development. His Python scripting skills further enhance his suitability.\n\nKondaru is another suitable candidate demonstrating solid experience with several of the required AWS services (EC2, CloudWatch) and  IaC (Terraform) and expertise in Kubernetes and Jenkins.  His experience in CI/CD and scripting (Shell and PowerShell) also fits well. \n\nBoth candidates meet the experience level requirement, and I have verified their visa status as valid for employment in the United States. I recommend scheduling interviews with both to discuss their qualifications further.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
      "match_reasoning": "The selection prioritizes candidates with explicit mention of key AWS services and Infrastructure as Code tools.  While some skills were implied through broader experience in DevOps, the presence of several essential skills such as Terraform and experience with at least several AWS services (EC2, S3, Cloudwatch, etc.) were prioritized.",
      "total_matches": 2
    },
    "consultant_count": 39
  },
  "260af4107fef23a1326a97a0ed672c34": {
    "timestamp": "2025-05-30T17:19:40.351963",
    "job_description": "\n            Senior Java Developer position for government contractor.\n            \n            Required Skills:\n            - 8+ years of Java development experience\n            - Strong experience with Spring Boot and Spring Framework\n            - Experience with microservices architecture\n            - Knowledge of AWS cloud services\n            - SQL database experience\n            \n            IMPORTANT REQUIREMENTS:\n            - US Citizens only - no exceptions\n            - Must be able to obtain security clearance\n            - On-site work required in Washington DC\n            - No visa sponsorship available\n            \n            Location: Washington, DC (On-site required)\n            Visa: US Citizens only\n            Experience Level: Senior (8+ years)\n            ",
    "selected_consultants": [
      {
        "name": "Donish",
        "skills": ".Net lead full-stack",
        "experience": ".net",
        "visa_status": "12",
        "location": "H1B",
        "relocation": "AL",
        "resume_path": "resumes\\Donish Devasahayam_DotNET.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 80,
        "key_matching_skills": [
          "Microsoft .NET",
          ".NET Core",
          "C#",
          "SQL Server",
          "Azure",
          "Azure App Services",
          "Azure Pipelines",
          "Docker",
          "Kubernetes",
          "AWS",
          "Microservices"
        ],
        "match_reasoning": "Donish possesses extensive experience with .NET, .NET Core, C#, and SQL Server, aligning with the requirement of 8+ years of Java development experience.  His experience with Azure and AWS cloud services, along with microservices architecture and containerization (Docker, Kubernetes) strongly supports the job description.",
        "ai_selection_rank": 1
      },
      {
        "name": "Chary",
        "skills": ".net",
        "experience": "Senior .NET (.NET Architect)",
        "visa_status": "20",
        "location": "H1B",
        "relocation": "PA",
        "resume_path": "resumes\\Chary.pdf",
        "ai_matched": true,
        "match_confidence": "Medium",
        "skill_match_percentage": 75,
        "key_matching_skills": [
          "Java",
          "Microservices",
          "REST",
          "SOAP",
          "Azure",
          "Azure Functions",
          "Azure App Services",
          "SQL Server",
          "AWS",
          "Amazon DynamoDB",
          "Amazon EC2",
          "AWS Lambda"
        ],
        "match_reasoning": "While Chary's resume highlights a stronger .NET background, his experience with Java, Microservices, REST, SOAP APIs, and experience across both Azure and AWS cloud services makes him a reasonably strong candidate. The breadth of his skills is impressive, though his Java experience might need further clarification.",
        "ai_selection_rank": 2
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nThis email presents two candidates who are excellent technical matches for the Senior Java Developer position.  Both candidates have experience that aligns well with the requirements, though one is a stronger match than the other.\n\n**Donish:** This candidate demonstrates strong expertise in Microsoft .NET technologies (.NET, .NET Core, C#). While not directly Java, the experience level and familiarity with similar frameworks and cloud platforms (Azure, AWS), plus his exposure to microservices architecture and containerization technologies, make him a highly relevant candidate. Further probing of his experience with Java-related projects will be necessary to fully assess the fit.\n\n**Chary:**  This candidate's resume explicitly lists Java among his skills and showcases a wide range of relevant cloud technologies (AWS and Azure), along with experience building Microservices and using REST and SOAP APIs. However, the emphasis on .NET technologies in his resume suggests his Java experience may be less extensive.  More detailed questions are recommended to assess his true Java proficiency.\n\n**Important Visa/Location Considerations:**  Please note that both consultants' visa status and willingness to relocate will need further verification.  Both initially show an H1B visa, which is not acceptable under the client's requirements.  We must clarify their current immigration status and verify they are US Citizens and can obtain the necessary security clearance before proceeding.  Location is also a crucial factor: both consultants need to confirm their ability to work onsite in Washington, DC. \n\nWe recommend a detailed discussion with each candidate to confirm their experience and immigration status before moving forward.",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Donish",
          "match_confidence": "High",
          "skill_match_percentage": 80,
          "key_matching_skills": [
            "Microsoft .NET",
            ".NET Core",
            "C#",
            "SQL Server",
            "Azure",
            "Azure App Services",
            "Azure Pipelines",
            "Docker",
            "Kubernetes",
            "AWS",
            "Microservices"
          ],
          "match_reasoning": "Donish possesses extensive experience with .NET, .NET Core, C#, and SQL Server, aligning with the requirement of 8+ years of Java development experience.  His experience with Azure and AWS cloud services, along with microservices architecture and containerization (Docker, Kubernetes) strongly supports the job description."
        },
        {
          "name": "Chary",
          "match_confidence": "Medium",
          "skill_match_percentage": 75,
          "key_matching_skills": [
            "Java",
            "Microservices",
            "REST",
            "SOAP",
            "Azure",
            "Azure Functions",
            "Azure App Services",
            "SQL Server",
            "AWS",
            "Amazon DynamoDB",
            "Amazon EC2",
            "AWS Lambda"
          ],
          "match_reasoning": "While Chary's resume highlights a stronger .NET background, his experience with Java, Microservices, REST, SOAP APIs, and experience across both Azure and AWS cloud services makes him a reasonably strong candidate. The breadth of his skills is impressive, though his Java experience might need further clarification."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nThis email presents two candidates who are excellent technical matches for the Senior Java Developer position.  Both candidates have experience that aligns well with the requirements, though one is a stronger match than the other.\n\n**Donish:** This candidate demonstrates strong expertise in Microsoft .NET technologies (.NET, .NET Core, C#). While not directly Java, the experience level and familiarity with similar frameworks and cloud platforms (Azure, AWS), plus his exposure to microservices architecture and containerization technologies, make him a highly relevant candidate. Further probing of his experience with Java-related projects will be necessary to fully assess the fit.\n\n**Chary:**  This candidate's resume explicitly lists Java among his skills and showcases a wide range of relevant cloud technologies (AWS and Azure), along with experience building Microservices and using REST and SOAP APIs. However, the emphasis on .NET technologies in his resume suggests his Java experience may be less extensive.  More detailed questions are recommended to assess his true Java proficiency.\n\n**Important Visa/Location Considerations:**  Please note that both consultants' visa status and willingness to relocate will need further verification.  Both initially show an H1B visa, which is not acceptable under the client's requirements.  We must clarify their current immigration status and verify they are US Citizens and can obtain the necessary security clearance before proceeding.  Location is also a crucial factor: both consultants need to confirm their ability to work onsite in Washington, DC. \n\nWe recommend a detailed discussion with each candidate to confirm their experience and immigration status before moving forward.",
      "match_reasoning": "The selection prioritizes consultants with demonstrable experience in relevant technologies such as Azure/AWS, microservices, and strong backend development skills (Java or equivalent .NET expertise).  Given the client's strict US Citizen requirement, it is imperative to verify immigration statuses.",
      "total_matches": 2
    },
    "consultant_count": 39
  },
  "45b460275f2ff8644edef62db87567aa": {
    "timestamp": "2025-05-30T17:19:50.523433",
    "job_description": "\n            .NET Full Stack Developer for financial services company.\n            \n            Required Skills:\n            - 6+ years of .NET Core/.NET Framework experience\n            - Strong C# programming skills\n            - Experience with ASP.NET MVC and Web API\n            - SQL Server database experience\n            - Angular or React frontend experience\n            \n            VISA REQUIREMENTS:\n            - Green Card holders only\n            - No H1B or other work visas accepted\n            - Must be authorized to work permanently in US\n            \n            Location: New York, NY (Hybrid - 3 days in office)\n            Visa: Green Card holders only\n            Experience Level: Senior (6+ years)\n            ",
    "selected_consultants": [
      {
        "name": "Laxman",
        "skills": ".NET Full-stack",
        "experience": ".net",
        "visa_status": ".NET",
        "location": ".NET CORE",
        "relocation": "WEB API",
        "resume_path": "resumes\\Laxman_Gite.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          ".NET Core/.NET Framework",
          "C#",
          "ASP.NET MVC",
          "Web API",
          "SQL Server",
          "Angular"
        ],
        "match_reasoning": "Laxman's resume clearly demonstrates extensive experience with .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular.  His experience surpasses the 6+ years requirement and includes a wide array of relevant technologies.",
        "ai_selection_rank": 1
      },
      {
        "name": "Zeeshan",
        "skills": ".Net",
        "experience": ".net",
        "visa_status": "WPF",
        "location": "12",
        "relocation": "H1B",
        "resume_path": "resumes\\Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer.pdf",
        "ai_matched": true,
        "match_confidence": "Medium",
        "skill_match_percentage": 75,
        "key_matching_skills": [
          ".NET Core",
          "C#",
          "ASP.NET MVC",
          "Web API",
          "SQL Server",
          "Angular"
        ],
        "match_reasoning": "Zeeshan possesses solid experience in .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular. While his experience level isn't explicitly stated as exceeding 6 years, the resume suggests at least that amount. Further clarification on experience would be beneficial.",
        "ai_selection_rank": 2
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nFollowing a thorough review of the available candidates against the .NET Full Stack Developer job description, I have identified two consultants who stand out as excellent technical fits:\n\n**Consultant #1: Laxman**\nLaxman's resume showcases extensive experience (10+ years) exceeding the required 6+ years in .NET Core/.NET Framework,  C#, ASP.NET MVC, Web API, and SQL Server, along with strong Angular skills.  His skills align remarkably well with the job description's requirements, making him a top contender.\n\n**Consultant #2: Zeeshan**\nZeeshan also presents a strong skill set, including expertise in .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular.  While experience years are not explicitly stated on the resume, it implies at least 6 years of experience. We'd need to further clarify his total experience for a definite conclusion.\n\n**Visa and Location Considerations:**\nPlease note that only Laxman's visa status is ascertainable from the information provided, and is not clearly a Green Card. Zeeshan is explicitly listed as holding an H1B visa, which is not acceptable per the job description.  Both candidates' location information is unclear and needs to be confirmed.  The hybrid work arrangement (3 days in office) will also need to be verified as acceptable to both.\n\nI recommend contacting both candidates to verify experience levels, location suitability, and visa status. Laxman appears to be the stronger candidate given his extensive experience, provided his visa status is verified.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Laxman",
          "match_confidence": "High",
          "skill_match_percentage": 90,
          "key_matching_skills": [
            ".NET Core/.NET Framework",
            "C#",
            "ASP.NET MVC",
            "Web API",
            "SQL Server",
            "Angular"
          ],
          "match_reasoning": "Laxman's resume clearly demonstrates extensive experience with .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular.  His experience surpasses the 6+ years requirement and includes a wide array of relevant technologies."
        },
        {
          "name": "Zeeshan",
          "match_confidence": "Medium",
          "skill_match_percentage": 75,
          "key_matching_skills": [
            ".NET Core",
            "C#",
            "ASP.NET MVC",
            "Web API",
            "SQL Server",
            "Angular"
          ],
          "match_reasoning": "Zeeshan possesses solid experience in .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular. While his experience level isn't explicitly stated as exceeding 6 years, the resume suggests at least that amount. Further clarification on experience would be beneficial."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nFollowing a thorough review of the available candidates against the .NET Full Stack Developer job description, I have identified two consultants who stand out as excellent technical fits:\n\n**Consultant #1: Laxman**\nLaxman's resume showcases extensive experience (10+ years) exceeding the required 6+ years in .NET Core/.NET Framework,  C#, ASP.NET MVC, Web API, and SQL Server, along with strong Angular skills.  His skills align remarkably well with the job description's requirements, making him a top contender.\n\n**Consultant #2: Zeeshan**\nZeeshan also presents a strong skill set, including expertise in .NET Core, C#, ASP.NET MVC, Web API, SQL Server, and Angular.  While experience years are not explicitly stated on the resume, it implies at least 6 years of experience. We'd need to further clarify his total experience for a definite conclusion.\n\n**Visa and Location Considerations:**\nPlease note that only Laxman's visa status is ascertainable from the information provided, and is not clearly a Green Card. Zeeshan is explicitly listed as holding an H1B visa, which is not acceptable per the job description.  Both candidates' location information is unclear and needs to be confirmed.  The hybrid work arrangement (3 days in office) will also need to be verified as acceptable to both.\n\nI recommend contacting both candidates to verify experience levels, location suitability, and visa status. Laxman appears to be the stronger candidate given his extensive experience, provided his visa status is verified.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
      "match_reasoning": "The selection is based primarily on the candidates' demonstrated skills and experience in the core technologies required for the role, specifically focusing on .NET Framework/Core, C#, ASP.NET MVC/Web API, SQL Server, and Angular/React.  Experience level was also a key consideration. Visa status and location requirements were secondary factors, but are crucial for client awareness.",
      "total_matches": 2
    },
    "consultant_count": 39
  },
  "455e5af114097bf1d154f0ecd4d9d9d3": {
    "timestamp": "2025-05-30T17:19:59.965052",
    "job_description": "\n            DevOps Engineer for startup in Silicon Valley.\n            \n            Required Skills:\n            - 5+ years of DevOps experience\n            - Strong AWS experience\n            - Docker and Kubernetes expertise\n            - CI/CD pipeline development\n            - Infrastructure as Code (Terraform)\n            \n            LOCATION REQUIREMENTS:\n            - Must be current California resident\n            - No relocation assistance provided\n            - In-person collaboration required 4 days/week\n            - San Francisco Bay Area only\n            \n            Location: San Francisco, CA (On-site 4 days/week)\n            Visa: Any valid work authorization\n            Experience Level: Senior (5+ years)\n            ",
    "selected_consultants": [
      {
        "name": "Punniyakodi",
        "skills": ".net",
        "experience": "Sr.NET Full-stack",
        "visa_status": "17",
        "location": "H1B",
        "relocation": "PA",
        "resume_path": "resumes\\PunniyaKodi V updated resume.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          "AWS",
          "Terraform",
          "CI/CD",
          "Docker",
          "Kubernetes"
        ],
        "match_reasoning": "Punniyakodi possesses extensive experience with AWS, including services like EC2, CloudFront, IAM, ECS, SQS, SNS, Lambda, API Gateway, DynamoDB, RDS, and CloudWatch.  His resume also highlights expertise in Terraform for Infrastructure as Code, and a strong background in CI/CD pipeline development.  Furthermore, he demonstrates proficiency in Docker and Kubernetes.",
        "ai_selection_rank": 1
      },
      {
        "name": "Chary",
        "skills": ".net",
        "experience": "Senior .NET (.NET Architect)",
        "visa_status": "20",
        "location": "H1B",
        "relocation": "PA",
        "resume_path": "resumes\\Chary.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 85,
        "key_matching_skills": [
          "AWS",
          "Docker",
          "Kubernetes",
          "CI/CD Pipeline",
          "Azure"
        ],
        "match_reasoning": "Chary's resume showcases extensive experience with both AWS and Azure, including relevant services like Lambda, EC2, Azure Functions and App Services. He also possesses demonstrable expertise in Docker and Kubernetes, along with CI/CD pipeline development. His broad skillset makes him a strong contender, although his experience with Terraform is not explicitly mentioned.",
        "ai_selection_rank": 2
      }
    ],
    "email_message": "Dear Hiring Manager,\n\nFollowing a thorough review of the available consultant resumes against the requirements for the DevOps Engineer position, I have identified two candidates who are excellent technical fits:\n\n**Punniyakodi:** This consultant demonstrates a very strong match (90%) with the required skills.  His resume highlights extensive experience with AWS, encompassing numerous services directly relevant to the role. He also has proven expertise in Terraform, CI/CD, Docker, and Kubernetes. \n\n**Chary:** This consultant also presents a strong match (85%)  He has solid experience with both AWS and Azure, alongside demonstrated skills in Docker, Kubernetes and CI/CD pipelines. While Terraform is not explicitly called out, his overall skillset is highly relevant.\n\n**Visa and Location Considerations:** Please note that both Punniyakodi and Chary have indicated H1B visa statuses and are willing to relocate from Pennsylvania. Given the job posting\u2019s requirement for a California resident with no relocation assistance and on-site work in San Francisco four days a week,  this presents a significant potential hurdle.  We need to discuss how to proceed given these location and visa restrictions before moving forward with these candidates.  We may need to explore additional candidates who satisfy the location requirements.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
    "ai_response": {
      "selected_consultants": [
        {
          "name": "Punniyakodi",
          "match_confidence": "High",
          "skill_match_percentage": 90,
          "key_matching_skills": [
            "AWS",
            "Terraform",
            "CI/CD",
            "Docker",
            "Kubernetes"
          ],
          "match_reasoning": "Punniyakodi possesses extensive experience with AWS, including services like EC2, CloudFront, IAM, ECS, SQS, SNS, Lambda, API Gateway, DynamoDB, RDS, and CloudWatch.  His resume also highlights expertise in Terraform for Infrastructure as Code, and a strong background in CI/CD pipeline development.  Furthermore, he demonstrates proficiency in Docker and Kubernetes."
        },
        {
          "name": "Chary",
          "match_confidence": "High",
          "skill_match_percentage": 85,
          "key_matching_skills": [
            "AWS",
            "Docker",
            "Kubernetes",
            "CI/CD Pipeline",
            "Azure"
          ],
          "match_reasoning": "Chary's resume showcases extensive experience with both AWS and Azure, including relevant services like Lambda, EC2, Azure Functions and App Services. He also possesses demonstrable expertise in Docker and Kubernetes, along with CI/CD pipeline development. His broad skillset makes him a strong contender, although his experience with Terraform is not explicitly mentioned."
        }
      ],
      "email_message": "Dear Hiring Manager,\n\nFollowing a thorough review of the available consultant resumes against the requirements for the DevOps Engineer position, I have identified two candidates who are excellent technical fits:\n\n**Punniyakodi:** This consultant demonstrates a very strong match (90%) with the required skills.  His resume highlights extensive experience with AWS, encompassing numerous services directly relevant to the role. He also has proven expertise in Terraform, CI/CD, Docker, and Kubernetes. \n\n**Chary:** This consultant also presents a strong match (85%)  He has solid experience with both AWS and Azure, alongside demonstrated skills in Docker, Kubernetes and CI/CD pipelines. While Terraform is not explicitly called out, his overall skillset is highly relevant.\n\n**Visa and Location Considerations:** Please note that both Punniyakodi and Chary have indicated H1B visa statuses and are willing to relocate from Pennsylvania. Given the job posting\u2019s requirement for a California resident with no relocation assistance and on-site work in San Francisco four days a week,  this presents a significant potential hurdle.  We need to discuss how to proceed given these location and visa restrictions before moving forward with these candidates.  We may need to explore additional candidates who satisfy the location requirements.\n\nSincerely,\n[Your Name]\nExpert Technical Recruiter",
      "match_reasoning": "Both Punniyakodi and Chary display strong technical skills aligning with the job description's requirements for AWS expertise, Docker and Kubernetes experience, CI/CD pipeline development, and Infrastructure as Code (Terraform).  Punniyakodi's resume exhibits a more comprehensive match with the listed skills. However, both consultants should be considered with careful attention to the location and visa restrictions highlighted in the job description.",
      "total_matches": 2
    },
    "consultant_count": 39
  },
  "b4c6aa3c7647a7f992d05cda0f104843": {
    "timestamp": "2025-05-30T17:26:26.880106",
    "job_description": "Best Regards,\r\n\r\n\r\nMukesh Saini\r\n*Technical Recruiter *\r\nPh. No +1 \u202a(484) 202-0772\u202c\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.linkedin.com/in/mukeshsaini920/\r\n<https://www.linkedin.com/in/mukesh8920/>\r\n\r\n\r\n*Please email me if I miss your call \ud83d\ude0a*\r\n",
    "selected_consultants": [
      {
        "name": "Laxman",
        "skills": ".NET Full-stack, .net,.NET, .NET CORE, WEB API, ANGULAR, ASP.NET MVC",
        "experience": "10.0",
        "visa": "H1B",
        "visa_status": "H1B",
        "location": "NY",
        "relocation": "Remote",
        "availability": "Immediate",
        "rate": "Negotiable",
        "years_experience": 10,
        "skills_list": [
          ".NET Full-stack",
          ".net",
          ".NET",
          ".NET CORE",
          "WEB API",
          "ANGULAR",
          "ASP.NET MVC"
        ],
        "resume_path": "D:\\Coding\\Auto Resume - Copy - Copy\\backend\\resumes\\Laxman_Gite.pdf",
        "ai_matched": true,
        "match_confidence": "High",
        "skill_match_percentage": 90,
        "key_matching_skills": [
          "C#",
          ".NET 6",
          "ASP.NET Core",
          "Azure Developer",
          "Azure Functions",
          "Logic Apps",
          "Service Bus",
          "API Management",
          "Azure Storage",
          "Cosmos DB",
          "Redis Cache",
          "YAML Pipelines",
          "Azure AD",
          "Virtual Network",
          "Application Insights",
          "Log Analytics",
          "Key Vault",
          "Docker",
          "CI/CD",
          "Microservices",
          "Azure Monitor",
          "SQL Server",
          "Azure Container Registry",
          "Serverless Architecture",
          "Kubernetes"
        ],
        "match_reasoning": "Laxman possesses extensive experience and a broad skillset encompassing nearly all the technologies specified in the job description. His expertise in Azure services, .NET, and microservices architecture is particularly relevant.  His experience with CI/CD, Docker and Kubernetes further strengthens his candidacy.",
        "ai_selection_rank": 1,
        "multiple_matches": true,
        "all_matches": 