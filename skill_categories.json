{"Sudhakara Rao Illuri-Fusion Financial Cloud": {"programming_languages": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud Budgetary Control", "Oracle Cloud I-Expenses", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "Data Loader", "Hyperion FRS", "Sub Ledger Accounting (SLA)", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Business Process Management (BPM)", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials"], "databases": ["SQL"], "other": ["TOAD", "BIP", "AIM Methodology", "OUM Methodology", "Windows 2007/2008/2010", "UNIX"]}, "pavani_resume": {"programming_languages": ["Selenium RC", "Selenium WebDriver", "Selenium Grid", "<PERSON><PERSON><PERSON>", "JavaScript", "Python", "Java", "<PERSON>", "Oracle", "SQL Server", "Tortoise SVN", "HP Quality Center", "JIRA", "Waterfall"], "web_technologies": ["HTML"], "databases": ["SQL"], "devops_tools": ["<PERSON>"], "other": ["Selenium IDE", "TestNG", "QTP", "MS Access", "Toad", "<PERSON><PERSON>", "SoapUI", "Agile"]}, "Saisree Kondamareddy_ QA Consultant (1)": {"programming_languages": ["Selenium WebDriver", "SeeTest (Experitest)", "Java", "<PERSON><PERSON>", "Azure DevOps", "PostgreSQL", "BrowserStack", "Waterfall", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "Production Support", "Test Plan Creation", "Bug Reporting", "Defect Tracking", "AI-powered Automation", "Android Testing"], "cloud_platforms": ["LambdaTest"], "devops_tools": ["Git", "GitHub"], "mobile": ["iOS Testing"], "other": ["ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "HP ALM", "Agile", "Functional Testing", "Smoke Testing", "System Testing", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Unit Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Scheduling", "Test Management", "Mobile Web Testing", "Windows"]}, "Sowmya": {"programming_languages": ["Power BI", "SSRS", "Power BI Desktop", "Power BI Service", "Power Query", "Data Warehousing", "Star Schema", "Microsoft BI Stack", "SQL Server Data Tools", "Oracle", "Python", "Power Pivot", "Row-Level Security (RLS)", "Data Mart", "Azure Blob Storage", "Power Automate"], "web_technologies": ["HTML"], "databases": ["SQL", "T-SQL", "PL/SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["SSIS", "SSAS", "DAX", "M Language", "Dimensional Modeling", "ETL", "Snowf<PERSON>a", "SSMS", "Data Gateway", "Data Flows", "Talend", "Visual Studio Code"]}, "Varshika": {"programming_languages": ["SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "SAP Warehouse Management", "Business Process Mapping", "WRICEF Documentation", "Financial Reporting", "Hyperion Financial Management (HFM)", "Profit Center Accounting (PCBS)", "Material Master Data Management", "Procurement Processes", "Demand Forecasting", "Order-to-Delivery Process", "IFRS", "Automatic Payment Program (F110)", "Bank Reconciliation", "Real-time Cash Visibility System", "SAP Best Practices"], "other": ["SAP Financial Accounting (FI)", "ASAP Methodology", "Agile Methodology", "FIT-GAP Analysis", "SAP Solution Design", "Financial Data Management (FDMEE)", "SAP Cash Management (CM)", "SAP MM Functionalities", "GAAP", "Cash Pooling", "Inhouse Cash Management"]}, "Maanvi Resume (3)": {"programming_languages": ["Python", "Java", "PowerShell", "C++", "Android App Development", "Spring Boot", "Django", "Terraform", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", ".NET Core", "SQL Server", "Azure DevOps", "AWS Certified Solutions Architect - Associate"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["MySQL"], "frameworks": ["Flask"], "other": ["<PERSON><PERSON>", "C", "<PERSON><PERSON>", "JUnit", "Apache Kafka", "JSON", "Linux", "macOS", "Kali Linux", "Windows", "OAuth"]}, "SivakumarDega_CV": {"programming_languages": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "Perfecto", "REST Assured", "Karate Framework", "Azure DevOps", "JIRA", "<PERSON><PERSON><PERSON><PERSON>", "Quality Center", "Swagger", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "Data Warehouse Testing", "Interactive Voice Response (IVR) Testing", "Customer Telephony Integration (CTI) Testing", "Mainframes Testing", "Azure", "Microsoft Azure", "Android"], "cloud_platforms": ["AWS", "GCP"], "devops_tools": ["<PERSON>", "GitLab"], "mobile": ["iOS"], "other": ["<PERSON><PERSON>", "TestNG", "Appium", "SeeTest", "UFT", "LeanFT", "Bitbucket", "HP ALM", "Confluence", "Postman", "SOAP", "CICS", "JCL", "VSAM", "COBOL", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Agile", "OAuth", "SSO", "ETL", "Business Intelligence", "Database Testing", "C", "COBOL"]}, "Laxman_Gite": {"programming_languages": ["C#", "ASP.NET Core", "Azure Developer", "Azure Functions", "Service Bus", "Azure Storage", "<PERSON><PERSON>", "Azure AD", "Virtual Network", "<PERSON>er", "Microservices", "Angular", "Azure Monitor", "j<PERSON><PERSON><PERSON>", "Event Grid", "Azure Container Registry", "Serverless Architecture", "Microsoft Azure", "Amazon Web Services (AWS)", "Azure Service Fabric", "Kubernetes", "SQL Server", "Stored Procedures", "Triggers", "Microsoft Azure", "Amazon Web Services (AWS)", "C# Programming", "Query Performance Optimization", "Problem Solving", "Process Improvement", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Data & Analytics Architecture Modernization", "Hybrid Solution Architecture", "Direct Connect", "Enterprise Systems Integration", "Software Architecture Planning & Design", "Scalable Architecture Design", "High-Performance Architecture Design", "Micro-services Architecture Design", "Federated Database Design", "Container-based Architecture Design"], "web_technologies": ["HTML", "CSS"], "databases": ["MySQL", "T-SQL", "PL/SQL"], "devops_tools": ["CI/CD"], "frameworks": [".NET 6", "ASP.NET MVC"], "other": ["Logic App", "API Management", "Cosmos DB", "YAML Pipeline", "Web API", "Application Insights", "Log Analytics", "<PERSON>", "Event Hub", "Snowflake", "Functions", "Web Sites Design and Development", "Data Modeling", "VPC Design", "VPN", "Data Analytics"]}, "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": {"programming_languages": ["C#", "ASP.NET Core", "Winforms", "Azure", "Azure Functions", "Azure Blob Storage", "Azure Table Storage", "Azure App Service", "Azure Redis <PERSON>ache", "Azure Application Insights", "Azure API Management (APIM)", "Azure Service Bus", "Azure Logic Apps", "Entity Framework", "Lambda Expressions", "Caliburn.Micro", "Prism", "Angular", "Angular Reactive Forms", "XML Parser", "SQL Server", "SQL Server Reporting Services (SSRS)", "PostgreSQL", "Oracle", "Strapi CMS", "DevExpress", "MS SharePoint Server", "Windows Services", "WCF RESTful", "Observables", "AZ-900: Microsoft Azure Fundamentals", "Brainbench C# 5.0"], "web_technologies": ["HTML", "CSS"], "databases": ["T-SQL", "SQLite", "PL/SQL"], "devops_tools": ["Git", "GitHub"], "frameworks": ["ASP.NET MVC", "ADO.NET"], "other": ["Web API", "WPF", "WCF", "LINQ", "NUnit", "Coded UI Testing", "MS Access", "AJAX", "Stimulsoft", "InstallShield", "TFS", "SVN", "IIS", "Apache Tomcat", "MVVM", "HTTP"]}, "Vivek Anil .Net lead": {"programming_languages": ["ASP.NET Core", ".NET Framework", "C#", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "React", "Swagger", "Saga Pattern", "Circuit Breaker <PERSON>", "Event-Driven Architecture", "CQRS", "Backend for Frontend (BFF)", "SOLID Principles", "Design Patterns", "Team Foundation Server (TFS)", "<PERSON><PERSON>", "Azure DevOps", "Azure Service Bus", "Blue-Green Deployment", "Scrum", "Waterfall Methodology", "Test-Driven Development (TDD)", "C++", "Python"], "web_technologies": ["Node.js", "HTML"], "devops_tools": ["Git", "GitHub", "CI/CD"], "frameworks": ["ASP.NET", "ADO.NET"], "other": ["CosmosDB", "Apache Kafka", "ActiveMQ", "Web API", "OAuth 2.0", "Open API", "API Gateway", "OOPS", "SVN", "NUnit", "Moq", "IoT", "Agile Methodologies", "WCF"]}, "PunniyaKodi V updated resume": {"programming_languages": ["C#", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Windows Service", "j<PERSON><PERSON><PERSON>", "Angular", "AngularJS", "React", "ReactJS", "SQL Server", "XML Web Services", ".NET Core", "TypeScript", "Entity Framework", "AGGrid", "txText Control", "JavaScript", "Bootstrap", "OpenSearch", "PostgreSQL", "RESTful", "Amazon Web Services (AWS)", "CloudFront", "RDS", "ElasticSearch", "Crystal Reports", "Active Reports", "SSRS", "Azure DevOps", "Terraform", "SNS Event Producers", "SNS Event Consumers", "OpenTelemetry", "FullStory", "Google Analytics", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "Scrum"], "web_technologies": ["HTML", "HTML5", "CSS", "Sass"], "databases": ["SQL", "PL/SQL", "DynamoDB"], "cloud_platforms": ["EC2", "Lambda"], "frameworks": ["ASP.NET MVC", "ASP.NET", "VB.NET", "ADO.NET"], "other": ["Web API", "LINQ", "WCF", "AJAX", "NodeJS", "JSON", "SOAP", "IAM", "ECS", "SQS", "SNS", "API Gateway", "CloudWatch", "Step Functions", "El<PERSON>", "SSIS", "TFS", "YAML", "ASPX", "NuGet", "Pub/Sub", "Elastic APM", "Agile"]}, "Chary": {"programming_languages": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID Principles", "Web Services", "Microservices", "REST", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "<PERSON>er", "Kubernetes", "Azure Logic Apps", "RabbitMQ", "Azure App Services", "Azure Functions", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "Azure Entra ID", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Azure SQL Database", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "Omniture", "Google Analytics", "Google Tag Manager", "Object-Oriented Programming (OOP)", "Design Patterns", "Repository Pattern", "Dependency Inversion Principle", "Factory Pattern", "Abstract Factory Pattern", "CQRS", "Azure Cloud", "Cloud Design Patterns", "Azure Data Lake", "Azure Data Factory", "Python", "Amazon Web Services (AWS)", "Microsoft Azure", "<PERSON><PERSON> (Project Management Professional)", "Agile (Scrum)", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "Internet of Things (IoT)", "Container Storage", "Azure Storage (Tables, Queues, Blobs)"], "databases": ["Amazon DynamoDB"], "cloud_platforms": ["Amazon EC2", "AWS Lambda"], "devops_tools": ["CI/CD Pipeline"], "frameworks": ["ASP.NET MVC", "ASP.NET", ".NET MAUI"], "other": ["XAML", "WCF", "Web API", "SOAP", "<PERSON><PERSON><PERSON><PERSON>", "Kendo UI", "Web Jobs", "OAuth 2.0", "OKTA", "Bitbucket", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "SSIS", "Data Modeling", "SEO Optimization", "MVC", "Dependency Injection", "Selenium", "MuleSoft", "Kafka", "Tibco", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304"]}, "Donish Devasahayam_DotNET": {"programming_languages": ["C#", "Python", ".NET Core", "gRPC", "Angular", "Azure", "SQL Server", "Entity Framework", "Lambda Expressions", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "Microservices", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "Amazon ECR", "Application Load Balancer", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Azure SQL", ".NET Core Web API", "Blazor", "MudBlazor", "Telerik", "React", "Redux", "Hangfire", "Blue Yonder"], "databases": ["LINQ to SQL", "NoSQL"], "cloud_platforms": ["Amazon S3"], "frameworks": [".NET", "ADO.NET"], "data_analytics": ["<PERSON><PERSON>"], "other": ["LINQ", "LINQ to Objects", "DB2", "Amazon Elastic Beanstalk", "Amazon EKS", "Datadog", "Cosmos DB", "Kafka", "Kendo UI", "ADFS", "SAP", "IDoc", "Logility"]}, "Kondaru_04_Manjunath_Resume": {"programming_languages": ["Amazon Web Services (AWS)", "CloudFormation", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "Shell Scripting", "<PERSON>er", "PowerShell", "JIRA", "WebSphere", "Windows Server", "Red Hat Linux", "VMware", "Waterfall", "<PERSON><PERSON>"], "devops_tools": ["<PERSON>", "Git", "Ansible", "GitHub"], "other": ["VPC", "IAM", "ANT", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Unix", "CentOS", "Agile"]}, "Jhansi P": {"programming_languages": ["Amazon Web Services (AWS)", "Azure", "Amazon RDS", "Amazon Route 53", "Amazon CloudFormation", "Amazon CloudFront", "Pivotal Cloud Foundry (PCF)", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "Groovy", "Subversion (SVN)", "Elasticsearch", "<PERSON><PERSON>", "Python", "Java", "Infrastructure as Code (IaC)", "Configuration Management", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)"], "databases": ["Amazon DynamoDB", "MySQL"], "cloud_platforms": ["Amazon EC2", "Amazon S3", "Amazon Lambda", "AWS CLI", "AWS-Kops (EKS)"], "devops_tools": ["Ansible", "<PERSON>", "Git", "GitHub", "GitLab", "CI/CD"], "other": ["Amazon ECS", "Elastic Beanstalk", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon IAM", "Amazon Auto Scaling", "Amazon CloudWatch", "<PERSON><PERSON>", "Ant", "<PERSON><PERSON>", "Tomcat", "WebLogic", "<PERSON><PERSON><PERSON><PERSON>", "HTTP", "TLS"]}, "Puneet": {"programming_languages": ["Java", "Scrum", "<PERSON><PERSON>", "Microsoft Project", "SmartSheet", "SonarQube", "Warehouse Management"], "devops_tools": ["<PERSON>", "CI/CD"], "other": ["J2EE", "Agile", "SAFe", "Ka<PERSON><PERSON>", "Confluence", "DevOps", "PMP", "PSM", "SAP", "CMMI Level 5"]}, "Pradeep_Project_manager_Nithin1": {"programming_languages": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Resource Management", "Stakeholder Management", "Release Management", "Delivery Management", "Strategic Planning", "<PERSON><PERSON>", "Azure DevOps", "ServiceNow", "Microsoft Excel", "Angular", "Azure Cloud", "EZTrieve", "C#"], "web_technologies": ["Node.js"], "databases": ["SQL"], "frameworks": [".NET"], "other": ["Cost Analysis", "Client Management", "Confluence", "Cobol", "IBM BMP", "CMMI Level 5", "ISO 27001"]}, "Kamal": {"programming_languages": ["Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Fivetran", "Streamset", "Snowpark", "Python", "Stored Procedures", "Data Encryption", "Data Decryption", "Data Governance", "Real-time Data Ingestion", "PySpark", "Apache Airflow", "Informatica Power Center", "Account Receivables", "<PERSON> (GL)", "Oracle", "MS SQL Server", "Query Plan Optimization", "Certified Professional Data Engineer"], "databases": ["Snow SQL", "SQL"], "cloud_platforms": ["AWS", "Amazon S3", "AWS Glue", "EC2"], "devops_tools": ["GitHub"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Snowflake", "DBT", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "Column <PERSON>", "Data Masking", "Data Quality", "Data Validation", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "Kafka", "Sigma", "Talend", "PeopleSoft Financials", "PeopleSoft Supply Chain Management", "Account Payables", "Billing", "JSON", "XML", "DB2", "OLTP", "OLAP", "Dimension Modeling", "Fact Tables", "Index Design"]}, "Aiswarya Sukumaran Data analyst": {"programming_languages": ["ETL Processes", "Python", "Power Query", "PostgreSQL", "Data Warehousing", "Power BI", "Regression", "Predictive Modeling", "Time Series Forecasting", "Data Transformation", "R", "SQL Server Integration Services (SSIS)", "<PERSON><PERSON>", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level"], "databases": ["SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>", "pandas", "NumPy"], "other": ["Data Analysis", "Business Intelligence", "Data Management", "Data Modeling", "Excel", "PivotTables", "DAX", "Statistical Analysis", "Hypothesis Testing", "Classification", "Data Cleaning", "Data Automation"]}, "Himanshu": {"programming_languages": ["Data Warehousing", "Cloud Migration", "Data Modernization", "Data Enrichment", "Data Integration", "Data Processing", "Data Transformation", "Enterprise Reporting", "Dashboards", "Amazon Web Services (AWS)", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Python", "Oracle PL/SQL", "Informatica PowerCenter", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8i", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Triggers", "RDBMS", "Data Mart", "Star Schema", "Normalization", "Microsoft Power BI", "SAS Visual Investigator", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "Erro<PERSON>", "Load Strategizing", "Performance Tuning", "Integration Testing", "System Integration Testing", "User Acceptance Testing"], "databases": ["SQL"], "cloud_platforms": ["AWS S3", "Amazon EC2", "AWS Glue", "AWS Lambda", "AWS Athena"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Advanced Analytics", "Data Pipelining", "Data Quality", "Data Validation", "Data Intelligence", "ETL Design", "ETL Development", "Data Modeling", "MIS Management", "Functions", "OLAP", "OLTP", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "OBIEE", "SAS Visual Analytics", "Neb<PERSON>", "Snowflake", "Flat Files", "CSV", "JSON", "XML", "Data Cleansing", "Code Optimization", "Unit Testing"]}, "DA manager Nithin": {"programming_languages": ["Python", "Power BI", "Data Security", "Data Warehousing", "Data Wrangling", "Risk Management", "Program Management", "Project Planning", "Azure Cloud", "JIRA"], "databases": ["SQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["MS Excel", "Data Analysis", "Data Visualization", "Data Modeling", "Cost Analysis", "ETL", "Visual Studio", "Agile"]}, "Raghu": {"programming_languages": ["Mechanical Product Design", "System Integration", "Machined Parts Design", "Design Standardization", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tools Design", "Configuration Management", "Process Management", "SolidWorks", "2D Drawings Review", "CE Marking", "Machinery Directive 2006/42/EC", "Reverse Engineering"], "other": ["Sheet Metal Design", "Component Localization", "Cost Optimization", "Design Calculations", "UG-NX", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack up analysis", "ASME Y14.5", "MathCAD", "DNVGL", "EN-13155", "EN ISO 50308", "EN ISO 14122"]}, "Karnati": {"programming_languages": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Oracle", "Terada<PERSON>", "Python", "Databricks", "Spark", "Shell Scripting", "Data Warehousing", "Data Integration", "Data Profiling", "Cloud Data Integration", "Cloud Application Integration", "Cloud Data Governance", "Data Modeling (<PERSON> Schema, <PERSON><PERSON><PERSON>)", "Data Capture (CDC)", "API Development (ICS, ICRT)", "Data Migration", "Production Support", "Software Development Life Cycle (SDLC)", "Technical Architecture Documentation"], "databases": ["SQL"], "cloud_platforms": ["AWS"], "other": ["Intelligent Data Management Cloud (IDMC)", "DB2", "Netezza", "Snowflake", "Hive", "Unix", "Windows", "ETL", "Data Quality", "Business 360 Console", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "JSON", "Big Data", "<PERSON><PERSON><PERSON>", "Technical Design Documentation"]}, "Shashindra": {"programming_languages": ["React", "ReactJS", "Redux Toolkit", "SWR", "<PERSON><PERSON>", "React Router", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "PHP", "Amazon Web Services (AWS)", "Azure", "REST", "ServiceNow", "<PERSON><PERSON><PERSON>", "Scrum", "RBAC"], "web_technologies": ["Webpack", "HTML5", "CSS3", "Tailwind CSS"], "databases": ["MySQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2", "Amazon Lambda"], "devops_tools": ["GitHub", "GitHub Copilot"], "mobile": ["A<PERSON>os"], "other": ["ES6", "NodeJS", "SOAP", "JSON", "Gulp", "SVN", "Agile", "JWT"]}, "Upendra": {"programming_languages": ["Java", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "Servlets", "JAX-RS", "Java Mail", "Angular", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "MongoDB", "Quartz", "Hibernate", "Spring JPA", "WebSphere", "RDBMS", "AWS Aurora Postgres"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["SQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2"], "devops_tools": ["Git", "<PERSON>"], "other": ["J2EE", "JSF", "Apache POI", "iText", "JSP", "JDBC", "JAX-WS", "JMS", "JUnits", "Ant", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon EKS", "AJAX", "<PERSON><PERSON>", "SVN", "Bitbucket", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Putty", "WinSCP", "Bamboo"]}, "Chandra_Resume": {"programming_languages": ["Java", "JavaScript", "Python", "Servlets", "REST", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "Cypress", "Resin", "Oracle 11g/12c", "PostgreSQL", "MongoDB", "JDeveloper", "ERwin Data Modeler", "JMS Hermes", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "TypeScript", "Dynatrace", "NX Monorepo", "SiteMinder", "Harvest", "<PERSON><PERSON>"], "web_technologies": ["Node.js"], "databases": ["SQL", "MySQL", "DynamoDB"], "cloud_platforms": ["AWS CDK"], "devops_tools": ["Git", "GitLab", "<PERSON>"], "other": ["JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "JPA", "AJAX", "NestJS", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "Glassfish", "IBM DB2", "Eclipse", "NetBeans", "IntelliJ", "MyEclipse", "VS Code", "Toad", "Visio", "UML", "CVS", "SVN", "SoapUI", "JUnit", "Log4j", "Ant", "<PERSON><PERSON>", "Agile", "ITIL Foundation", "LDAP", "SAML", "Bitbucket", "OOAD", "SOA", "@task", "TFS", "Single Page Application (SPA)"]}, "KRISHNA_KANT_NIRALA_Oracle_DBA": {"programming_languages": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 19c Data Guard", "Oracle 19c RAC", "Oracle 21c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle Enterprise Manager 12c", "Oracle Grid Control 11g", "Oracle TDE", "Oracle Cloud Infrastructure", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "RMAN", "Linux Shell Scripting", "AWR", "SQL*Trace", "TKPROF", "SQL Server 2016", "Red Hat Linux 8.x", "Red Hat Linux 7.x", "Windows Server 2016", "V<PERSON>am Backup and Recovery", "OCI Object Storage", "HP/IBM Power E980", "IBM Power E850", "PRINCE2", "OCA - Oracle Database Administrator Certified", "OCI - Oracle Cloud Infrastructure Foundations Associate", "Teradata Certified Administrator (V2R5)", "Data Guard Physical Standby", "Data Guard Snapshot Standby", "Export/Import", "Transportable Tablespaces", "Crontab"], "databases": ["SQL", "PL/SQL"], "other": ["Data Pump", "ADDM", "EXPLAIN PLAN", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "Glassfish", "JDK", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "<PERSON>adata", "Exadata CS X9M-2", "Exadata CS DB X7", "ITIL V3 Foundation", "ISO/IEC 27001", "ISO 20000"]}, "Akhila D": {"programming_languages": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Java", "JavaScript", "REST", "Scrum", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "Ruleset locking mechanisms", "Waterfall", "E-Commerce", "Insurance", "Queue Processors", "Decision Rules", "Declarative Rules", "Process Flows", "Screen Flows", "Data Transforms", "Rule Resolution", "Enterprise Class Structure", "Code review", "Document review", "WebSphere", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)"], "web_technologies": ["CSS", "HTML"], "other": ["Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "SOAP", "Agile methodology", "Unit testing", "Sections", "Flow Actions", "List-View", "PLA", "SDLC", "Agents", "Application Design", "Case Management", "Data Modeling", "Activities", "Dev Studio", "App Studio", "Admin Studio", "CDH", "XML", "Postman"]}, "Uday": {"programming_languages": ["<PERSON><PERSON>", "Fiori Elements", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "JavaScript", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BP Integrations", "User Exits", "Customer Exits", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "SAP Fiori List Report Application", "SAP Fiori Launchpad", "JIRA", "SAP Security", "PFTC Roles", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "SAP Scripts", "Smart Forms", "Adobe Forms", "ALV Reports", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "FI-AR", "RTR", "Product Life Cycle Management (PLM)"], "web_technologies": ["HTML5"], "devops_tools": ["GitHub"], "other": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "PI/PO", "AIF", "BTP", "CAPM", "XML", "JSON", "BADIs", "BDC", "BAPI", "Enhancement Points", "ALE IDOCs", "CDS Views", "AMDP", "Web IDE", "BSP", "Business Objects (BO)", "ATC", "SPDD", "SPAU", "Quality Management (QM)", "FI-AP", "FI-GL (FICO)", "SCM"]}, "Updated_CV_-_Tauqeer_Ahmad1_1": {"programming_languages": ["TypeScript", "React", "Angular", "REST APIs", "GraphQL APIs", "Apache ECharts", "MySQL Aurora", "Serverless Architecture", "Microservices Architecture", "Salesforce"], "web_technologies": ["Node.js"], "cloud_platforms": ["AWS SAM", "AWS CDK", "AWS Lambda", "AWS API Gateway"], "devops_tools": ["CI/CD"], "other": ["Next.js", "NestJS", "Cognito", "Okta", "OIDC", "Mantine UI", "Vite", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "CDL"]}, "Ajeesh_resume": {"programming_languages": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Talwar Simulator", "Ethernet", "Swift", "ClearCase", "ios-xe asr 1K router", "C++", "OpenWRT", "Qualcomm SDX hardware", "AT interfaces", "Shell Scripting", "POLARIS", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure"], "devops_tools": ["GIT"], "other": ["Cisco Access Points", "WiFi", "802.11", "WLAN", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "SVN", "Cisco catalyst 3750 Switch", "C", "Linux", "QMI", "XML", "GDB", "LTE", "5G", "Ubus"]}, "Vidwaan_vidwan_resume": {"programming_languages": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "ReactJS", "Spark SQL", "PostgreSQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "Amazon Web Services (AWS)", "Load Balancers", "AWS Firehose", "Test Driven Development (TDD)", "Kubernetes", "<PERSON>er", "Design Patterns", "Data Structures", "Machine Learning", "IAM Role Management", "Server-Side Encryption", "BottleRocket"], "web_technologies": ["HTML", "CSS"], "databases": ["SQL", "MySQL", "DynamoDB"], "cloud_platforms": ["AWS CDK", "EC2", "S3", "AWS Glue", "AWS Athena", "AWS Lambda", "AWS CloudWatch"], "devops_tools": ["<PERSON>", "CI/CD", "Git"], "other": ["DNS Delegation", "VPCs", "SNS", "SQS", "Step Functions", "JUnit", "<PERSON><PERSON><PERSON>", "EKS", "Agile", "Postman"]}, "Soham_Resume_Java": {"programming_languages": ["Java", "Spring Boot", "Hibernate", "Python", "JavaScript", "TypeScript", "React.js", "Angular", "<PERSON><PERSON><PERSON><PERSON>", "Bootstrap", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Azure", "Google Cloud Platform (GCP)", "JIRA", "<PERSON>er", "Microservices Architecture", "REST APIs", "Power BI", "Android Studio", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "web_technologies": ["HTML5", "CSS3"], "databases": ["SQL", "MySQL"], "devops_tools": ["Git", "<PERSON>", "CI/CD"], "data_analytics": ["<PERSON><PERSON>"], "other": ["JUnit", "<PERSON><PERSON><PERSON>", "Apache Kafka", "SOAP", "JSON", "Bluetooth"]}}