{"Sudhakara Rao Illuri-Fusion Financial Cloud": {"programming_languages": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Oracle Financials Cloud Receivables", "General <PERSON><PERSON>", "Accounts Receivable", "Order Management", "Oracle Financial Accounting Hub", "Business Intelligence Publisher (BIP)", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "Data Loader", "Hyperion FRS", "Sub Ledger Accounting (SLA)", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Business Process Management (BPM)", "Oracle Allocations", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Oracle Cloud Budgetary Control", "I-Receivables", "Intercompany"], "databases": ["SQL"], "other": ["Accounts Payable", "Fixed Assets", "Cash Management", "I-Expenses", "TOAD", "AIM Methodology", "OUM Methodology", "Windows 2007/2008/2010", "UNIX"]}, "pavani_resume": {"programming_languages": ["Selenium RC", "Selenium WebDriver", "Selenium Grid", "<PERSON><PERSON><PERSON>", "JavaScript", "Python", "Java", "<PERSON>", "Oracle", "SQL Server", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "Waterfall"], "web_technologies": ["HTML"], "databases": ["SQL"], "devops_tools": ["<PERSON>"], "other": ["Selenium IDE", "TestNG", "QTP", "MS Access", "Toad", "<PERSON><PERSON>", "SoapUI", "Agile"]}, "Saisree Kondamareddy_ QA Consultant (1)": {"programming_languages": ["Selenium WebDriver", "SeeTest/Experitest", "Java", "<PERSON><PERSON>", "Azure DevOps", "PostgreSQL", "BrowserStack", "Waterfall", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "Test Plan Creation", "Test Schedule Creation", "Bug Reporting", "Defect Tracking", "Production Support", "AI-Powered Automation", "Android Testing"], "cloud_platforms": ["LambdaTest"], "devops_tools": ["Git", "GitHub"], "mobile": ["iOS Testing"], "other": ["ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "HP ALM", "Agile", "Functional Testing", "Smoke Testing", "System Testing", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Unit Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Management", "Mobile Web Testing", "Windows"]}, "Sowmya": {"programming_languages": ["Power BI", "SSRS", "Power BI Desktop", "Power BI Service", "Power Query", "Data Warehousing", "Star Schema", "Microsoft BI Stack", "Power Pivot", "Row-Level Security (RLS)", "DataMart", "Python", "Azure Blob Storage", "Power Automate", "Oracle", "SQL Server Data Tools", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Stored Procedures", "Triggers", "Database Performance Tuning", "Query Optimization", "Database Partitioning"], "web_technologies": ["HTML"], "databases": ["SQL", "T-SQL", "PL/SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["SSIS", "SSAS", "DAX", "M Language", "ETL", "Dimensional Modeling", "Snowf<PERSON>a", "Data Gateway", "Data Flows", "Talend", "Visual Studio Code", "SSMS", "Excel", "Functions", "Indexing"]}, "Varshika": {"programming_languages": ["SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "SAP Warehouse Management", "Business Process Mapping", "WRICEF Documentation", "Financial Reporting", "Hyperion Financial Management (HFM)", "PCBS (Profit Center Budgeting System)", "Material Master Data Management", "Procurement Processes", "Demand Forecasting", "Order-to-Delivery Process Optimization", "GAAP (Generally Accepted Accounting Principles)", "IFRS (International Financial Reporting Standards)", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "SAP Treasury Modules", "Automated data entry", "AR Processing & Reporting", "FI-GL Transactions", "AP/AR Transactions", "Vendor/Customer Open Item Management", "BPPs (Business Process Procedures) Documentation", "Inventory Planning", "Pricing Strategies", "Product Assortment Management", "SAP Best Practices"], "other": ["SAP Financial Accounting (FI)", "ASAP Methodology", "Agile Methodologies", "FIT-GAP Analysis", "SAP Solution Design", "Financial Data Management (FDMEE)", "SAP Cash Management (CM)", "SAP MM Functionalities", "Financial Analysis", "Inhouse Cash Management", "Cash Pooling", "Cash Discount Management", "Dispute and Deduction Management"]}, "Maanvi Resume (3)": {"programming_languages": ["Python", "Java", "PowerShell", "C++", "Android App Development", "Spring Boot", "Django", "Terraform", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "SQL Server", ".NET Core", "Azure DevOps", "AWS Certified Solutions Architect - Associate"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["MySQL"], "frameworks": ["Flask"], "other": ["<PERSON><PERSON>", "C", "<PERSON><PERSON>", "JUnit", "Apache Kafka", "JSON", "Linux", "macOS", "Kali Linux", "Windows", "OAuth"]}, "SivakumarDega_CV": {"programming_languages": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "Perfecto", "REST Assured", "Karate Framework", "Azure DevOps", "JIRA", "<PERSON><PERSON><PERSON><PERSON>", "Quality Center", "Swagger", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "Regression Testing", "Integration Testing", "User Interface Testing", "Browser Compatibility Testing", "Exploratory Testing", "Risk-based Testing", "User Acceptance Testing", "Test Plan Creation", "Test Strategy Creation", "Requirements Mapping", "Test Coverage", "Web Services Testing", "Microservices Testing", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "Data Warehouse Testing", "Interactive Voice Response (IVR) Testing", "Customer Telephony Integration (CTI) Testing", "Mainframe Testing"], "devops_tools": ["<PERSON>", "GitLab"], "other": ["<PERSON><PERSON>", "TestNG", "Appium", "SeeTest", "UFT", "LeanFT", "Bitbucket", "HP ALM", "Confluence", "Postman", "SOAP", "CICS", "JCL", "VSAM", "COBOL", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Agile", "Functional Testing", "System Testing", "End-to-End Testing", "Test Design", "Test Execution", "Test Estimation", "API Testing", "Database Testing", "Mobile Testing", "UI Testing", "ETL Testing", "OAuth Testing", "SSO Testing", "Business Intelligence"]}, "Laxman_Gite": {"programming_languages": ["C#", "ASP.NET Core", "Azure Developer", "Azure Functions", "Service Bus", "Azure Storage", "<PERSON><PERSON>", "Azure AD", "Virtual Network", "<PERSON>er", "Microservices", "Angular", "Azure Monitor", "j<PERSON><PERSON><PERSON>", "Event Grid", "Azure Container Registry", "Serverless Architecture", "Microsoft Azure", "Amazon Web Services (AWS)", "SQL Server", "Azure Data Lake", "Stored Procedures", "Triggers", "Azure Service Fabric", "Kubernetes", "Microsoft technologies", "Software architecture", "Micro-services", "Federated database design", "Container-based architecture", "High throughput system architecture", "Real-time data analytics solution architecture", "E-commerce architecture", "Query performance optimization", "Hybrid solution architecture", "Direct Connect"], "web_technologies": ["HTML", "CSS"], "databases": ["MySQL", "T-SQL", "PL/SQL"], "devops_tools": ["CI/CD"], "frameworks": [".NET 6", "ASP.NET MVC"], "other": ["Logic Apps", "API Management", "Cosmos DB", "YAML Pipelines", "Web API", "Application Insights", "Log Analytics", "<PERSON>", "Event Hub", "Snowflake", "Functions", "Agile methodology", "Data modeling", "VPN", "Data analytics"]}, "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": {"programming_languages": ["C#", "ASP.NET Core", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "Azure APIM", "Azure Service Bus", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "Angular", "Angular Reactive Forms", "SQL Server", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTFUL", "MS SQL Server 2019", "PostgreSQL", "Oracle", "DevExpress", "Brainbench C# 5.0"], "databases": ["T-SQL", "SQLite", "PL/SQL"], "devops_tools": ["GitHub"], "frameworks": ["ADO.NET"], "other": ["Web API", "WPF", "MVC", "WCF", "App Insights", "Logic Apps", "LINQ", "Stimulsoft", "HttpClient", "NUnit", "Coded UI testing", "MS Access", "InstallShield", "TFS", "SVN", "IIS", "Apache Tomcat"]}, "Vivek Anil .Net lead": {"programming_languages": ["ASP.NET Core", ".NET Framework", "C#", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "React", "Swagger", "Saga Pattern", "Circuit Breaker <PERSON>", "Event-Driven Architecture", "CQRS", "Backend for Frontend (BFF)", "Object-Oriented Programming (OOP)", "SOLID Principles", "Design Patterns", "Team Foundation Server (TFS)", "<PERSON><PERSON>", "Azure DevOps", "Pivotal Cloud Foundry (PCF)", "Azure Service Bus", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "C++", "Python"], "web_technologies": ["Node.js", "HTML"], "devops_tools": ["Git", "GitHub", "CI/CD"], "frameworks": ["ASP.NET", "ADO.NET"], "other": ["CosmosDB", "Apache Kafka", "ActiveMQ", "Single Page Application (SPA)", "Web API", "OAuth 2.0", "Open API", "API Gateway", "SVN", "NUnit", "Moq", "Agile Methodologies", "WCF"]}, "PunniyaKodi V updated resume": {"programming_languages": ["C#", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Windows Services", "Entity Framework", "j<PERSON><PERSON><PERSON>", "AngularJS", "Angular", "React", "TypeScript", "JavaScript", "Bootstrap", "SQL Server", "SQL Server 2016", "PostgreSQL", "OpenSearch", "Amazon Web Services (AWS)", "CloudFront", "RDS", "Elasticsearch", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "Azure DevOps", "Terraform", "Domain Driven Development (DDD)", "Test-Driven Development (TDD)", "Scrum", "Object-Oriented Programming (OOP)", "N-tier Applications", "Client-Server Applications", "XML Web Services", "AGGrid", "txText Control", "OpenTelemetry", "FullStory", "Google Analytics"], "web_technologies": ["HTML", "HTML5", "CSS", "Sass"], "databases": ["SQL", "PL/SQL", "T-SQL", "DynamoDB"], "cloud_platforms": ["EC2", "Lambda"], "frameworks": ["ASP.NET MVC", "ASP.NET", "ADO.NET", ".NET", "VB.NET"], "other": ["ASPX", "Web API", "LINQ", "WCF", "AJAX", "JSON", "XML", "IAM", "ECS", "SQS", "SNS", "API Gateway", "CloudWatch", "Step Functions", "El<PERSON>", "SOAP", "SSIS", "TFS", "YAML", "NuGet", "Agile", "NodeJS", "Elastic APM"]}, "Chary": {"programming_languages": ["ASP.NET Core", "C#", "Java", "SOLID principles", "Web Services", "Microservices", "REST", "Angular", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework", "Azure DevOps", "<PERSON>er", "Kubernetes", "Azure Logic Apps", "RabbitMQ", "Azure App Services", "Azure Functions", "Azure Active Directory", "ServiceNow", "Azure Entra ID", "Team Foundation Server", "Subversion", "TortoiseSVN", "CQRS", "Choreography", "Gateway Aggregation", "Circuit Breaker", "MVC design pattern", "Repository pattern", "Dependency Inversion principle", "Factory pattern", "Abstract Factory pattern", "Tridion CMS", "Sitecore", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server", "Azure SQL Server", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Object-Oriented Programming", "Design Patterns", "Python", "Azure Data Lake", "Azure Data Factory"], "databases": ["Amazon DynamoDB"], "cloud_platforms": ["Amazon EC2", "AWS Lambda"], "devops_tools": ["CI/CD Pipeline"], "frameworks": ["ASP.NET MVC", "ASP.NET", ".NET MAUI"], "other": ["XAML", "WCF", "Web API", "SOAP", "<PERSON><PERSON><PERSON><PERSON>", "Kendo UI", "Web Jobs", "HPSM", "SOA", "OAuth 2.0", "OKTA", "Bitbucket", "Visual Studio", "Saga", "API Gateway", "MuleSoft", "Kafka", "Tibco", "AKS", "Dependency Injection", "SEO optimization", "SSIS", "Data Modeling", "Selenium", "PMP"]}, "Donish Devasahayam_DotNET": {"programming_languages": [".NET Core", "C#", "Python", "Angular", "SQL Server", "Azure", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Azure SQL", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure Pipelines", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "Amazon Elastic Container Registry (ECR)", "Amazon Elastic Kubernetes Service (EKS)", "Application Load Balancer", "Blazor", "MudBlazor", "Telerik", "React", "Redux", "Hangfire", "gRPC", "Entity Framework", "Lambda Expressions", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "<PERSON><PERSON>", "Rally"], "databases": ["NoSQL", "LINQ to SQL"], "cloud_platforms": ["Amazon S3"], "frameworks": [".NET", "ADO.NET"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Cosmos DB", "Elastic Beanstalk", "Datadog", "Kendo UI", "ADFS", "LINQ", "LINQ to Objects", "DB2", "Kafka", "IDoc", "SAP"]}, "Kondaru_04_Manjunath_Resume": {"programming_languages": ["Amazon Web Services (AWS)", "CloudFormation", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "Shell Scripting", "<PERSON>er", "PowerShell", "<PERSON><PERSON>", "WebSphere", "Windows Server", "Red Hat Linux", "VMware", "<PERSON><PERSON>", "Elastic Load Balancers", "Active Directory", "Waterfall"], "cloud_platforms": ["EC2"], "devops_tools": ["<PERSON>", "Git", "Ansible", "GitHub"], "other": ["VPC", "IAM", "ANT", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Unix", "CentOS", "Agile"]}, "Jhansi P": {"programming_languages": ["Amazon Web Services (AWS)", "Azure", "Amazon RDS", "Amazon Route 53", "Amazon CloudFormation", "Amazon CloudFront", "Python", "Java", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "Groovy", "Subversion (SVN)", "Elasticsearch", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Continuous Integration/Continuous Deployment (CI/CD)", "Configuration Management", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)"], "databases": ["Amazon DynamoDB", "MySQL"], "cloud_platforms": ["Amazon EC2", "Amazon S3", "Amazon Lambda", "AWS CLI", "A<PERSON> (EKS)"], "devops_tools": ["Ansible", "<PERSON>", "Git", "GitHub", "GitLab"], "other": ["Amazon ECS", "Elastic Beanstalk", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon IAM", "Amazon Auto Scaling", "Amazon CloudWatch", "<PERSON><PERSON>", "Ant", "<PERSON><PERSON>", "Tomcat", "WebLogic", "Apache", "<PERSON><PERSON><PERSON><PERSON>", "HTTP", "TLS"]}, "Puneet": {"programming_languages": ["Java", "Scrum", "<PERSON><PERSON>", "Microsoft Project", "SmartSheet", "SonarQube", "Warehouse Management"], "devops_tools": ["<PERSON>", "CI/CD"], "other": ["J2EE", "Agile", "SAFe", "Ka<PERSON><PERSON>", "Confluence", "DevOps", "PMP", "PSM", "SAP", "CMMI Level 5"]}, "Pradeep_Project_manager_Nithin1": {"programming_languages": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Resource Management", "Stakeholder Management", "Release Management", "Delivery Management", "Strategic Planning", "<PERSON><PERSON>", "Azure DevOps", "ServiceNow", "Angular", "Azure Cloud", "Microsoft Excel", "Ezetrieves", "C#"], "web_technologies": ["Node.js"], "databases": ["SQL"], "frameworks": [".NET"], "other": ["Cost Analysis", "Client Management", "Confluence", "Cobol", "IBM BMP", "CMMI Level 5", "ISO 27001"]}, "Kamal": {"programming_languages": ["Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Fivetran", "Streamset", "Snowpark", "Python", "Stored Procedures", "Data Encryption", "Data Decryption", "Data Governance", "PySpark", "Apache Airflow", "Informatica PowerCenter", "Oracle", "MS SQL Server", "Data Warehousing", "Data Integration", "Data Migration", "Real-time Data Ingestion", "SQR 6.0", "Query Plan Optimization"], "databases": ["Snow SQL", "SQL"], "cloud_platforms": ["AWS", "Amazon S3", "AWS Glue", "EC2"], "devops_tools": ["GitHub"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Snowflake", "DBT", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "Column <PERSON>", "Data Masking", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "Kafka", "Sigma", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "DB2", "OLTP", "OLAP", "Dimension Modeling", "Fact Tables", "Data Modeling", "ETL", "ELT", "Data Quality", "Data Validation", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "Index Design"]}, "Aiswarya Sukumaran Data analyst": {"programming_languages": ["Python", "R", "ETL Processes", "PostgreSQL", "Power BI", "Data Warehousing", "Regression Analysis", "Predictive Modeling", "Time Series Forecasting", "Data Transformation", "Power Query", "SSIS (SQL Server Integration Services)", "<PERSON><PERSON>", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level"], "databases": ["SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>", "pandas", "NumPy"], "other": ["Excel", "Data Modeling", "Hypothesis Testing", "Classification", "Data Cleaning", "Data Automation", "PivotTables", "DAX"]}, "Himanshu": {"programming_languages": ["Data Warehousing", "Cloud Migration", "Data Modernization", "Data Enrichment", "Data Integration", "Data Processing", "Erro<PERSON>", "Data Transformation", "Enterprise Reporting", "Dashboarding", "Solution Architecture", "Amazon Web Services (AWS)", "Amazon Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Python", "Oracle PL/SQL", "Stored Procedures", "Informatica PowerCenter", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Cloud (IDMC)", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle Database (11g, 10g, 9i, 8x)", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Microsoft Power BI", "SAS Visual Investigator", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "RDBMS", "Star Schema", "Normalization"], "databases": ["SQL"], "cloud_platforms": ["AWS S3", "Amazon EC2", "AWS Glue", "AWS Lambda"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Advanced Analytics", "Data Pipelining", "Data Quality", "Data Validation", "Data Intelligence", "ETL Design", "ETL Development", "MIS Management", "Data Modeling", "Amazon Athena", "OBIEE", "SAS Visual Analytics", "Neb<PERSON>", "Snowflake", "DWH", "DM", "OLAP", "OLTP", "Snowf<PERSON>a", "Fact Tables", "Dimension Tables", "Slowly Changing Dimensions (SCD)", "Agile"]}, "DA manager Nithin": {"programming_languages": ["Data Security", "Data Warehousing", "Risk Management", "Program Management", "Project Planning", "Data Wrangling", "Python", "Power BI", "Microsoft Excel", "Azure Cloud", "JIRA", "Data Flow Architectures", "Data Transformation", "Data Storage Strategies", "Microsoft Excel Spreadsheets", "Relational Databases"], "databases": ["SQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Data Analysis", "Business Intelligence", "Data Visualization", "Data Compliance", "Data Modeling", "Cost Analysis", "ETL", "Visual Studio", "Agile", "Data Collection", "KPI Development", "SLA Development"]}, "Raghu": {"programming_languages": ["Mechanical Product Design", "System Integration", "Machined Parts Design", "Design Standardization", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "SolidWorks", "2D Drawing Review", "CE Marking", "Machinery Directive 2006/42/EC", "Reverse Engineering"], "other": ["Mechanical Component Design", "Sheet Metal Design", "Component Localization", "Cost Optimization", "Design Calculations", "UG-NX", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "MathCAD", "DNVGL", "EN-13155", "EN ISO 50308", "EN ISO 14122"]}, "Karnati": {"programming_languages": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Oracle", "Terada<PERSON>", "Python", "Databricks", "Spark", "Data Warehousing", "Star Schema", "Data Marts", "Data Profiling", "Cloud Data Governance", "Cloud Data Integration", "Cloud Application Integration", "ICRT", "Data Capture (CDC)", "Unix Shell Scripting", "Materialized Views", "Stored Procedures"], "databases": ["SQL"], "cloud_platforms": ["AWS"], "other": ["Intelligent Data Management Cloud (IDMC)", "DB2", "Netezza", "Snowflake", "Hive", "Unix", "Windows", "ETL", "Snowf<PERSON>a", "Dimensions", "Fact Tables", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Data Quality", "Business 360 Console", "JSON", "API", "ICS", "Big Data", "<PERSON><PERSON><PERSON>", "Data Pipelines"]}, "Shashindra": {"programming_languages": ["React", "TypeScript", "JavaScript", "PHP", "Azure", "REST", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "<PERSON><PERSON><PERSON>", "Redux Toolkit", "SWR", "<PERSON><PERSON>", "React Router", "RBAC", "ServiceNow"], "web_technologies": ["HTML5", "CSS3", "Node.js", "Tailwind CSS", "Webpack"], "databases": ["MySQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2", "Amazon Lambda"], "devops_tools": ["GitHub"], "mobile": ["A<PERSON>os"], "other": ["SOAP", "JSON", "Gulp", "SVN", "JWT"]}, "Upendra": {"programming_languages": ["Java", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "Servlets", "JAX-RS", "Java Mail", "Angular", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "MongoDB", "Quartz", "Hibernate", "Spring JPA", "WebSphere", "RDBMS", "PostgreSQL", "AWS Aurora"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["SQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2"], "devops_tools": ["Git", "<PERSON>"], "other": ["J2EE", "JSF", "Apache POI", "iText", "JSP", "JDBC", "JAX-WS", "JMS", "JUnits", "Ant", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon EKS", "AJAX", "<PERSON><PERSON>", "SVN", "Bitbucket", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Putty", "WinSCP", "Bamboo"]}, "Chandra_Resume": {"programming_languages": ["Java", "JavaScript", "Python", "Servlets", "REST", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "Cypress", "Resin", "Oracle", "PostgreSQL", "MongoDB", "JDeveloper", "ERwin Data Modeler", "JMS Hermes", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "Typescript", "Dynatrace", "SiteMinder", "Harvest", "Nx Monorepo", "<PERSON><PERSON>"], "web_technologies": ["Node.js"], "databases": ["SQL", "MySQL", "DynamoDB"], "devops_tools": ["Git", "GitLab", "<PERSON>"], "other": ["JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "JPA", "AJAX", "NestJS", "Tomcat", "WebLogic", "<PERSON><PERSON><PERSON>", "GlassFish", "IBM DB2", "Eclipse", "NetBeans", "IntelliJ", "MyEclipse", "VS Code", "Toad", "Visio", "UML", "CVS", "SVN", "SoapUI", "JUnit", "Log4j", "Ant", "<PERSON><PERSON>", "Agile", "ITIL Foundation", "LDAP", "SAML", "Bitbucket", "OOPS", "OOAD", "SOA", "@task", "TFS"]}, "KRISHNA_KANT_NIRALA_Oracle_DBA": {"programming_languages": ["Oracle DBA", "Oracle OCI", "Oracle 19c Data Guard", "Oracle 19c RAC", "Oracle 21c", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Cloud Infrastructure", "Oracle Data Guard", "Oracle Enterprise Manager 12c", "Oracle Grid Control 11g", "RMAN", "Oracle TDE", "AWR", "SQLTRACE", "TKPROF", "V<PERSON>am Backup and Recovery", "Linux Shell Scripting", "Crontab", "SQL Server 2016", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "OCI Object Storage", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "PRINCE2", "OCA - Oracle Database Administrator Certified", "OCI-Oracle Cloud Infrastructure Foundations Associate", "Teradata Certified Administrator (V2R5)", "Oracle 19c Database Administrator Training from Koenig Database Administration"], "databases": ["SQL*Plus", "PL/SQL"], "other": ["Data Pump", "ADDM", "EXPLAIN PLAN", "STATSPACK", "IBM LTO 9", "IBM LTO 8", "WebLogic 14c", "WebLogic 12c", "Tomcat", "Glassfish", "JDK", "OCI IAM", "OCI VCN", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "<PERSON>adata", "Exadata X9M-2", "ITIL V3 Foundation", "ISO/IEC 27001", "ISO 20000"]}, "Akhila D": {"programming_languages": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Java", "JavaScript", "REST", "Scrum", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "Product locking", "Ruleset locking", "Waterfall", "E-Commerce", "Insurance", "Queue Processors", "Decision Rules", "Declarative Rules", "Process Flows", "Screen Flows", "Data Transforms", "Rule Resolution", "Enterprise Class Structure", "Code review", "Document review", "WebSphere", "Pega Marketing Consultant", "Senior System Architect", "System Architect"], "web_technologies": ["CSS", "HTML"], "other": ["Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "SOAP", "Agile methodology", "Unit testing", "Sections", "Flow Actions", "List-View", "PLA", "Package locking", "SDLC", "Agents", "Application Design", "Case Management", "Data Modeling", "Activities", "Dev Studio", "App Studio", "Admin Studio", "CDH", "XML", "Postman"]}, "Uday": {"programming_languages": ["<PERSON><PERSON>", "Fiori Elements", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "JavaScript", "RICEF", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BP Integrations", "User Exits", "Customer Exits", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "SAP Fiori Launchpad", "JIRA", "SAP Security", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "FI-AR", "RTR", "Product Life Cycle Management (PLM)"], "web_technologies": ["HTML5"], "devops_tools": ["GitHub"], "other": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "PI/PO", "AIF", "BTP", "CAPM", "XML", "JSON", "BADIs", "BDC", "BAPI", "Enhancement Points", "ALE IDOCs", "CDS Views", "AMDP", "Web IDE", "BSP", "Business Objects (BO)", "ATC", "SPDD", "SPAU", "PFTC", "Quality Management (QM)", "FI-AP", "FI-GL (FICO)", "SCM"]}, "Updated_CV_-_Tauqeer_Ahmad1_1": {"programming_languages": ["TypeScript", "React", "Angular", "REST APIs", "GraphQL APIs", "Serverless Architecture", "Amazon Web Services", "MySQL Aurora", "Apache ECharts", "Microservices", "Salesforce"], "web_technologies": ["Node.js"], "cloud_platforms": ["AWS SAM", "AWS CDK", "Lambda"], "devops_tools": ["CI/CD"], "other": ["Next.js", "NestJS", "Cognito", "Okta", "OIDC", "Mantine UI", "Vite", "AI", "<PERSON><PERSON><PERSON>", "Sanity", "Styled Components", "Amplify", "ShadCN UI", "CDL"]}, "Ajeesh_resume": {"programming_languages": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Talwar Simulator", "Ethernet", "Swift", "Device Drivers", "ClearCase", "ios-xe asr 1K router", "C++", "OpenWRT", "AT Interfaces", "LED Manager", "Shell Scripting", "Integration Testing", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "Gre", "Mesh Networking", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "RFID", "AeroScout tags"], "devops_tools": ["GIT"], "other": ["Cisco Access Points", "WiFi", "802.11", "WLAN", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "SVN", "Cisco catalyst 3750 Switch", "C", "Linux", "Ubus", "QMI", "Unit Testing", "<PERSON><PERSON>xing", "XML", "GDB"]}, "Vidwaan_vidwan_resume": {"programming_languages": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "ReactJS", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "Amazon Web Services (AWS)", "Kubernetes", "<PERSON>er", "Design Patterns", "Data Structures", "Machine Learning", "Test Driven Development (TDD)", "Spark SQL", "PostgreSQL", "BottleRocket", "IAM Role Management", "Server-Side Encryption"], "web_technologies": ["HTML", "CSS"], "databases": ["SQL", "DynamoDB", "MySQL"], "cloud_platforms": ["Lambda", "EC2", "S3", "AWS CDK", "AWS Glue", "AWS Athena"], "devops_tools": ["<PERSON>", "CI/CD", "Git"], "other": ["SQS", "SNS", "Agile", "Postman", "JUnit", "<PERSON><PERSON><PERSON>", "Step Functions", "CloudWatch", "JDK 8", "JDK 17"]}, "Soham_Resume_Java": {"programming_languages": ["Java", "Spring Boot", "Hibernate", "Python", "JavaScript", "TypeScript", "React.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Azure", "Google Cloud Platform (GCP)", "JIRA", "<PERSON>er", "Microservices Architecture", "REST APIs", "Power BI", "Android Studio", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer"], "web_technologies": ["HTML5", "CSS3"], "databases": ["SQL", "MySQL"], "devops_tools": ["Git", "<PERSON>", "CI/CD"], "data_analytics": ["<PERSON><PERSON>"], "other": ["JUnit", "<PERSON><PERSON><PERSON>", "Apache Kafka", "SOAP", "JSON", "Bluetooth"]}}