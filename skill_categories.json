{"Sudhakara Rao Illuri-Fusion Financial Cloud": {"programming_languages": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Intercompany"], "databases": ["SQL"], "other": ["TOAD", "AIM Methodology", "OUM Methodology", "Windows 2007/2008/2010", "UNIX", "BIP"]}, "pavani_resume": {"programming_languages": ["Selenium RC", "Selenium WebDriver", "Selenium Grid", "<PERSON><PERSON><PERSON>", "JavaScript", "Python", "Java", "<PERSON>", "Oracle", "SQL Server", "TortoiseSVN", "HP Quality Center", "<PERSON><PERSON>", "SharePoint", "Microsoft Office", "Waterfall", "Scrum"], "web_technologies": ["HTML"], "databases": ["SQL"], "devops_tools": ["<PERSON>"], "other": ["Selenium IDE", "TestNG", "QTP", "MS Access", "Toad", "<PERSON><PERSON>", "SoapUI", "Agile"]}, "Saisree Kondamareddy_ QA Consultant (1)": {"programming_languages": ["Java", "Selenium WebDriver", "SeeTest/Experitest", "<PERSON><PERSON>", "Azure DevOps", "PostgreSQL", "BrowserStack", "Waterfall", "Integration Testing", "Regression Testing", "UAT (User Acceptance Testing)", "Test Plan Creation", "Bug Reporting", "AI-powered Automation"], "cloud_platforms": ["LambdaTest"], "devops_tools": ["Git", "GitHub"], "other": ["ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "HP ALM", "Agile", "Functional Testing", "Smoke Testing", "System Testing", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Unit Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Case Execution", "Defect Management", "Test Management", "Windows"]}, "Sowmya": {"programming_languages": ["Power BI", "SSRS", "Power BI Desktop", "Power BI Service", "Power Query", "Power Pivot", "Star Schema", "Data Warehousing", "Microsoft BI Stack", "SQL Server", "Oracle", "Python", "Azure Blob Storage", "Power Automate", "Row-Level Security (RLS)", "Data Mart", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Report Development", "Stored Procedures", "Triggers", "Database Performance Tuning", "Query Optimization", "Database Partitioning"], "web_technologies": ["HTML"], "databases": ["SQL", "T-SQL", "PL/SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["SSIS", "SSAS", "DAX", "M Language", "Data Gateway", "ETL", "Dimensional Modeling", "Snowf<PERSON>a", "Talend", "Visual Studio Code", "Data Flows", "Functions", "Indexing"]}, "Varshika": {"programming_languages": ["SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "SAP Warehouse Management", "Business Process Mapping", "WRICEF Documentation", "Financial Reporting", "Hyperion Financial Management (HFM)", "Profit Center Accounting (PCA)", "General Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Material Master Data Management", "Procurement Processes", "Demand Forecasting", "Order-to-Delivery Process", "Inventory Planning", "Pricing Strategies", "Bank Reconciliation", "Automatic Payment Program (F110)", "Real-time Cash Visibility System", "SAP Best Practices", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Cash Management Integration", "FI-GL Transactions", "AP/AR Transactions", "Vendor/Customer Open Items", "Business Process Documentation", "SAP Certified Functional Consultant"], "other": ["SAP Financial Accounting (FI)", "ASAP Methodology", "Agile Methodologies", "FIT-GAP Analysis", "SAP Solution Design", "Financial Data Management (FDMEE)", "SAP Cash Management (CM)", "Financial Analysis", "SAP MM Functionalities", "Cash Pooling", "Inhouse Cash Management"]}, "Maanvi Resume (3)": {"programming_languages": ["Python", "Java", "PowerShell", "C++", "Android App Development", "Spring Boot", "Django", "Terraform", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "Kubernetes", "Redis", "Amazon Web Services (AWS)", "Azure", "<PERSON>er", ".NET Core", "SQL Server", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["MySQL"], "frameworks": ["Flask"], "other": ["<PERSON><PERSON>", "C", "<PERSON><PERSON>", "JUnit", "Apache Kafka", "JSON", "Linux", "macOS", "Kali Linux", "Windows", "OAuth", "DBMS"]}, "SivakumarDega_CV": {"programming_languages": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "Perfecto", "REST Assured", "Karate Framework", "Azure DevOps", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "Swagger", "Azure", "Microsoft Azure", "Android", "Web Services Testing", "Microservices Testing", "Regression Testing", "Integration Testing", "User Interface Testing", "Browser Compatibility Testing", "Exploratory Testing", "Risk-based Testing", "User Acceptance Testing", "Service Virtualization", "Continuous Integration", "Data Warehouse Testing", "Mainframes Testing", "Interactive Voice Response (IVR) Testing", "Customer Telephony Integration (CTI) Testing"], "cloud_platforms": ["AWS", "GCP"], "devops_tools": ["<PERSON>", "GitLab"], "mobile": ["iOS"], "other": ["<PERSON><PERSON>", "TestNG", "Appium", "SeeTest", "CA DevTest", "UFT", "LeanFT", "Bitbucket", "CICS", "JCL", "VSAM", "COBOL", "Sufi", "DB2", "File-Aid", "SOAP", "Postman", "Agile", "<PERSON><PERSON><PERSON><PERSON> (implied)", "Mobile Testing", "Database Testing", "API Testing", "UI Testing", "Functional Testing", "System Testing", "End-to-End Testing", "OAuth", "SSO", "Continuous Deployment", "ETL Testing"]}, "Laxman_Gite": {"programming_languages": ["C#", "ASP.NET Core", "Angular", "Azure Developer", "Azure Functions", "Service Bus", "Azure Storage", "<PERSON><PERSON>", "Azure AD", "Virtual Network", "<PERSON>er", "Kubernetes", "Azure Service Fabric", "Microservices", "j<PERSON><PERSON><PERSON>", "Event Grid", "Azure Container Registry", "Serverless Architecture", "Microsoft Azure", "Amazon Web Services (AWS)", "SQL Server", "Stored Procedures", "Triggers", "Azure Data Lake", "Azure Monitor", "Direct Connect", "Microsoft Azure Certified Professional", "Query Performance Optimization", "Hybrid Solution Architecture", "Container-based Architecture", "Federated Database Design", "Real-time Data Analytics", "E-commerce Architecture", "High-Throughput System Architecture", "Scalable Architecture Design", "High-Performance Architecture Design"], "web_technologies": ["HTML", "CSS"], "databases": ["MySQL", "T-SQL", "PL/SQL"], "devops_tools": ["CI/CD"], "frameworks": [".NET 6", "ASP.NET MVC"], "other": ["Web API", "Logic App", "API Management", "Cosmos DB", "YAML Pipeline", "Application Insights", "Log Analytics", "<PERSON>", "Event Hub", "Snowflake", "Functions", "VPC", "VPN", "Data Modeling"]}, "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": {"programming_languages": ["C#", "ASP.NET Core", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "Azure APIM", "Azure Service Bus", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "Angular", "Angular Reactive Forms", "Observable", "SQL Server", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "PostgreSQL", "Oracle (PL/SQL)", "DevExpress", "Winforms", "Brainbench C# 5.0"], "databases": ["T-SQL", "SQLite", "MySQL"], "devops_tools": ["GitHub"], "frameworks": ["ADO.NET"], "other": ["Web API", "WPF", "MVC", "WCF", "App Insights", "Logic Apps", "LINQ", "Stimulsoft", "HttpClient", "NUnit", "Coded UI Testing", "MS Access", "InstallShield", "TFS", "SVN", "IIS", "Apache Tomcat", "MS Excel", "Postman"]}, "Vivek Anil .Net lead": {"programming_languages": ["ASP.NET Core", ".NET Framework", "C#", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "React", "Swagger", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "<PERSON><PERSON>", "Azure DevOps", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "C++", "Python"], "web_technologies": ["Node.js", "HTML"], "devops_tools": ["Git", "GitHub", "CI/CD"], "frameworks": ["ASP.NET", "ADO.NET"], "other": ["CosmosDB", "Apache Kafka", "ActiveMQ", "Web API", "OAuth2", "Open API", "OOPS", "SVN", "NUnit", "Moq", "Agile Methodologies", "WCF"]}, "PunniyaKodi V updated resume": {"programming_languages": ["C#", ".NET Framework", ".NET Core", "Windows Service", "j<PERSON><PERSON><PERSON>", "AngularJS", "ReactJS", "SQL Server", "XML Web Services", "TypeScript", "JavaScript", "Bootstrap", "Amazon Web Services (AWS)", "CloudFront", "RDS", "PostgreSQL", "ElasticSearch", "OpenSearch", "Entity Framework", "AG Grid", "txText Control", "Crystal Reports", "Active Reports", "SSRS", "Azure DevOps", "Terraform", "Domain Driven Design (DDD)", "Test Driven Development (TDD)", "Scrum", "Object-Oriented Programming (OOP)", "RESTful Web Services", "OpenTelemetry", "FullStory", "Google Analytics"], "web_technologies": ["Node.js", "HTML", "HTML5", "CSS", "Sass"], "databases": ["SQL", "PL/SQL", "DynamoDB"], "cloud_platforms": ["EC2", "Lambda"], "frameworks": ["ASP.NET MVC", "ASP.NET", "VB.NET", "ADO.NET"], "other": ["Web API", "LINQ", "WCF", "AJAX", "JSON", "IAM", "ECS", "SQS", "SNS", "API Gateway", "CloudWatch", "Step Functions", "El<PERSON>", "SSIS", "TFS", "ASPX", "NuGet", "Agile", "SOAP", "Pub/Sub", "Elastic APM"]}, "Chary": {"programming_languages": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID Principles", "Web Services", "Microservices", "REST", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "<PERSON>er", "Kubernetes", "Azure Logic Apps", "RabbitMQ", "Azure App Services", "Azure Functions", "Azure Active Directory", "ServiceNow", "Azure Entra ID", "Team Foundation Server", "SVN Subversion", "TortoiseSVN", "CQRS", "Saga Pattern", "Choreography Pattern", "Gateway Aggregation", "Circuit Breaker <PERSON>", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Design Pattern", "Dependency Inversion Principle", "Factory Design Pattern", "Abstract Factory Design Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Database", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Object-Oriented Programming", "Design Patterns", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence"], "databases": ["Amazon DynamoDB"], "cloud_platforms": ["Amazon EC2", "AWS Lambda"], "devops_tools": ["CI/CD Pipeline"], "frameworks": ["ASP.NET MVC", "ASP.NET", ".NET MAUI"], "other": ["XAML", "WCF", "Web API", "SOAP", "<PERSON><PERSON><PERSON><PERSON>", "Kendo UI", "Web Jobs", "HPSM", "SOA", "OAuth 2.0", "OKTA", "Bitbucket", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "API Gateway", "MuleSoft", "Kafka", "Tibco", "Dependency Injection", "SEO Optimization", "SSIS", "Data Modeling", "Selenium", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "IoT"]}, "Donish Devasahayam_DotNET": {"programming_languages": [".NET Core", "C#", "Python", "Angular", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "gRPC", "Entity Framework", "Lambda Expressions", "Azure", "Azure App Services", "Azure Pipelines", "Azure Container Registry (ACR)", "AKS (Azure Kubernetes Service)", "Azure SQL Server", "Azure Blob Storage", "Azure Functions", "<PERSON>er", "Kubernetes", "AWS (Amazon Web Services)", "Amazon ECR", "Blazor", "MudBlazor", "Telerik", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "Blue Yonder", "<PERSON><PERSON>", "Rally"], "databases": ["LINQ to SQL", "NoSQL"], "cloud_platforms": ["Amazon S3"], "frameworks": [".NET", "ADO.NET"], "data_analytics": ["<PERSON><PERSON>"], "other": ["DB2", "LINQ", "LINQ to Objects", "Cosmos DB", "Amazon EKS", "Elastic Beanstalk", "Datadog", "Kafka", "Kendo UI", "SAP", "IDoc", "Logility"]}, "Kondaru_04_Manjunath_Resume": {"programming_languages": ["Amazon Web Services (AWS)", "CloudFormation", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "Waterfall", "Shell Scripting", "<PERSON>er", "PowerShell", "WebSphere", "Windows Server", "Red Hat Linux", "VMware", "Elastic Load Balancers", "JIRA", "<PERSON><PERSON>"], "cloud_platforms": ["EC2"], "devops_tools": ["<PERSON>", "GitHub", "Ansible", "CI/CD", "Git"], "other": ["VPC", "IAM", "Agile", "ANT", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Unix", "CentOS"]}, "Jhansi P": {"programming_languages": ["Amazon RDS", "Amazon Route 53", "AWS CloudFormation", "Amazon CloudFront", "Python", "Java", "Pivotal Cloud Foundry (PCF)", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "Azure", "Azure DevOps", "Groovy", "Subversion (SVN)", "Elasticsearch", "<PERSON><PERSON>", "Infrastructure as Code (IaC)", "Configuration Management", "Containerization", "Orchestration", "Build/Release Management", "Microservices"], "databases": ["Amazon DynamoDB", "MySQL"], "cloud_platforms": ["AWS", "Amazon EC2", "Amazon S3", "AWS Auto Scaling", "AWS Lambda", "AWS CLI", "A<PERSON> (EKS)"], "devops_tools": ["Ansible", "<PERSON>", "Git", "GitHub", "GitLab", "CI/CD"], "other": ["Amazon ECS", "Elastic Beanstalk", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon IAM", "Amazon CloudWatch", "HTTP", "TLS", "<PERSON><PERSON>", "Ant", "<PERSON><PERSON>", "Tomcat", "WebLogic", "Apache", "<PERSON><PERSON><PERSON><PERSON>", "SCM"]}, "Puneet": {"programming_languages": ["Java", "Scrum", "<PERSON><PERSON>", "Microsoft Project", "SmartSheet", "SonarQube", "Warehouse Management"], "devops_tools": ["<PERSON>", "CI/CD"], "other": ["J2EE", "Agile", "SAFe", "Ka<PERSON><PERSON>", "Confluence", "DevOps", "CMMI Level 5", "SAP", "Windows Application", "OTT", "PMP", "PSM"]}, "Pradeep_Project_manager_Nithin1": {"programming_languages": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Resource Management", "Stakeholder Management", "Release Management", "Delivery Management", "<PERSON><PERSON>", "Azure DevOps", "ServiceNow", "Microsoft Excel", "Angular", "Azure Cloud", "EZTrieve", "C#"], "web_technologies": ["Node.js"], "databases": ["SQL"], "frameworks": [".NET"], "other": ["Cost Analysis", "Client Management", "Confluence", "COBOL", "IBM BMP", "CMMI Level 5", "ISO 27001"]}, "Kamal": {"programming_languages": ["Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Fivetran", "Streamset", "Snowpark", "Python", "Stored Procedures", "Data Encryption", "Data Decryption", "Data Governance", "PySpark", "Apache Airflow", "Informatica PowerCenter", "Oracle", "MS SQL Server", "Data Warehousing", "Data Integration", "Data Migration", "Real-time Data Ingestion", "SQR 6.0", "Query Plan Optimization"], "databases": ["Snow SQL", "SQL"], "cloud_platforms": ["AWS", "Amazon S3", "AWS Glue", "EC2"], "devops_tools": ["GitHub"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Snowflake", "DBT", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "Column <PERSON>", "Data Masking", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "Kafka", "Sigma", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "DB2", "OLTP", "OLAP", "Dimension Modeling", "Fact Tables", "Data Modeling", "ETL", "ELT", "Data Quality", "Data Validation", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "Index Design"]}, "Aiswarya Sukumaran Data analyst": {"programming_languages": ["ETL Processes", "Python", "PostgreSQL", "Power BI", "Data Warehousing", "Regression", "Predictive Modeling", "Time Series Forecasting", "Data Transformation", "Power Query", "SSIS (SQL Server Integration Services)", "<PERSON><PERSON>", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "R"], "databases": ["SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>", "<PERSON><PERSON>", "NumPy"], "other": ["Data Analysis", "Business Intelligence", "Data Management", "Excel", "Data Modeling", "Statistical Analysis", "Hypothesis Testing", "Classification", "Data Cleaning", "Data Automation", "PivotTables", "DAX", "Agile"]}, "Himanshu": {"programming_languages": ["Python", "Oracle PL/SQL", "Data Warehousing", "Data Integration", "Data Migration", "Data Modernization", "Data Processing", "Data Transformation", "Enterprise Reporting", "Dashboarding", "Amazon Web Services (AWS)", "Amazon Redshift", "Lake Formation", "CloudFormation", "Microsoft Azure", "Informatica PowerCenter", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "PostgreSQL", "Stored Procedures", "Triggers", "RDBMS", "Star Schema", "Normalization", "Microsoft Power BI", "SAS Visual Investigator", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "<PERSON>aud Detection", "FinCrime"], "databases": ["SQL"], "cloud_platforms": ["AWS S3", "EC2", "AWS Glue", "AWS Lambda"], "data_analytics": ["<PERSON><PERSON>"], "other": ["ETL", "Data Modeling", "Data Quality", "Data Validation", "Data Pipelining", "Data Visualization", "Athena", "IICS", "IDMC", "DWH", "DM", "OLAP", "OLTP", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "OBIEE", "SAS Visual Analytics", "Neb<PERSON>", "Snowflake", "Agile", "Data Intelligence", "AML Compliance", "Flat Files", "CSV", "JSON", "XML"]}, "DA manager Nithin": {"programming_languages": ["Microsoft Excel", "Python", "Power BI", "Data Security", "Data Warehousing", "Data Wrangling", "Azure Cloud", "JIRA", "Risk Management", "Project Management", "Program Management", "Data Flow Architectures", "Data Transformation", "Data Collection and Storage"], "databases": ["SQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Data Analysis", "Data Visualization", "Data Modeling", "ETL", "Visual Studio", "Cost Analysis", "Agile", "KPI Development", "SLA Development"]}, "Raghu": {"programming_languages": ["Mechanical Product Design", "System Integration", "Machined Parts Design", "Design Standardization", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "SolidWorks", "2D Drawings Review", "CE Marking", "Machinery Directive 2006/42/EC", "Reverse Engineering"], "other": ["Mechanical Design", "Sheet Metal Design", "Component Localization", "Cost Optimization", "Design Calculations", "UG-NX", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack up analysis", "ASME Y14.5", "MathCAD", "DNVGL", "EN-13155", "EN ISO 50308", "EN ISO 14122"]}, "Karnati": {"programming_languages": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Oracle", "Terada<PERSON>", "Python", "Databricks", "Spark", "Data Warehousing", "Star Schema", "Data Marts", "Data Profiling", "Cloud Data Governance", "Cloud Data Integration", "Cloud Application Integration", "ICRT", "Data Capture (CDC)", "Unix Shell Scripting", "Materialized Views", "Stored Procedures"], "databases": ["SQL"], "cloud_platforms": ["AWS"], "other": ["Intelligent Data Management Cloud (IDMC)", "DB2", "Netezza", "Snowflake", "Hive", "Unix", "Windows", "ETL", "Snowf<PERSON>a", "Dimensions", "Fact Tables", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Data Quality", "Business 360 Console", "JSON", "API", "ICS", "Big Data", "<PERSON><PERSON><PERSON>", "Data Pipelines"]}, "Shashindra": {"programming_languages": ["React", "ReactJS", "Redux Toolkit", "SWR", "<PERSON><PERSON>", "React Router", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "PHP", "Azure", "REST", "<PERSON><PERSON><PERSON>", "RBAC", "Silverlight", "Scrum", "Software Development Lifecycle (SDLC)"], "web_technologies": ["HTML5", "CSS3", "Tailwind CSS", "Node.js", "Webpack"], "databases": ["MySQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2", "Amazon Lambda"], "devops_tools": ["GitHub", "GitHub Copilot"], "mobile": ["A<PERSON>os"], "other": ["ES6", "SOAP", "JSON", "Gulp", "SVN", "JWT", "Agile"]}, "Upendra": {"programming_languages": ["Java", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "Servlets", "JAX-RS", "Java Mail", "Angular", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "MongoDB", "Quartz", "Hibernate", "Spring JPA", "WebSphere", "RDBMS", "AWS Aurora Postgres"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["SQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2"], "devops_tools": ["Git", "<PERSON>"], "other": ["J2EE", "JSF", "Apache POI", "iText", "JSP", "JDBC", "JAX-WS", "JMS", "JUnit", "Ant", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon EKS", "AJAX", "Dojo", "SVN", "Bitbucket", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Putty", "WinSCP", "Bamboo"]}, "Chandra_Resume": {"programming_languages": ["Java", "JavaScript", "Python", "Servlets", "REST", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "Cypress", "Resin", "Oracle", "PostgreSQL", "MongoDB", "JDeveloper", "ERwin Data Modeler", "JMS Hermes", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "Typescript", "Dynatrace", "SiteMinder", "Harvest", "Nx Monorepo", "<PERSON><PERSON>"], "web_technologies": ["Node.js"], "databases": ["SQL", "MySQL", "DynamoDB"], "devops_tools": ["Git", "GitLab", "<PERSON>"], "other": ["JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "JPA", "AJAX", "NestJS", "Tomcat", "WebLogic", "<PERSON><PERSON><PERSON>", "GlassFish", "IBM DB2", "Eclipse", "NetBeans", "IntelliJ", "MyEclipse", "VS Code", "Toad", "Visio", "UML", "CVS", "SVN", "SoapUI", "JUnit", "Log4j", "Ant", "<PERSON><PERSON>", "Agile", "ITIL Foundation", "LDAP", "SAML", "Bitbucket", "OOPS", "OOAD", "SOA", "@task", "TFS"]}, "KRISHNA_KANT_NIRALA_Oracle_DBA": {"programming_languages": ["Oracle Database Administration", "Oracle 19c", "Oracle 21c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle RAC", "Oracle Data Guard", "Oracle Cloud Infrastructure (OCI)", "Oracle Enterprise Manager (OEM)", "Oracle Enterprise Manager 12c", "Oracle Grid Control 11g", "Oracle TDE", "Oracle Data Guard", "RMAN", "SQL Server 2016", "Red Hat Linux 7", "Red Hat Linux 8", "V<PERSON>am Backup and Recovery", "AWR", "SQL*Trace", "TKPROF", "Linux Shell Scripting", "Crontab", "WAR file deployment", "OCI Object Storage", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "Terada<PERSON>", "PRINCE2", "OCA - Oracle Database Administrator Certified", "OCI - Oracle Cloud Infrastructure Foundations Associate", "Teradata Certified Administrator (V2R5)", "Database Migration", "Backup and Recovery", "Performance Tuning", "Fault Tolerance", "Scalability", "Virtualization", "Azure", "Import/Export", "Transportable Tablespaces"], "databases": ["SQL", "PL/SQL"], "cloud_platforms": ["AWS"], "other": ["Data Pump", "IBM LTO 9", "IBM LTO 8", "ADDM", "EXPLAIN PLAN", "STATSPACK", "Tomcat", "Glassfish", "WebLogic 14c", "WebLogic 12c", "JDK", "<PERSON>adata", "Exadata X9M-2", "OCI IAM", "OCI VCN", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "ITIL v3 Foundation", "High Availability"]}, "Akhila D": {"programming_languages": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Java", "JavaScript", "REST", "Scrum", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "Product locking", "Ruleset locking", "Waterfall", "E-Commerce", "Insurance", "Queue Processors", "Decision Rules", "Declarative Rules", "Process Flows", "Screen Flows", "Data Transforms", "Rule Resolution", "Enterprise Class Structure", "Code review", "Document review", "WebSphere", "Pega Marketing Consultant", "Senior System Architect", "System Architect"], "web_technologies": ["CSS", "HTML"], "other": ["Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "SOAP", "Agile methodology", "Unit testing", "Sections", "Flow Actions", "List-View", "PLA", "Package locking", "SDLC", "Agents", "Application Design", "Case Management", "Data Modeling", "Activities", "Dev Studio", "App Studio", "Admin Studio", "CDH", "XML", "Postman"]}, "Uday": {"programming_languages": ["<PERSON><PERSON>", "Fiori Elements", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "FI-AR", "RTR", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "RICEF objects", "Data Dictionary (DDIC)", "Module pool programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BP Integrations", "User exits", "Customer exits", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "SAP Fiori List Report Application", "SAP Fiori Launchpad", "SAP UI5 Framework", "JIRA", "SAP Security", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer"], "web_technologies": ["HTML5"], "devops_tools": ["GitHub"], "other": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "PI/PO", "AIF", "BTP", "CAPM", "Quality Management (QM)", "FI-AP", "FI-GL (FICO)", "SCM", "XML", "JSON", "BADIs", "BDC", "BAPI", "Enhancement points", "ALE IDOCs", "CDS Views", "AMDP", "Analytical Applications", "CDS Annotations", "WebIDE", "BSP", "Business Objects (BO)", "ATC", "SPDD", "SPAU", "PFTC"]}, "Updated_CV_-_Tauqeer_Ahmad1_1": {"programming_languages": ["TypeScript", "React", "Angular", "REST APIs", "GraphQL APIs", "Apache ECharts", "MySQL Aurora", "Amazon Web Services", "Serverless Architecture", "Microservices Architecture", "Salesforce"], "web_technologies": ["Node.js"], "cloud_platforms": ["AWS SAM", "AWS CDK", "AWS Lambda"], "devops_tools": ["CI/CD"], "other": ["Next.js", "NestJS", "Cognito", "Okta", "OIDC", "Mantine UI", "Vite", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "CDL"]}, "Ajeesh_resume": {"programming_languages": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Talwar Simulator", "Ethernet", "Swift", "ClearCase", "ios-xe asr 1K router", "C++", "OpenWRT", "AT interfaces", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filter configuration"], "devops_tools": ["GIT"], "other": ["Cisco Access Points", "WiFi", "802.11", "WLAN", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "SVN", "Cisco catalyst 3750 Switch", "C", "Linux", "QMI", "Ubus", "XML", "GDB"]}, "Vidwaan_vidwan_resume": {"programming_languages": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "ReactJS", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "Amazon Web Services (AWS)", "Kubernetes", "<PERSON>er", "PostgreSQL", "Spark SQL", "Design Patterns", "Data Structures", "Machine Learning", "Test Driven Development (TDD)", "IAM Role Management", "Server-Side Encryption (S3)", "BottleRocket"], "web_technologies": ["HTML", "CSS"], "databases": ["SQL", "DynamoDB", "MySQL"], "cloud_platforms": ["Lambda", "EC2", "S3", "AWS CDK", "AWS Glue"], "devops_tools": ["<PERSON>", "CI/CD", "Git"], "other": ["SQS", "SNS", "Agile", "Postman", "JUnit", "<PERSON><PERSON><PERSON>", "CloudWatch", "Step Functions", "Athena", "JDK 8", "JDK 17", "EKS"]}, "Soham_Resume_Java": {"programming_languages": ["Java", "Spring Boot", "Hibernate", "Python", "JavaScript", "TypeScript", "React.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Azure", "Google Cloud Platform (GCP)", "JIRA", "<PERSON>er", "Microservices Architecture", "REST APIs", "Power BI", "Android Studio", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer"], "web_technologies": ["HTML5", "CSS3"], "databases": ["SQL", "MySQL"], "devops_tools": ["Git", "<PERSON>", "CI/CD"], "data_analytics": ["<PERSON><PERSON>"], "other": ["JUnit", "<PERSON><PERSON><PERSON>", "Apache Kafka", "SOAP", "JSON", "Bluetooth"]}}