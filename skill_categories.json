{"Sudhakara Rao Illuri-Fusion Financial Cloud": {"programming_languages": ["Oracle Fusion Financials", "Oracle E-Business Suite R12", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Oracle Cloud Budgetary Control", "Accounts Receivable", "General <PERSON><PERSON>", "I-Receivables", "Order Management", "Oracle Financial Accounting Hub", "FRS", "SmartView", "Data Loader", "Hyperion FRS", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Business Process Management (BPM)", "Oracle Allocations", "Sub Ledger Accounting (SLA)", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Oracle Cloud", "Intercompany"], "databases": ["SQL"], "other": ["Accounts Payable", "Fixed Assets", "Cash Management", "I-Expenses", "BIP", "OTBI", "TOAD", "Windows 2007/2008/2010", "UNIX", "AIM Methodology", "OUM Methodology"]}, "pavani_resume": {"programming_languages": ["Selenium RC", "Selenium WebDriver", "Selenium Grid", "<PERSON><PERSON><PERSON>", "JavaScript", "Python", "Java", "<PERSON>", "Oracle", "SQL Server", "TortoiseSVN", "HP Quality Center", "JIRA", "Waterfall"], "web_technologies": ["HTML"], "databases": ["SQL"], "devops_tools": ["<PERSON>"], "other": ["Selenium IDE", "TestNG", "QTP", "MS Access", "Toad", "<PERSON><PERSON>", "SoapUI", "Agile"]}, "Saisree Kondamareddy_ QA Consultant (1)": {"programming_languages": ["Selenium WebDriver", "SeeTest/Experitest", "Java", "<PERSON><PERSON>", "Azure DevOps", "PostgreSQL", "BrowserStack", "Waterfall", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "Production Support", "Test Plan Creation", "Test Schedule Creation", "Defect Tracking"], "cloud_platforms": ["LambdaTest"], "devops_tools": ["Git", "GitHub"], "other": ["ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "HP ALM", "Agile", "Functional Testing", "Smoke Testing", "System Testing", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Development", "Test Management", "Windows"]}, "Sowmya": {"programming_languages": ["Power BI", "SSRS", "Power Query", "Power Pivot", "Power BI Desktop", "Power BI Service", "SQL Server", "Oracle", "Microsoft BI Stack", "Data Warehousing", "Star Schema", "Python", "Azure Blob Storage", "Power Automate"], "web_technologies": ["HTML"], "databases": ["SQL", "T-SQL", "PL/SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["SSIS", "SSAS", "DAX", "M Language", "Data Gateway", "ETL", "Dimensional Modeling", "Snowf<PERSON>a", "Talend", "Visual Studio Code"]}, "Varshika": {"programming_languages": ["SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "SAP Warehouse Management", "Business Process Mapping", "WRICEF Documentation", "Financial Reporting", "Hyperion Financial Management (HFM)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Material Master Data Management", "Procurement Processes", "Demand Forecasting", "Inventory Planning", "Bank Reconciliation", "Automatic Payment Program (F110)", "Real-time Cash Visibility System", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "SAP Best Practices"], "other": ["SAP Financial Accounting (FI)", "ASAP Methodology", "Agile Methodology", "FIT-GAP Analysis", "SAP Solution Design", "Financial Data Management (FDMEE)", "SAP Cash Management (CM)", "SAP Accounts Payable (AP)", "Cash Pooling", "Inhouse Cash Management", "Financial Analysis"]}, "Maanvi Resume (3)": {"programming_languages": ["Python", "Java", "PowerShell", "C++", "Android App Development", "Spring Boot", "Django", "Terraform", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "Kubernetes", "Redis", "Amazon Web Services (AWS)", "Azure", "<PERSON>er", ".NET Core", "SQL Server", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["MySQL"], "frameworks": ["Flask"], "other": ["<PERSON><PERSON>", "C", "<PERSON><PERSON>", "JUnit", "Apache Kafka", "JSON", "Linux", "macOS", "Kali Linux", "Windows", "OAuth", "DBMS"]}, "SivakumarDega_CV": {"programming_languages": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "Perfecto", "Rest Assured", "Karate Framework", "Azure DevOps", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "Azure", "Microsoft Azure", "Swagger", "Web Services Testing", "Data Warehouse Testing", "Mainframe Testing", "Performance Testing", "Regression Testing", "Integration Testing", "User Interface Testing", "Browser Compatibility Testing", "Exploratory Testing", "Risk-based Testing", "User Acceptance Testing", "Service Virtualization", "Android Testing"], "cloud_platforms": ["AWS", "GCP"], "devops_tools": ["<PERSON>", "GitLab", "CI/CD"], "mobile": ["iOS Testing"], "other": ["<PERSON><PERSON>", "TestNG", "Appium", "SeeTest", "CA DevTest", "UFT", "LeanFT", "Bitbucket", "CICS", "JCL", "VSAM", "COBOL", "Sufi", "DB2", "File-Aid", "Postman", "SOAP", "JUnit", "Agile", "OAuth", "SSO", "Test Management", "Mobile Testing", "API Testing", "Database Testing", "ETL Testing", "Functional Testing", "System Testing", "End-to-End Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "C", "COBOL"]}, "Laxman_Gite": {"programming_languages": ["C#", "ASP.NET Core", "Angular", "Azure Developer", "Azure Functions", "Service Bus", "Azure Storage", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Virtual Network", "Azure Container Registry", "Event Grid", "<PERSON>er", "Kubernetes", "Azure Service Fabric", "Microservices", "Serverless Architecture", "j<PERSON><PERSON><PERSON>", "SQL Server", "Stored Procedures", "Triggers", "Azure Monitor", "Microsoft Azure", "Amazon Web Services (AWS)", "Query Performance Optimization", "Hybrid Solution Architecture", "Direct Connect", "Real-time Data Analytics", "E-commerce Architecture", "Azure Data Lake", "Blueprints", "Microsoft Certified Professional"], "web_technologies": ["HTML", "CSS"], "databases": ["MySQL", "T-SQL", "PL/SQL", "NoSQL Databases"], "devops_tools": ["CI/CD"], "frameworks": [".NET 6", "ASP.NET MVC"], "other": ["Web API", "Logic Apps", "API Management", "Cosmos DB", "Application Insights", "Log Analytics", "<PERSON>", "Event Hub", "YAML Pipelines", "Snowflake", "Agile Methodology", "Data Modeling", "VPC Design", "VPN", "Data Analytics"]}}