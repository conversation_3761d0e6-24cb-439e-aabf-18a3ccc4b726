{"Sudhakara Rao Illuri-Fusion Financial Cloud": {"programming_languages": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Oracle Cloud Budgetary Control", "General <PERSON><PERSON>", "Accounts Receivable", "I-Receivables", "Oracle Financial Accounting Hub", "Order Management", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Business Process Management (BPM)", "Oracle Allocations", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "Data Loader", "Hyperion FRS", "Sub Ledger Accounting (SLA)", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Intercompany"], "databases": ["SQL"], "other": ["Accounts Payable", "Fixed Assets", "Cash Management", "I-Expenses", "TOAD", "BIP", "Windows 2007/2008/2010", "UNIX", "AIM Methodology", "OUM Methodology"]}, "pavani_resume": {"programming_languages": ["Selenium RC", "Selenium WebDriver", "Selenium Grid", "<PERSON><PERSON><PERSON>", "JavaScript", "Python", "Java", "<PERSON>", "Oracle", "SQL Server", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "Waterfall"], "web_technologies": ["HTML"], "databases": ["SQL"], "devops_tools": ["<PERSON>"], "other": ["Selenium IDE", "TestNG", "QTP", "MS Access", "Toad", "<PERSON><PERSON>", "SoapUI", "Agile"]}, "Saisree Kondamareddy_ QA Consultant (1)": {"programming_languages": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "<PERSON><PERSON>", "Azure DevOps", "PostgreSQL", "BrowserStack", "Waterfall", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "Production Support", "Test Plan Creation", "Defect Tracking", "AI-powered Automation", "Android Testing"], "cloud_platforms": ["LambdaTest"], "devops_tools": ["Git", "GitHub"], "mobile": ["iOS Testing"], "other": ["ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "HP ALM", "Agile", "Functional Testing", "Smoke Testing", "System Testing", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Unit Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Case Execution", "Test Management", "Mobile Web Testing", "SDLC"]}, "Sowmya": {"programming_languages": ["Power BI", "SSRS", "Power BI Desktop", "Power BI Service", "Power Query", "Data Warehousing", "Star Schema", "Microsoft BI Stack", "Power Pivot", "Row-Level Security (RLS)", "DataMart", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "SQL Server", "Oracle", "Python", "Azure Blob Storage", "Power Automate"], "web_technologies": ["HTML"], "databases": ["SQL", "T-SQL", "PL/SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["SSIS", "SSAS", "DAX", "M Language", "ETL", "Dimensional Modeling", "Snowf<PERSON>a", "Data Gateway", "Data Flows", "Talend", "Visual Studio Code", "Excel"]}, "Varshika": {"programming_languages": ["SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "SAP Warehouse Management", "Business Process Mapping", "WRICEF Documentation", "Financial Reporting", "Hyperion Financial Management (HFM)", "SAP Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "<PERSON> (GL)", "Automated Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Demand Forecasting", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Inventory Planning", "Pricing Strategies", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Business Process Optimization", "SAP Best Practices"], "other": ["SAP Financial Accounting (FI)", "ASAP Methodology", "Agile Methodology", "FIT-GAP Analysis", "SAP Solution Design", "Financial Data Management (FDMEE)", "SAP Cash Management (CM)", "SAP Accounts Payable (AP)", "Inhouse Cash Management", "Cash Pooling", "Financial Analysis", "SAP MM Functionalities"]}, "Maanvi Resume (3)": {"programming_languages": ["Python", "Java", "PowerShell", "C++", "Android App Development", "Spring Boot", "Django", "Terraform", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "SQL Server", ".NET Core", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["MySQL"], "frameworks": ["Flask"], "other": ["<PERSON><PERSON>", "C", "<PERSON><PERSON>", "JUnit", "Apache Kafka", "JSON", "Linux", "macOS", "Kali Linux", "Windows", "OAuth", "DBMS"]}, "SivakumarDega_CV": {"programming_languages": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "Perfecto", "REST Assured", "Karate Framework", "Azure DevOps", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "Swagger", "JIRA", "<PERSON><PERSON><PERSON><PERSON>", "Quality Center", "Azure", "Microsoft Azure", "Web Services Testing", "Microservices Testing", "Data Warehouse Testing", "Mainframe Testing", "IVR Testing", "Service Virtualization", "Continuous Integration", "Regression Testing", "Integration Testing", "User Interface Testing", "Browser Compatibility Testing", "Exploratory Testing", "Risk-based Testing", "User Acceptance Testing"], "cloud_platforms": ["AWS", "GCP"], "devops_tools": ["<PERSON>", "GitLab"], "other": ["<PERSON><PERSON>", "TestNG", "Appium", "SeeTest", "CA DevTest", "UFT", "LeanFT", "Bitbucket", "DB2", "CICS", "JCL", "VSAM", "COBOL", "Sufi", "File-Aid", "Agile", "OAuth", "SSO", "SOAP", "Postman", "HP ALM", "Confluence", "Mobile Testing", "UI Testing", "Database Testing", "API Testing", "ETL Testing", "Business Intelligence", "CTI Testing", "Continuous Deployment", "Functional Testing", "System Testing", "End-to-End Testing", "C", "COBOL"]}, "Laxman_Gite": {"programming_languages": ["C#", "ASP.NET Core", "Azure Developer", "Azure Functions", "Service Bus", "Azure Storage", "<PERSON><PERSON>", "Azure AD", "Virtual Network", "<PERSON>er", "Microservices", "Angular", "Azure Monitor", "j<PERSON><PERSON><PERSON>", "Event Grid", "Azure Container Registry", "Serverless Architecture", "Microsoft Azure", "Amazon Web Services (AWS)", "Azure Service Fabric", "Kubernetes", "SQL Server", "Stored Procedures", "Triggers", "Software Architecture", "High-Performance Architecture Design", "Federated Database Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Query Performance Optimization", "Hybrid Solution Architecture", "Direct Connect", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional"], "web_technologies": ["HTML", "CSS"], "databases": ["MySQL", "T-SQL", "PL/SQL"], "devops_tools": ["CI/CD"], "frameworks": [".NET 6", "ASP.NET MVC"], "other": ["Logic Apps", "API Management", "Cosmos DB", "YAML Pipelines", "Web API", "Application Insights", "Log Analytics", "<PERSON>", "Event Hub", "Snowflake", "Functions", "Data Modeling", "Data Analytics", "VPC Design", "VPN", "Agile Methodology"]}, "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": {"programming_languages": ["C#", "ASP.NET Core", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "Azure APIM", "Azure Service Bus", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "Angular", "Angular Reactive Forms", "SQL Server", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "PostgreSQL", "Oracle PL/SQL", "DevExpress", "MS SharePoint Server"], "databases": ["T-SQL", "SQLite", "MySQL"], "devops_tools": ["GitHub"], "frameworks": ["ADO.NET"], "other": ["Web API", "WPF", "MVC", "WCF", "App Insights", "Logic Apps", "LINQ", "Stimulsoft", "HttpClient", "NUnit", "Coded UI", "MS Access", "InstallShield", "TFS", "SVN", "Apache Tomcat", "IIS"]}, "Vivek Anil .Net lead": {"programming_languages": ["ASP.NET Core", ".NET Framework", "C#", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Azure DevOps", "Azure Service Bus", "Pivotal Cloud Foundry", "React", "Swagger", "SOLID principles", "Design patterns", "Team Foundation Server (TFS)", "<PERSON><PERSON>", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "C++", "Python"], "web_technologies": ["Node.js", "HTML"], "devops_tools": ["Git", "GitHub", "CI/CD"], "frameworks": ["ASP.NET", "ADO.NET"], "other": ["CosmosDB", "Apache Kafka", "ActiveMQ", "Web API", "OAuth2", "Open API", "OOPS", "SVN", "NUnit", "Moq", "Agile Methodologies", "WCF"]}, "PunniyaKodi V updated resume": {"programming_languages": ["C#", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Windows Services", "j<PERSON><PERSON><PERSON>", "AngularJS", "Angular", "React", "SQL Server", "XML Web Services", ".NET Core Apps", "Entity Framework", "AGGrid", "txText Control", "JavaScript", "Bootstrap", "TypeScript", "Terraform", "OpenSearch", "PostgreSQL", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "Azure DevOps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "Amazon Web Services (AWS)", "CloudFront", "RDS", "ElasticSearch", "OpenTelemetry", "FullStory", "Google Analytics"], "web_technologies": ["HTML", "HTML5", "CSS", "Sass"], "databases": ["SQL", "DynamoDB", "PL/SQL"], "cloud_platforms": ["EC2"], "frameworks": ["ASP.NET MVC", "ASP.NET", "VB.NET", "ADO.NET"], "other": ["Web API", "LINQ", "WCF", "AJAX", "NodeJS", "JSON", "SOAP", "SQS", "SNS", "SSIS", "TFS", "YAML", "ASPX", "IAM", "ECS", "API Gateway", "CloudWatch", "Step Functions", "El<PERSON>", "Elastic APM"]}, "Chary": {"programming_languages": ["ASP.NET Core", "C#", "Java", "SOLID Principles", "Web Services", "Microservices", "REST", "Angular", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework", "Azure DevOps", "<PERSON>er", "Kubernetes", "Azure Logic Apps", "RabbitMQ", "Azure App Services", "Azure Functions", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "Azure Entra ID", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "CQRS", "Saga Pattern", "Choreography Pattern", "Circuit Breaker <PERSON>", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Repository Pattern", "Dependency Inversion Principle", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS", "Sitecore", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server", "Azure SQL Database", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Object-Oriented Programming (OOP)", "Design Patterns", "Azure Data Lake", "Azure Data Factory", "Python", "Agile (SCRUM)", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "Internet of Things (IoT)"], "databases": ["Amazon DynamoDB"], "cloud_platforms": ["Amazon EC2", "AWS Lambda"], "devops_tools": ["CI/CD Pipeline"], "frameworks": ["ASP.NET MVC", "ASP.NET", ".NET MAUI"], "other": ["XAML", "WCF", "Web API", "SOAP", "<PERSON><PERSON><PERSON><PERSON>", "Kendo UI", "Web Jobs", "OAuth 2.0", "OKTA", "Bitbucket", "Visual Studio", "API Gateway", "MuleSoft", "Kafka", "Tibco", "Dependency Injection", "SEO Optimization", "SSIS", "Data Modeling", "Selenium", "PMP", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304"]}, "Donish Devasahayam_DotNET": {"programming_languages": ["C#", ".NET Core", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "gRPC", "Entity Framework", "Lambda Expressions", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Azure SQL", "Azure Container Registry (ACR)", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "React", "Redux", "Blazor", "MudBlazor", "Telerik", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "Rally", "Blue Yonder"], "databases": ["LINQ to SQL", "NoSQL"], "cloud_platforms": ["S3 (Amazon S3)"], "frameworks": [".NET", "ASP.NET", "ADO.NET"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Elastic Beanstalk", "LINQ", "LINQ to Objects", "Cosmos DB", "DB2", "Kendo UI", "Kafka", "Datadog", "SAP", "IDoc", "Logility"]}, "Kondaru_04_Manjunath_Resume": {"programming_languages": ["Amazon Web Services (AWS)", "CloudFormation", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "Waterfall", "Shell Scripting", "<PERSON>er", "Windows Server", "<PERSON><PERSON>", "PowerShell", "WebSphere", "Elastic Load Balancers", "VMware", "<PERSON><PERSON>", "Disaster Recovery"], "cloud_platforms": ["EC2"], "devops_tools": ["<PERSON>", "GitHub", "Ansible", "CI/CD", "Git"], "other": ["VPC", "IAM", "Agile", "ANT", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "Linux", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Multi-AZ", "High Availability"]}, "Jhansi P": {"programming_languages": ["Amazon Web Services (AWS)", "Azure", "Amazon RDS", "Amazon Route 53", "AWS CloudFormation", "Amazon CloudFront", "Python", "Java", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "Groovy", "Subversion (SVN)", "Elasticsearch", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)"], "databases": ["Amazon DynamoDB", "MySQL"], "cloud_platforms": ["Amazon EC2", "Amazon S3", "AWS Auto Scaling", "AWS Lambda", "AWS CLI", "AWS-Kops (EKS)"], "devops_tools": ["Ansible", "<PERSON>", "Git", "GitHub", "GitLab", "CI/CD"], "other": ["Amazon ECS", "Elastic Beanstalk", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon IAM", "Amazon CloudWatch", "<PERSON><PERSON>", "Ant", "<PERSON><PERSON>", "Tomcat", "WebLogic", "<PERSON><PERSON><PERSON><PERSON>", "HTTP", "TLS"]}, "Puneet": {"programming_languages": ["Java", "Scrum", "<PERSON><PERSON>", "Microsoft Project", "SmartSheet", "SonarQube", "Warehouse Management", "Windows Application Migration", "RACI Matrix"], "devops_tools": ["<PERSON>", "CI/CD"], "other": ["J2EE", "Agile", "SAFe", "Ka<PERSON><PERSON>", "Confluence", "DevOps", "CMMI Level 5", "SAP", "OTT", "PMP", "PSM"]}, "Pradeep_Project_manager_Nithin1": {"programming_languages": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Delivery Management", "Risk Management", "Stakeholder Management", "Resource Management", "Release Management", "<PERSON><PERSON>", "Azure DevOps", "ServiceNow", "Microsoft Excel", "Angular", "Azure Cloud", "Ezetrieves", "C#"], "web_technologies": ["Node.js"], "databases": ["SQL"], "frameworks": [".NET"], "other": ["Cost Analysis", "Client Management", "Confluence", "Cobol", "IBM BMP", "CMMI Level 5", "ISO 27001"]}, "Kamal": {"programming_languages": ["Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Fivetran", "Streamset", "Snowpark", "Python", "Stored Procedures", "Data Encryption", "Data Decryption", "Data Governance", "PySpark", "Apache Airflow", "Informatica PowerCenter", "Oracle", "MS SQL Server", "Avro", "Pa<PERSON><PERSON>", "Data Warehousing", "Data Architecture", "Data Integration", "Real-time Data Ingestion", "SQR 6.0", "Data Migration", "Performance Tuning", "Query Optimization"], "databases": ["Snow SQL", "SQL"], "cloud_platforms": ["AWS", "S3", "AWS Glue", "EC2"], "devops_tools": ["Git", "GitHub"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Snowflake", "DBT", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "Column <PERSON>", "Data Masking", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "Kafka", "Sigma", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "DB2", "CSV", "OLTP", "OLAP", "ELT", "ETL", "Data Modeling", "Data Quality", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "Business Intelligence", "Index Design"]}, "Aiswarya Sukumaran Data analyst": {"programming_languages": ["ETL Processes", "Python", "PostgreSQL", "Data Warehousing", "Power BI", "Regression", "Predictive Modeling", "Time Series Forecasting", "Data Transformation", "Power Query", "<PERSON><PERSON>", "SSIS (SQL Server Integration Services)", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "R"], "databases": ["SQL", "MySQL"], "data_analytics": ["<PERSON><PERSON>", "<PERSON><PERSON>", "NumPy"], "other": ["Data Analysis", "Business Intelligence", "Data Management", "Excel", "Data Modeling", "Statistical Analysis", "Hypothesis Testing", "Classification", "Data Cleaning", "Data Automation", "PivotTables", "Agile", "DAX"]}, "Himanshu": {"programming_languages": ["Python", "Oracle PL/SQL", "Informatica PowerCenter", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Amazon Web Services (AWS)", "Amazon Redshift", "Amazon Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "PostgreSQL", "Microsoft Power BI", "SAS Visual Investigator", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Data Warehousing", "Data Integration", "Data Transformation", "Data Migration", "Data Modernization", "Data Enrichment", "Cloud Migration", "Data Governance", "RDBMS", "Star Schema", "Normalization", "Stored Procedures", "Triggers"], "databases": ["SQL"], "cloud_platforms": ["AWS Glue", "Amazon S3", "Amazon EC2", "AWS Lambda"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Amazon Athena", "OBIEE", "SAS Visual Analytics", "ETL", "Data Modeling", "Data Pipelining", "Data Quality", "Data Validation", "Data Visualization", "Agile", "OLAP", "OLTP", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Functions", "Neb<PERSON>"]}, "DA manager Nithin": {"programming_languages": ["Microsoft Excel", "Python", "Power BI", "Data Security", "Data Warehousing", "Risk Management", "Program Management", "Project Planning", "Data Wrangling", "Azure Cloud", "JIRA", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Storage Strategies", "Agile Transformation"], "databases": ["SQL"], "data_analytics": ["<PERSON><PERSON>"], "other": ["Data Analysis", "Business Intelligence", "Advanced Analytics", "Data Visualization", "Data Compliance", "Data Modeling", "Cost Analysis", "ETL", "Visual Studio", "Data Collection", "ISO27001 Compliance"]}, "Raghu": {"programming_languages": ["Mechanical Product Design", "System Integration", "Machined Parts Design", "Design Standardization", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tools Design", "Configuration Management", "Process Management", "SolidWorks", "2D Drawings Review", "CE Marking", "Machinery Directive 2006/42/EC", "Reverse Engineering"], "other": ["Mechanical Design", "Sheet Metal Design", "Component Localization", "Cost Optimization", "Design Calculations", "UG-NX", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack up analysis", "ASME Y14.5", "MathCAD", "DNVGL", "EN-13155", "EN ISO 50308", "EN ISO 14122"]}, "Karnati": {"programming_languages": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Oracle", "Terada<PERSON>", "Python", "Databricks", "Spark", "Data Warehousing", "Data Integration", "Data Profiling", "Cloud Data Governance", "Unix Shell Scripting", "Star Schema", "Data Capture (CDC)", "ICRT", "Technical Architecture", "Data Migration", "Production Support", "Pyspark", "Real-time Data Integration"], "databases": ["SQL"], "cloud_platforms": ["AWS"], "other": ["Intelligent Data Management Cloud (IDMC)", "DB2", "Netezza", "Snowflake", "Hive", "Unix", "Windows", "ETL", "Data Quality", "Business 360 Console", "Cloud Data Catalog", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "JSON", "API", "ICS", "<PERSON><PERSON>", "Data Modeling", "Technical Design", "Big Data"]}, "Shashindra": {"programming_languages": ["React", "ReactJS", "Redux Toolkit", "SWR", "<PERSON><PERSON>", "React Router", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "PHP", "Amazon Web Services (S3, EC2, Lambda)", "Azure", "REST", "<PERSON><PERSON><PERSON>", "Scrum", "Silverlight", "RBAC"], "web_technologies": ["Webpack", "HTML5", "CSS3", "Tailwind CSS"], "databases": ["MySQL"], "devops_tools": ["GitHub", "GitHub Copilot"], "mobile": ["A<PERSON>os"], "other": ["ES6", "NodeJS", "SOAP", "JSON", "Gulp", "SVN", "Agile", "JWT"]}, "Upendra": {"programming_languages": ["Java", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "Servlets", "JAX-RS", "Java Mail", "Angular", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "MongoDB", "Quartz", "Hibernate", "Spring JPA", "WebSphere", "RDBMS", "AWS Aurora Postgres"], "web_technologies": ["Node.js", "HTML", "CSS"], "databases": ["SQL"], "cloud_platforms": ["Amazon S3", "Amazon EC2"], "devops_tools": ["Git", "<PERSON>"], "other": ["J2EE", "JSF", "Apache POI", "iText", "JSP", "JDBC", "JAX-WS", "JMS", "JUnit", "Ant", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon EKS", "AJAX", "<PERSON><PERSON>", "SVN", "Bitbucket", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "Putty", "WinSCP", "Bamboo"]}, "Chandra_Resume": {"programming_languages": ["Java", "JavaScript", "Python", "Servlets", "REST", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "Cypress", "Resin", "Oracle 11g/12c", "PostgreSQL", "MongoDB", "JDeveloper", "ERwin Data Modeler", "JMS Hermes", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "Typescript", "Dynatrace", "SiteMinder", "Harvest", "Nx Monorepo", "<PERSON><PERSON>"], "web_technologies": ["Node.js"], "databases": ["SQL", "MySQL", "DynamoDB"], "devops_tools": ["Git", "GitLab", "<PERSON>", "CI/CD", "GitLab Pipelines"], "other": ["JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "JPA", "AJAX", "NestJS", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "IBM DB2", "Eclipse", "NetBeans", "IntelliJ", "MyEclipse", "VS Code", "Toad", "Visio", "UML", "CVS", "SVN", "SoapUI", "JUnit", "Log4j", "Ant", "<PERSON><PERSON>", "Agile", "ITIL Foundation", "LDAP", "SAML", "Bitbucket", "Single Page Application (SPA)", "OOAD", "SOA", "@task", "TFS"]}, "KRISHNA_KANT_NIRALA_Oracle_DBA": {"programming_languages": ["Oracle DBA", "Oracle OCI", "Oracle 19c Data Guard", "Oracle 19c RAC", "Oracle 21c", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Cloud Infrastructure", "Oracle Data Guard", "V<PERSON>am Backup and Recovery", "RMAN", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle TDE", "Oracle Export/Import", "Transportable Tablespaces", "Linux Shell Scripting", "Crontab", "AWR", "SQLTrace", "TKProf", "Oracle Real Application Cluster", "SQL Server 2016", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "Object Storage", "IBM Power E980", "IBM Power E850", "Prince2", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "Teradata Certified Administrator (V2R5)"], "databases": ["SQL", "PL/SQL"], "other": ["IBM LTO 9", "IBM LTO 8", "Data Pump", "ADDM", "Explain Plan", "Statspack", "WebLogic 14c", "WebLogic 12c", "Tomcat", "Glassfish", "JDK", "High Availability", "IAM", "VCN", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "<PERSON>adata", "Exadata X9M-2", "HP", "ITIL V3 Foundation", "ISO/IEC 27001", "ISO 20000", "ISO 27000"]}, "Akhila D": {"programming_languages": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Java", "JavaScript", "REST", "Scrum", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "Waterfall", "E-Commerce", "Insurance", "Queue Processors", "Decision Rules", "Declarative Rules", "Process Flows", "Screen Flows", "Data Transforms", "Rule Resolution", "Enterprise Class Structure", "Code review", "Document review", "WebSphere", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)"], "web_technologies": ["CSS", "HTML"], "other": ["Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "SOAP", "Agile methodology", "Sections", "Flow Actions", "List-View", "PLA", "SDLC", "Agents", "Application Design", "Case Management", "Data Modeling", "Activities", "Dev Studio", "App Studio", "Admin Studio", "CDH", "XML", "Postman"]}, "Uday": {"programming_languages": ["<PERSON><PERSON>", "Fiori Elements", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "JavaScript", "RICEF", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BP Integrations", "User Exits", "Customer Exits", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "SAP Fiori List Report Application", "SAP Fiori Launchpad", "JIRA", "SAP Security", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "FI-AR", "RTR", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)"], "web_technologies": ["HTML5"], "devops_tools": ["GitHub"], "other": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "PI/PO", "AIF", "BTP", "CAPM", "XML", "JSON", "BADIs", "BDC", "BAPI", "Enhancement Points", "ALE IDOCs", "CDS Views", "AMDP", "Web IDE", "BSP", "Business Objects (BO)", "ATC", "SPDD", "SPAU", "PFTC", "Quality Management (QM)", "FI-AP", "FI-GL (FICO)", "SCM"]}, "Updated_CV_-_Tauqeer_Ahmad1_1": {"programming_languages": ["TypeScript", "React", "Angular", "REST APIs", "GraphQL APIs", "Serverless Architecture", "MySQL Aurora", "Apache ECharts", "Amazon Web Services", "Microservices", "Salesforce"], "web_technologies": ["Node.js"], "cloud_platforms": ["AWS SAM", "AWS CDK", "AWS Lambda"], "devops_tools": ["CI/CD"], "other": ["Next.js", "NestJS", "Cognito", "Okta", "OIDC", "Mantine UI", "Vite", "API Gateway", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "CDL"]}, "Ajeesh_resume": {"programming_languages": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Talwar Simulator", "Ethernet", "Swift", "ClearCase", "ios-xe asr 1K router", "C++", "OpenWRT", "Qualcomm SDX hardware", "AT interfaces", "Shell Scripting", "AT&T Echo controller", "POLARIS", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filter configuration"], "devops_tools": ["GIT"], "other": ["Cisco Access Points", "WiFi", "802.11", "WLAN", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "SVN", "Cisco catalyst 3750 Switch", "C", "Linux", "QMI", "Ubus", "XML", "GDB"]}, "Vidwaan_vidwan_resume": {"programming_languages": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "ReactJS", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "Amazon Web Services (AWS)", "Kubernetes", "<PERSON>er", "PostgreSQL", "Spark SQL", "Design Patterns", "Data Structures", "Machine Learning", "Test Driven Development (TDD)", "IAM Role Management", "Server-Side Encryption", "BottleRocket"], "web_technologies": ["HTML", "CSS"], "databases": ["SQL", "DynamoDB", "MySQL"], "cloud_platforms": ["Lambda", "EC2", "S3", "AWS CDK", "AWS Glue"], "devops_tools": ["<PERSON>", "CI/CD", "Git"], "other": ["SQS", "SNS", "Agile", "Postman", "JUnit", "<PERSON><PERSON><PERSON>", "CloudWatch", "Step Functions", "Athena", "JDK 8", "JDK 17", "EKS"]}, "Soham_Resume_Java": {"programming_languages": ["Java", "Spring Boot", "Hibernate", "Python", "JavaScript", "TypeScript", "React.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Azure", "Google Cloud Platform (GCP)", "JIRA", "<PERSON>er", "Microservices Architecture", "REST APIs", "Power BI", "Android Studio", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "web_technologies": ["HTML5", "CSS3"], "databases": ["SQL", "MySQL"], "devops_tools": ["Git", "<PERSON>", "CI/CD"], "data_analytics": ["<PERSON><PERSON>"], "other": ["JUnit", "<PERSON><PERSON><PERSON>", "Apache Kafka", "SOAP", "JSON", "Bluetooth"]}}