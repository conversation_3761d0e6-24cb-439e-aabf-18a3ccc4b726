import os
import json
import logging
import google.generativeai as genai
from typing import List, Dict, Tu<PERSON>, Optional
from resume_parser import ResumeParser
from skill_repository import SkillRepository

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAIConsultantMatcher:
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Enhanced AI Consultant Matcher with Gemini Pro
        This version extracts skills from actual resume content and matches against JD

        Args:
            api_key: Gemini API key. If None, will try to load from environment or config
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("Gemini API key not found. Please set GEMINI_API_KEY environment variable or add to config.json")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Initialize resume parser and skill repository for enhanced matching
        self.resume_parser = ResumeParser()
        self.skill_repository = SkillRepository()

        # Cache for resume content and extracted skills to avoid re-processing
        self.resume_cache = {}
        self.skills_cache = {}

        logger.info("Enhanced AI Consultant Matcher initialized with resume analysis capabilities")

    def _load_api_key(self) -> Optional[str]:
        """Load API key from environment variable or config file"""
        # Try environment variable first
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            logger.info("Loaded Gemini API key from environment variable")
            return api_key

        # Try config.json file
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    api_key = config.get('gemini_api_key')
                    if api_key:
                        logger.info("Loaded Gemini API key from config.json")
                        return api_key
        except Exception as e:
            logger.warning(f"Error loading config.json: {e}")

        logger.warning("Gemini API key not found in environment or config")
        return None

    def extract_skills_from_resume(self, resume_path: str) -> List[str]:
        """
        Extract skills from resume using AI analysis of the actual resume content

        Args:
            resume_path: Path to the resume file

        Returns:
            List of extracted skills
        """
        try:
            # Check cache first
            if resume_path in self.skills_cache:
                logger.info(f"Using cached skills for {resume_path}")
                return self.skills_cache[resume_path]

            # Extract text from resume
            if resume_path.lower().endswith('.pdf'):
                resume_text = self.resume_parser.extract_text_from_pdf(resume_path)
            elif resume_path.lower().endswith('.docx'):
                resume_text = self.resume_parser.extract_text_from_docx(resume_path)
            else:
                logger.warning(f"Unsupported file format: {resume_path}")
                return []

            if not resume_text:
                logger.warning(f"No text extracted from {resume_path}")
                return []

            # Use AI to extract skills from resume content
            skills_extraction_prompt = f"""
            Analyze the following resume content and extract all technical skills, technologies, programming languages, frameworks, tools, and certifications mentioned.

            Resume Content:
            {resume_text[:4000]}  # Limit to avoid token limits

            Please return a JSON list of skills in this exact format:
            {{
                "skills": ["skill1", "skill2", "skill3", ...]
            }}

            Instructions:
            - Extract only technical skills, technologies, and tools
            - Include programming languages, frameworks, databases, cloud platforms, etc.
            - Normalize skill names (e.g., "JavaScript" not "JS", "Amazon Web Services" not "AWS")
            - Remove duplicates and generic terms like "software development"
            - Focus on specific, searchable technical terms
            - Include certifications if mentioned
            """

            response = self.model.generate_content(skills_extraction_prompt)

            if not response or not response.text:
                logger.error(f"Empty response from AI for skills extraction: {resume_path}")
                return []

            # Parse the AI response
            try:
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                skills_data = json.loads(response_text)
                extracted_skills = skills_data.get('skills', [])

                # Cache the results
                self.skills_cache[resume_path] = extracted_skills

                # Store skills in repository for future use
                consultant_name = os.path.splitext(os.path.basename(resume_path))[0]
                for skill in extracted_skills:
                    self.skill_repository.add_skill(skill, consultant_name)

                logger.info(f"Extracted {len(extracted_skills)} skills from {resume_path}")
                return extracted_skills

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse skills extraction response: {e}")
                return []

        except Exception as e:
            logger.error(f"Error extracting skills from resume {resume_path}: {e}")
            return []

    def get_resume_content(self, resume_path: str) -> str:
        """
        Get the full content of a resume for detailed matching

        Args:
            resume_path: Path to the resume file

        Returns:
            Resume content as text
        """
        try:
            # Check cache first
            if resume_path in self.resume_cache:
                return self.resume_cache[resume_path]

            # Extract text from resume
            if resume_path.lower().endswith('.pdf'):
                resume_text = self.resume_parser.extract_text_from_pdf(resume_path)
            elif resume_path.lower().endswith('.docx'):
                resume_text = self.resume_parser.extract_text_from_docx(resume_path)
            else:
                logger.warning(f"Unsupported file format: {resume_path}")
                return ""

            # Cache the content
            self.resume_cache[resume_path] = resume_text
            return resume_text

        except Exception as e:
            logger.error(f"Error getting resume content from {resume_path}: {e}")
            return ""

    def _format_consultant_data_with_resume_analysis(self, consultants: List[Dict]) -> str:
        """
        Format consultant data with actual resume content analysis for AI matching

        Args:
            consultants: List of consultant dictionaries

        Returns:
            Formatted consultant data string with resume analysis
        """
        if not consultants:
            return "No consultants available."

        formatted_consultants = []
        for i, consultant in enumerate(consultants, 1):
            name = consultant.get('name', 'Unknown')
            experience = consultant.get('experience', consultant.get('years_experience', ''))
            location = consultant.get('location', '')
            visa = consultant.get('visa', consultant.get('visa_status', ''))
            relocation = consultant.get('relocation', '')
            resume_path = consultant.get('resume_path', '')

            # Get actual skills from resume if available
            actual_skills = []
            resume_summary = ""

            if resume_path and os.path.exists(resume_path):
                # Extract skills from actual resume
                actual_skills = self.extract_skills_from_resume(resume_path)

                # Get resume content for better matching
                resume_content = self.get_resume_content(resume_path)
                if resume_content:
                    # Create a summary of the resume (first 500 characters)
                    resume_summary = resume_content[:500] + "..." if len(resume_content) > 500 else resume_content

            # Format skills list
            skills_text = ", ".join(actual_skills) if actual_skills else consultant.get('skills', 'No skills extracted')

            consultant_info = f"""
Consultant #{i}:
- Name: {name}
- Extracted Skills from Resume: {skills_text}
- Experience: {experience} years
- Location: {location}
- Visa Status: {visa}
- Willing to Relocate: {relocation}
- Resume Summary: {resume_summary}
"""
            formatted_consultants.append(consultant_info.strip())

        return "\n\n".join(formatted_consultants)

    def _create_enhanced_matching_prompt(self, job_description: str, consultant_data: str) -> str:
        """
        Create an enhanced prompt that matches JD against actual resume content

        Args:
            job_description: The job description from email
            consultant_data: Formatted consultant data with resume analysis

        Returns:
            Enhanced matching prompt
        """
        prompt = f"""
You are an expert technical recruiter with deep knowledge of technology skills and job requirements. Your task is to:

1. Carefully analyze the job description to understand ALL technical requirements, skills, and qualifications
2. Match consultants based on their ACTUAL resume content and extracted skills (not just CSV data)
3. Consider skill relevance, experience level, location, visa status, and overall fit
4. Generate a professional, compelling email response explaining the match

Job Description:
{job_description}

Available Consultants (with actual resume analysis):
{consultant_data}

Matching Criteria:
- Prioritize consultants whose extracted skills closely match the job requirements
- Consider both exact skill matches and related/transferable skills
- Factor in years of experience relative to job seniority level
- Consider location and visa status requirements
- Look for evidence of relevant project experience in resume summaries

Please respond in the following JSON format:
{{
    "selected_consultant_name": "Name of the best matching consultant",
    "match_confidence": "High/Medium/Low",
    "key_matching_skills": ["skill1", "skill2", "skill3"],
    "skill_match_percentage": 85,
    "match_reasoning": "Brief explanation of why this consultant is the best match",
    "email_message": "Professional email message explaining why this consultant is an excellent fit. Highlight specific skills from their resume that match the job requirements."
}}

Important:
- Base your decision on ACTUAL extracted skills from resumes, not CSV data
- Only return valid JSON
- The email_message should be professional and ready to send to a client
- Mention specific skills that were found in the consultant's resume
- Keep the message between 150-250 words
- Include confidence level and reasoning for transparency
"""
        return prompt

    def enhanced_match_consultant_to_job(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """
        Enhanced matching that analyzes actual resume content against job description

        Args:
            job_description: The job description from the email
            consultants: List of consultant dictionaries with resume paths

        Returns:
            Tuple of (consultant_name, custom_message, full_ai_response)
        """
        try:
            if not consultants:
                logger.warning("No consultants provided for enhanced matching")
                return None, None, None

            logger.info(f"Starting enhanced AI matching for {len(consultants)} consultants")

            # Format consultant data with resume analysis
            consultant_data = self._format_consultant_data_with_resume_analysis(consultants)

            # Create enhanced matching prompt
            prompt = self._create_enhanced_matching_prompt(job_description, consultant_data)

            logger.info("Sending enhanced matching request to Gemini AI")

            # Generate response using Gemini
            response = self.model.generate_content(prompt)

            if not response or not response.text:
                logger.error("Empty response from Gemini AI for enhanced matching")
                return None, None, None

            # Parse JSON response
            try:
                # Clean the response text
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                ai_response = json.loads(response_text)

                consultant_name = ai_response.get('selected_consultant_name')
                email_message = ai_response.get('email_message')
                match_confidence = ai_response.get('match_confidence', 'Unknown')
                key_skills = ai_response.get('key_matching_skills', [])
                skill_match_percentage = ai_response.get('skill_match_percentage', 0)
                match_reasoning = ai_response.get('match_reasoning', '')

                logger.info(f"Enhanced AI selected: {consultant_name}")
                logger.info(f"Match confidence: {match_confidence}")
                logger.info(f"Skill match percentage: {skill_match_percentage}%")
                logger.info(f"Key matching skills: {', '.join(key_skills)}")
                logger.info(f"Match reasoning: {match_reasoning}")

                return consultant_name, email_message, ai_response

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse enhanced matching response as JSON: {e}")
                logger.error(f"Raw response: {response.text}")
                return None, None, None

        except Exception as e:
            logger.error(f"Error in enhanced AI consultant matching: {e}")
            return None, None, None

    def test_connection(self) -> bool:
        """Test the connection to Gemini AI"""
        try:
            test_prompt = "Hello, please respond with 'Enhanced AI Matcher connection successful'"
            response = self.model.generate_content(test_prompt)

            if response and response.text:
                logger.info("Enhanced AI Matcher connection test successful")
                return True
            else:
                logger.error("Enhanced AI Matcher connection test failed - empty response")
                return False

        except Exception as e:
            logger.error(f"Enhanced AI Matcher connection test failed: {e}")
            return False

    def bulk_extract_skills_from_resumes(self, consultants: List[Dict]) -> Dict[str, List[str]]:
        """
        Extract skills from all consultant resumes in bulk and store for future use

        Args:
            consultants: List of consultant dictionaries with resume paths

        Returns:
            Dictionary mapping consultant names to their extracted skills
        """
        skills_by_consultant = {}

        logger.info(f"Starting bulk skill extraction for {len(consultants)} consultants")

        for consultant in consultants:
            name = consultant.get('name', 'Unknown')
            resume_path = consultant.get('resume_path', '')

            if resume_path and os.path.exists(resume_path):
                logger.info(f"Extracting skills for {name} from {resume_path}")
                extracted_skills = self.extract_skills_from_resume(resume_path)
                skills_by_consultant[name] = extracted_skills

                # Update consultant data with extracted skills
                consultant['extracted_skills'] = extracted_skills
                consultant['skill_count'] = len(extracted_skills)
            else:
                logger.warning(f"No resume file found for {name}")
                skills_by_consultant[name] = []

        logger.info(f"Completed bulk skill extraction for {len(skills_by_consultant)} consultants")
        return skills_by_consultant

    def save_extracted_skills_to_repository(self, skills_by_consultant: Dict[str, List[str]]):
        """
        Save all extracted skills to the skill repository for future use

        Args:
            skills_by_consultant: Dictionary mapping consultant names to skills
        """
        logger.info("Saving extracted skills to repository")

        for consultant_name, skills in skills_by_consultant.items():
            if skills:
                # Add consultant skills to repository
                self.skill_repository.add_consultant_skills(consultant_name, skills)
                logger.info(f"Saved {len(skills)} skills for {consultant_name}")

        logger.info("Completed saving skills to repository")

# Example usage and testing
if __name__ == "__main__":
    # Test the enhanced AI matcher
    try:
        matcher = EnhancedAIConsultantMatcher()

        # Test connection
        if matcher.test_connection():
            print("✅ Enhanced AI Matcher initialized successfully")

            # Test skill extraction with a sample resume (if available)
            sample_consultants = [
                {
                    'name': 'Test Consultant',
                    'resume_path': 'resumes/sample_resume.pdf',  # Replace with actual path
                    'experience': '5',
                    'location': 'New York',
                    'visa_status': 'H1B',
                    'relocation': 'Yes'
                }
            ]

            # Test bulk skill extraction
            print("Testing bulk skill extraction...")
            skills_result = matcher.bulk_extract_skills_from_resumes(sample_consultants)
            print(f"Extracted skills: {skills_result}")

        else:
            print("❌ Enhanced AI Matcher connection failed")

    except Exception as e:
        print(f"❌ Error initializing Enhanced AI Matcher: {e}")
