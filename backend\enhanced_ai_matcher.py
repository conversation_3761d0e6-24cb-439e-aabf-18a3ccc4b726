import os
import json
import logging
import hashlib
import datetime
import re
import random
from typing import List, Dict, Tuple, Optional
import google.generativeai as genai
from resume_parser import ResumeParser
from skill_repository import SkillRepository

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAIConsultantMatcher:
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Enhanced AI Consultant Matcher with Gemini Pro
        This version extracts skills from actual resume content and matches against JD

        Args:
            api_key: Gemini API key. If None, will try to load from environment or config
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("Gemini API key not found. Please set GEMINI_API_KEY environment variable or add to config.json")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Initialize resume parser and skill repository for enhanced matching
        self.resume_parser = ResumeParser()
        self.skill_repository = SkillRepository()

        # Cache for resume content and extracted skills to avoid re-processing
        self.resume_cache = {}
        self.skills_cache = {}

        # Phase 2 enhancements
        self.jd_cache = {}  # Cache for JD matching results
        self.jd_history = {}  # Track JD evolution
        self.match_log = []  # Log all match decisions
        self.skill_categories = {}  # Categorized skills

        # Load existing caches and logs
        self._load_jd_cache()
        self._load_match_log()
        self._load_skill_categories()

        logger.info("Enhanced AI Consultant Matcher initialized with Phase 2 features: JD caching, evolution tracking, multi-profile support, skill clustering")

    def _load_api_key(self) -> Optional[str]:
        """Load API key from environment variable or config file"""
        # Try environment variable first
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            logger.info("Loaded Gemini API key from environment variable")
            return api_key

        # Try config.json file
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    api_key = config.get('gemini_api_key')
                    if api_key:
                        logger.info("Loaded Gemini API key from config.json")
                        return api_key
        except Exception as e:
            logger.warning(f"Error loading config.json: {e}")

        logger.warning("Gemini API key not found in environment or config")
        return None

    # Phase 2: JD Caching and Evolution Tracking
    def _load_jd_cache(self):
        """Load JD cache from file"""
        try:
            cache_path = os.path.join(os.path.dirname(__file__), '..', 'jd_cache.json')
            if os.path.exists(cache_path):
                with open(cache_path, 'r') as f:
                    self.jd_cache = json.load(f)
                logger.info(f"Loaded {len(self.jd_cache)} cached JD results")
            else:
                self.jd_cache = {}
        except Exception as e:
            logger.error(f"Error loading JD cache: {e}")
            self.jd_cache = {}

    def _save_jd_cache(self):
        """Save JD cache to file"""
        try:
            cache_path = os.path.join(os.path.dirname(__file__), '..', 'jd_cache.json')
            with open(cache_path, 'w') as f:
                json.dump(self.jd_cache, f, indent=2)
            logger.info(f"Saved {len(self.jd_cache)} JD cache entries")
        except Exception as e:
            logger.error(f"Error saving JD cache: {e}")

    def clear_jd_cache(self):
        """Clear the JD cache to force fresh matching"""
        self.jd_cache = {}
        self._save_jd_cache()
        logger.info("JD cache cleared - forcing fresh matching")

    def _load_match_log(self):
        """Load match log from file"""
        try:
            log_path = os.path.join(os.path.dirname(__file__), '..', 'match_log.json')
            if os.path.exists(log_path):
                with open(log_path, 'r') as f:
                    self.match_log = json.load(f)
                logger.info(f"Loaded {len(self.match_log)} match log entries")
            else:
                self.match_log = []
        except Exception as e:
            logger.error(f"Error loading match log: {e}")
            self.match_log = []

    def _save_match_log(self):
        """Save match log to file"""
        try:
            log_path = os.path.join(os.path.dirname(__file__), '..', 'match_log.json')
            with open(log_path, 'w') as f:
                json.dump(self.match_log, f, indent=2)
            logger.info(f"Saved {len(self.match_log)} match log entries")
        except Exception as e:
            logger.error(f"Error saving match log: {e}")

    def _load_skill_categories(self):
        """Load skill categories from file"""
        try:
            categories_path = os.path.join(os.path.dirname(__file__), '..', 'skill_categories.json')
            if os.path.exists(categories_path):
                with open(categories_path, 'r') as f:
                    self.skill_categories = json.load(f)
                logger.info(f"Loaded skill categories for {len(self.skill_categories)} consultants")
            else:
                self.skill_categories = {}
        except Exception as e:
            logger.error(f"Error loading skill categories: {e}")
            self.skill_categories = {}

    def _save_skill_categories(self):
        """Save skill categories to file"""
        try:
            categories_path = os.path.join(os.path.dirname(__file__), '..', 'skill_categories.json')
            with open(categories_path, 'w') as f:
                json.dump(self.skill_categories, f, indent=2)
            logger.info(f"Saved skill categories for {len(self.skill_categories)} consultants")
        except Exception as e:
            logger.error(f"Error saving skill categories: {e}")

    def _generate_jd_hash(self, job_description: str) -> str:
        """Generate a hash for job description to use as cache key"""
        # Normalize the JD text for consistent hashing
        normalized_jd = re.sub(r'\s+', ' ', job_description.lower().strip())
        return hashlib.md5(normalized_jd.encode()).hexdigest()

    def _log_match_decision(self, jd_hash: str, job_description: str, selected_consultants: List[Dict], reasoning: str, match_type: str = "enhanced"):
        """Log match decision with timestamp and details"""
        log_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "jd_hash": jd_hash,
            "job_description_preview": job_description[:200] + "..." if len(job_description) > 200 else job_description,
            "match_type": match_type,
            "selected_consultants": [
                {
                    "name": c.get("name", "Unknown"),
                    "confidence": c.get("match_confidence", "Unknown"),
                    "skill_match_percentage": c.get("skill_match_percentage", 0),
                    "key_skills": c.get("key_matching_skills", [])
                } for c in selected_consultants
            ],
            "reasoning": reasoning,
            "consultant_count": len(selected_consultants)
        }

        self.match_log.append(log_entry)

        # Keep only last 100 entries to prevent file from growing too large
        if len(self.match_log) > 100:
            self.match_log = self.match_log[-100:]

        self._save_match_log()
        logger.info(f"Logged match decision for JD hash {jd_hash[:8]}... with {len(selected_consultants)} consultants")

    def extract_skills_from_resume(self, resume_path: str) -> List[str]:
        """
        Extract skills from resume using AI analysis of the actual resume content

        Args:
            resume_path: Path to the resume file

        Returns:
            List of extracted skills
        """
        try:
            # Check cache first and return immediately if found
            if resume_path in self.skills_cache:
                logger.info(f"Using cached skills for {resume_path}")
                return self.skills_cache[resume_path]

            # If not in cache, proceed with extraction
            logger.info(f"Extracting skills from {resume_path} (not in cache)")

            # Extract text from resume
            if resume_path.lower().endswith('.pdf'):
                resume_text = self.resume_parser.extract_text_from_pdf(resume_path)
            elif resume_path.lower().endswith('.docx'):
                resume_text = self.resume_parser.extract_text_from_docx(resume_path)
            else:
                logger.warning(f"Unsupported file format: {resume_path}")
                return []

            if not resume_text:
                logger.warning(f"No text extracted from {resume_path}")
                return []

            # Use AI to extract skills from resume content
            skills_extraction_prompt = f"""
            Analyze the following resume content and extract all technical skills, technologies, programming languages, frameworks, tools, and certifications mentioned.

            Resume Content:
            {resume_text[:4000]}  # Limit to avoid token limits

            Please return a JSON list of skills in this exact format:
            {{
                "skills": ["skill1", "skill2", "skill3", ...]
            }}

            Instructions:
            - Extract only technical skills, technologies, and tools
            - Include programming languages, frameworks, databases, cloud platforms, etc.
            - Normalize skill names (e.g., "JavaScript" not "JS", "Amazon Web Services" not "AWS")
            - Remove duplicates and generic terms like "software development"
            - Focus on specific, searchable technical terms
            - Include certifications if mentioned
            """

            response = self.model.generate_content(skills_extraction_prompt)

            if not response or not response.text:
                logger.error(f"Empty response from AI for skills extraction: {resume_path}")
                return []

            # Parse the AI response
            try:
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                skills_data = json.loads(response_text)
                extracted_skills = skills_data.get('skills', [])

                # Cache the results for future use
                self.skills_cache[resume_path] = extracted_skills

                # Store skills in repository for future use
                consultant_name = os.path.splitext(os.path.basename(resume_path))[0]
                for skill in extracted_skills:
                    self.skill_repository.add_skill(skill, consultant_name)

                # Phase 2: Categorize skills
                categorized_skills = self._categorize_skills(extracted_skills)
                self.skill_categories[consultant_name] = categorized_skills
                self._save_skill_categories()

                logger.info(f"Extracted and categorized {len(extracted_skills)} skills from {resume_path}")
                return extracted_skills

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse skills extraction response: {e}")
                return []

        except Exception as e:
            logger.error(f"Error extracting skills from resume {resume_path}: {e}")
            return []

    def get_resume_content(self, resume_path: str) -> str:
        """
        Get the full content of a resume for detailed matching

        Args:
            resume_path: Path to the resume file

        Returns:
            Resume content as text
        """
        try:
            # Check cache first
            if resume_path in self.resume_cache:
                return self.resume_cache[resume_path]

            # Extract text from resume
            if resume_path.lower().endswith('.pdf'):
                resume_text = self.resume_parser.extract_text_from_pdf(resume_path)
            elif resume_path.lower().endswith('.docx'):
                resume_text = self.resume_parser.extract_text_from_docx(resume_path)
            else:
                logger.warning(f"Unsupported file format: {resume_path}")
                return ""

            # Cache the content
            self.resume_cache[resume_path] = resume_text
            return resume_text

        except Exception as e:
            logger.error(f"Error getting resume content from {resume_path}: {e}")
            return ""

    def _categorize_skills(self, skills: List[str]) -> Dict[str, List[str]]:
        """Categorize skills into different technology areas"""
        categories = {
            "programming_languages": [],
            "web_technologies": [],
            "databases": [],
            "cloud_platforms": [],
            "devops_tools": [],
            "frameworks": [],
            "mobile": [],
            "data_analytics": [],
            "other": []
        }

        # Define skill patterns for categorization
        skill_patterns = {
            "programming_languages": [
                "python", "java", "javascript", "c#", "c++", "typescript", "php", "ruby", "go", "rust", "scala", "kotlin", "swift", "r"
            ],
            "web_technologies": [
                "html", "css", "react", "angular", "vue", "node.js", "express", "jquery", "bootstrap", "sass", "webpack"
            ],
            "databases": [
                "sql", "mysql", "postgresql", "mongodb", "redis", "oracle", "sql server", "sqlite", "cassandra", "dynamodb"
            ],
            "cloud_platforms": [
                "aws", "azure", "google cloud", "gcp", "amazon web services", "microsoft azure", "ec2", "s3", "lambda"
            ],
            "devops_tools": [
                "docker", "kubernetes", "jenkins", "git", "terraform", "ansible", "chef", "puppet", "gitlab", "ci/cd"
            ],
            "frameworks": [
                "spring", "django", "flask", "laravel", ".net", "asp.net", "hibernate", "struts", "rails"
            ],
            "mobile": [
                "android", "ios", "react native", "flutter", "xamarin", "ionic", "cordova"
            ],
            "data_analytics": [
                "tableau", "power bi", "spark", "hadoop", "pandas", "numpy", "matplotlib", "tensorflow", "pytorch"
            ]
        }

        for skill in skills:
            skill_lower = skill.lower()
            categorized = False

            for category, patterns in skill_patterns.items():
                if any(pattern in skill_lower for pattern in patterns):
                    categories[category].append(skill)
                    categorized = True
                    break

            if not categorized:
                categories["other"].append(skill)

        # Remove empty categories
        return {k: v for k, v in categories.items() if v}

    def _detect_jd_similarity(self, new_jd: str, cached_jd: str) -> float:
        """Detect similarity between job descriptions using simple text comparison"""
        try:
            # Simple similarity check based on common words
            new_words = set(re.findall(r'\w+', new_jd.lower()))
            cached_words = set(re.findall(r'\w+', cached_jd.lower()))

            if not new_words or not cached_words:
                return 0.0

            intersection = new_words.intersection(cached_words)
            union = new_words.union(cached_words)

            similarity = len(intersection) / len(union) if union else 0.0
            return similarity

        except Exception as e:
            logger.error(f"Error calculating JD similarity: {e}")
            return 0.0

    def _extract_client_filters(self, job_description: str) -> Dict[str, any]:
        """Extract client filters from job description (visa requirements, location, etc.)"""
        filters = {
            "visa_requirements": [],
            "location_requirements": [],
            "experience_requirements": [],
            "citizenship_requirements": []
        }

        jd_lower = job_description.lower()

        # Visa requirements
        if "us citizens only" in jd_lower or "citizens only" in jd_lower:
            filters["citizenship_requirements"].append("US Citizen")
        if "green card" in jd_lower:
            filters["visa_requirements"].append("Green Card")
        if "h1b" in jd_lower:
            filters["visa_requirements"].append("H1B")
        if "no visa sponsorship" in jd_lower:
            filters["visa_requirements"].append("No Sponsorship Required")

        # Location requirements
        location_patterns = [
            r"location[:\s]+([^.\n]+)",
            r"based in[:\s]+([^.\n]+)",
            r"must be located in[:\s]+([^.\n]+)"
        ]

        for pattern in location_patterns:
            matches = re.findall(pattern, jd_lower)
            for match in matches:
                filters["location_requirements"].append(match.strip())

        # Experience requirements
        exp_patterns = [
            r"(\d+)\+?\s*years?\s*(?:of\s*)?experience",
            r"minimum\s*(\d+)\s*years?",
            r"at least\s*(\d+)\s*years?"
        ]

        for pattern in exp_patterns:
            matches = re.findall(pattern, jd_lower)
            for match in matches:
                filters["experience_requirements"].append(int(match))

        return filters

    def _filter_consultants_by_requirements(self, consultants: List[Dict], filters: Dict[str, any]) -> List[Dict]:
        """Filter consultants based on client requirements"""
        filtered_consultants = []

        for consultant in consultants:
            # Check citizenship requirements
            if filters["citizenship_requirements"]:
                visa_status = consultant.get("visa_status", "").lower()
                if "us citizen" in filters["citizenship_requirements"][0].lower():
                    if "citizen" not in visa_status and "gc" not in visa_status and "green card" not in visa_status:
                        logger.info(f"Filtered out {consultant.get('name', 'Unknown')} - citizenship requirement not met")
                        continue

            # Check visa requirements
            if filters["visa_requirements"]:
                visa_status = consultant.get("visa_status", "").lower()
                visa_match = False
                for req in filters["visa_requirements"]:
                    if req.lower() in visa_status:
                        visa_match = True
                        break
                if not visa_match and "No Sponsorship Required" not in filters["visa_requirements"]:
                    logger.info(f"Filtered out {consultant.get('name', 'Unknown')} - visa requirement not met")
                    continue

            # Check experience requirements
            if filters["experience_requirements"]:
                try:
                    consultant_exp = int(consultant.get("experience", "0"))
                    min_exp = min(filters["experience_requirements"])
                    if consultant_exp < min_exp:
                        logger.info(f"Filtered out {consultant.get('name', 'Unknown')} - experience requirement not met ({consultant_exp} < {min_exp})")
                        continue
                except (ValueError, TypeError):
                    # If experience is not a number, skip this filter
                    pass

            filtered_consultants.append(consultant)

        logger.info(f"Filtered {len(consultants)} consultants down to {len(filtered_consultants)} based on client requirements")
        return filtered_consultants

    def _format_consultant_data_with_resume_analysis(self, consultants: List[Dict]) -> str:
        """
        Format consultant data with actual resume content analysis for AI matching

        Args:
            consultants: List of consultant dictionaries

        Returns:
            Formatted consultant data string with resume analysis
        """
        if not consultants:
            return "No consultants available."

        formatted_consultants = []
        for i, consultant in enumerate(consultants, 1):
            name = consultant.get('name', 'Unknown')
            experience = consultant.get('experience', consultant.get('years_experience', ''))
            location = consultant.get('location', '')
            visa = consultant.get('visa', consultant.get('visa_status', ''))
            relocation = consultant.get('relocation', '')
            resume_path = consultant.get('resume_path', '')

            # Get actual skills from resume if available
            actual_skills = []
            resume_summary = ""

            if resume_path and os.path.exists(resume_path):
                # Extract skills from actual resume
                actual_skills = self.extract_skills_from_resume(resume_path)

                # Get resume content for better matching
                resume_content = self.get_resume_content(resume_path)
                if resume_content:
                    # Create a summary of the resume (first 500 characters)
                    resume_summary = resume_content[:500] + "..." if len(resume_content) > 500 else resume_content

            # Format skills list
            skills_text = ", ".join(actual_skills) if actual_skills else consultant.get('skills', 'No skills extracted')

            consultant_info = f"""
Consultant #{i}:
- Name: {name}
- Extracted Skills from Resume: {skills_text}
- Experience: {experience} years
- Location: {location}
- Visa Status: {visa}
- Willing to Relocate: {relocation}
- Resume Summary: {resume_summary}
"""
            formatted_consultants.append(consultant_info.strip())

        return "\n\n".join(formatted_consultants)

    def _create_enhanced_matching_prompt(self, job_description: str, consultant_data: str) -> str:
        """
        Create an enhanced prompt that matches JD against actual resume content

        Args:
            job_description: The job description from email
            consultant_data: Formatted consultant data with resume analysis

        Returns:
            Enhanced matching prompt
        """
        prompt = f"""
You are an expert technical recruiter with deep knowledge of technology skills and job requirements. Your task is to:

1. Carefully analyze the job description to understand ALL technical requirements, skills, and qualifications
2. Match consultants based on their ACTUAL resume content and extracted skills (not just CSV data)
3. Consider skill relevance, experience level, location, visa status, and overall fit
4. Generate a professional, compelling email response explaining the match

Job Description:
{job_description}

Available Consultants (with actual resume analysis):
{consultant_data}

Matching Criteria:
- Prioritize consultants whose extracted skills closely match the job requirements
- Consider both exact skill matches and related/transferable skills
- Factor in years of experience relative to job seniority level
- Consider location and visa status requirements
- Look for evidence of relevant project experience in resume summaries

Please respond in the following JSON format:
{{
    "selected_consultant_name": "Name of the best matching consultant",
    "match_confidence": "High/Medium/Low",
    "key_matching_skills": ["skill1", "skill2", "skill3"],
    "skill_match_percentage": 85,
    "match_reasoning": "Brief explanation of why this consultant is the best match",
    "email_message": "Professional email message explaining why this consultant is an excellent fit. Highlight specific skills from their resume that match the job requirements."
}}

Important:
- Base your decision on ACTUAL extracted skills from resumes, not CSV data
- Only return valid JSON
- The email_message should be professional and ready to send to a client
- Mention specific skills that were found in the consultant's resume
- Keep the message between 150-250 words
- Include confidence level and reasoning for transparency
"""
        return prompt

    def enhanced_match_consultant_to_job(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[List[Dict]], Optional[str], Optional[Dict]]:
        """
        Phase 2 Enhanced matching with caching, evolution tracking, multi-profile support, and smart filtering

        Args:
            job_description: The job description from the email
            consultants: List of consultant dictionaries with resume paths

        Returns:
            Tuple of (selected_consultants_list, custom_message, full_ai_response)
        """
        try:
            if not consultants:
                logger.warning("No consultants provided for enhanced matching")
                return None, None, None

            # Phase 2: Generate JD hash for caching
            jd_hash = self._generate_jd_hash(job_description)
            logger.info(f"Processing JD with hash: {jd_hash[:8]}...")

            # Phase 2: Check cache first
            if jd_hash in self.jd_cache:
                cached_result = self.jd_cache[jd_hash]
                logger.info(f"Found cached result for JD hash {jd_hash[:8]}...")

                # Check if cached result is still valid (consultants haven't changed significantly)
                if self._is_cache_valid(cached_result, consultants):
                    logger.info("Using cached matching result")
                    selected_consultants = cached_result.get("selected_consultants", [])
                    email_message = cached_result.get("email_message", "")
                    ai_response = cached_result.get("ai_response", {})

                    # Log the cached decision
                    self._log_match_decision(jd_hash, job_description, selected_consultants, "Used cached result", "cached")

                    return selected_consultants, email_message, ai_response

            # Phase 2: Extract client filters for email highlighting (but don't filter consultants)
            client_filters = self._extract_client_filters(job_description)
            logger.info(f"Extracted client filters for email highlighting: {client_filters}")

            # Use all consultants for matching (no filtering by visa/location)
            filtered_consultants = consultants.copy()

            # Randomize consultant order to prevent bias
            random.shuffle(filtered_consultants)

            logger.info(f"Starting enhanced AI matching for {len(filtered_consultants)} consultants (visa/location filtering disabled, randomized order)")

            # Format consultant data with resume analysis
            consultant_data = self._format_consultant_data_with_resume_analysis(filtered_consultants)

            # Create enhanced matching prompt for multiple profiles with client filters
            prompt = self._create_enhanced_matching_prompt_multi_profile(job_description, consultant_data, client_filters)

            logger.info("Sending enhanced matching request to Gemini AI")

            # Generate response using Gemini
            response = self.model.generate_content(prompt)

            if not response or not response.text:
                logger.error("Empty response from Gemini AI for enhanced matching")
                return None, None, None

            # Parse JSON response for multiple profiles
            try:
                # Clean the response text
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                ai_response = json.loads(response_text)

                # Phase 2: Handle multiple consultant selection
                selected_consultants = self._process_multi_profile_response(ai_response, filtered_consultants)
                email_message = ai_response.get('email_message', '')
                match_reasoning = ai_response.get('match_reasoning', '')

                if selected_consultants:
                    logger.info(f"Enhanced AI selected {len(selected_consultants)} consultants:")
                    for consultant in selected_consultants:
                        logger.info(f"  - {consultant.get('name', 'Unknown')}: {consultant.get('match_confidence', 'Unknown')} confidence, {consultant.get('skill_match_percentage', 0)}% skill match")

                    # Phase 2: Cache the result (avoid circular references)
                    cache_entry = {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "job_description": job_description[:500] + "..." if len(job_description) > 500 else job_description,
                        "selected_consultant_names": [c.get("name", "Unknown") for c in selected_consultants],
                        "email_message": email_message,
                        "match_reasoning": ai_response.get("match_reasoning", ""),
                        "consultant_count": len(consultants),
                        "match_confidence": [c.get("match_confidence", "Unknown") for c in selected_consultants],
                        "skill_match_percentages": [c.get("skill_match_percentage", 0) for c in selected_consultants]
                    }
                    self.jd_cache[jd_hash] = cache_entry
                    self._save_jd_cache()

                    # Phase 2: Log the decision
                    self._log_match_decision(jd_hash, job_description, selected_consultants, match_reasoning, "enhanced_multi_no_filter")

                    return selected_consultants, email_message, ai_response
                else:
                    logger.warning("No suitable consultants found")
                    return None, None, None

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse enhanced matching response as JSON: {e}")
                logger.error(f"Raw response: {response.text}")
                return None, None, None

        except Exception as e:
            logger.error(f"Error in enhanced AI consultant matching: {e}")
            return None, None, None

    def test_connection(self) -> bool:
        """Test the connection to Gemini AI"""
        try:
            test_prompt = "Hello, please respond with 'Enhanced AI Matcher connection successful'"
            response = self.model.generate_content(test_prompt)

            if response and response.text:
                logger.info("Enhanced AI Matcher connection test successful")
                return True
            else:
                logger.error("Enhanced AI Matcher connection test failed - empty response")
                return False

        except Exception as e:
            logger.error(f"Enhanced AI Matcher connection test failed: {e}")
            return False

    def _is_cache_valid(self, cached_result: Dict, current_consultants: List[Dict]) -> bool:
        """Check if cached result is still valid based on consultant changes"""
        try:
            cached_count = cached_result.get("consultant_count", 0)
            current_count = len(current_consultants)

            # If consultant count changed significantly, invalidate cache
            if abs(cached_count - current_count) > 2:
                logger.info(f"Cache invalid: consultant count changed from {cached_count} to {current_count}")
                return False

            # Check if cache is older than 24 hours
            cached_time = datetime.datetime.fromisoformat(cached_result.get("timestamp", ""))
            age_hours = (datetime.datetime.now() - cached_time).total_seconds() / 3600

            if age_hours > 24:
                logger.info(f"Cache invalid: result is {age_hours:.1f} hours old")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating cache: {e}")
            return False

    def _create_enhanced_matching_prompt_multi_profile(self, job_description: str, consultant_data: str, client_filters: Dict = None) -> str:
        """Create enhanced prompt for multi-profile matching with client filter awareness"""

        # Format client filters for prompt
        filter_info = ""
        if client_filters:
            filter_parts = []
            if client_filters.get("visa_requirements"):
                filter_parts.append(f"Visa Requirements: {', '.join(client_filters['visa_requirements'])}")
            if client_filters.get("citizenship_requirements"):
                filter_parts.append(f"Citizenship Requirements: {', '.join(client_filters['citizenship_requirements'])}")
            if client_filters.get("location_requirements"):
                filter_parts.append(f"Location Requirements: {', '.join(client_filters['location_requirements'])}")
            if client_filters.get("experience_requirements"):
                filter_parts.append(f"Experience Requirements: {min(client_filters['experience_requirements'])}+ years")

            if filter_parts:
                filter_info = f"""
CLIENT REQUIREMENTS DETECTED:
{chr(10).join(f"- {part}" for part in filter_parts)}

NOTE: Include these requirements in your email response to highlight any potential visa/location considerations for the client.
"""

        prompt = f"""
You are an expert technical recruiter. Analyze the job description and find the BEST 1-3 consultants who match the technical requirements.

CRITICAL MATCHING RULES:
1. AVOID BIAS: Do not favor any specific consultant names (like Laxman, Zeeshan, etc.)
2. MATCH SKILLS PRECISELY: Only select consultants whose actual extracted skills align with the job requirements
3. DIVERSIFY SELECTIONS: Consider different consultants for different types of roles
4. TECHNICAL FOCUS: Prioritize technical skill alignment over general experience
5. QUALITY OVER QUANTITY: Better to select 1 perfect match than 3 mediocre ones

SELECTION CRITERIA:
- 80%+ skill match = High confidence (select 1-2 consultants)
- 70-79% skill match = Medium confidence (select 2-3 consultants)
- 60-69% skill match = Low confidence (select 1 consultant with explanation)
- <60% skill match = No match (return empty list)

AVOID THESE COMMON MISTAKES:
- Selecting the same consultants for different job types
- Choosing consultants based on name familiarity
- Ignoring specific technical requirements
- Over-generalizing skill matches

{filter_info}

Job Description:
{job_description}

Available Consultants (with actual resume analysis):
{consultant_data}

Respond in this JSON format:
{{
    "selected_consultants": [
        {{
            "name": "Consultant Name",
            "match_confidence": "High/Medium/Low",
            "skill_match_percentage": 85,
            "key_matching_skills": ["skill1", "skill2", "skill3"],
            "match_reasoning": "Specific technical reasons why this consultant matches the job requirements"
        }}
    ],
    "email_message": "Professional email explaining why these consultants are excellent technical fits for this specific role. Include visa/location considerations if relevant. If multiple consultants, explain each one's unique strengths.",
    "match_reasoning": "Overall reasoning for the technical selection(s) based on job-specific requirements",
    "total_matches": 2
}}

CRITICAL: Only return valid JSON. If no suitable technical matches, use "selected_consultants": [] and explain why in the email_message.
"""
        return prompt

    def _process_multi_profile_response(self, ai_response: Dict, consultants: List[Dict]) -> List[Dict]:
        """Process AI response for multiple consultant profiles"""
        try:
            selected_consultant_data = ai_response.get("selected_consultants", [])

            if not selected_consultant_data:
                return []

            # Find the actual consultant objects and enrich them with AI data
            enriched_consultants = []

            for ai_consultant in selected_consultant_data:
                consultant_name = ai_consultant.get("name", "")

                # Find matching consultant in original list
                matching_consultant = None
                for consultant in consultants:
                    if consultant.get("name", "").lower() == consultant_name.lower():
                        matching_consultant = consultant.copy()
                        break

                if matching_consultant:
                    # Enrich with AI analysis
                    matching_consultant.update({
                        "ai_matched": True,
                        "match_confidence": ai_consultant.get("match_confidence", "Unknown"),
                        "skill_match_percentage": ai_consultant.get("skill_match_percentage", 0),
                        "key_matching_skills": ai_consultant.get("key_matching_skills", []),
                        "match_reasoning": ai_consultant.get("match_reasoning", ""),
                        "ai_selection_rank": len(enriched_consultants) + 1
                    })
                    enriched_consultants.append(matching_consultant)
                else:
                    logger.warning(f"Could not find consultant '{consultant_name}' in original list")

            logger.info(f"Processed {len(enriched_consultants)} consultant profiles from AI response")
            return enriched_consultants

        except Exception as e:
            logger.error(f"Error processing multi-profile response: {e}")
            return []

    # Backward compatibility method
    def match_consultant_and_generate_reply(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """
        Backward compatibility method that returns single consultant for existing code
        """
        try:
            selected_consultants, email_message, ai_response = self.enhanced_match_consultant_to_job(job_description, consultants)

            if selected_consultants and len(selected_consultants) > 0:
                # Return the first (best) consultant for backward compatibility
                best_consultant = selected_consultants[0]
                consultant_name = best_consultant.get("name", "")
                return consultant_name, email_message, ai_response
            else:
                return None, None, None

        except Exception as e:
            logger.error(f"Error in backward compatibility method: {e}")
            return None, None, None

    def bulk_extract_skills_from_resumes(self, consultants: List[Dict]) -> Dict[str, List[str]]:
        """
        Extract skills from all consultant resumes in bulk and store for future use

        Args:
            consultants: List of consultant dictionaries with resume paths

        Returns:
            Dictionary mapping consultant names to their extracted skills
        """
        skills_by_consultant = {}

        logger.info(f"Starting bulk skill extraction for {len(consultants)} consultants")

        for consultant in consultants:
            name = consultant.get('name', 'Unknown')
            resume_path = consultant.get('resume_path', '')

            if resume_path and os.path.exists(resume_path):
                logger.info(f"Extracting skills for {name} from {resume_path}")
                extracted_skills = self.extract_skills_from_resume(resume_path)
                skills_by_consultant[name] = extracted_skills

                # Update consultant data with extracted skills
                consultant['extracted_skills'] = extracted_skills
                consultant['skill_count'] = len(extracted_skills)
            else:
                logger.warning(f"No resume file found for {name}")
                skills_by_consultant[name] = []

        logger.info(f"Completed bulk skill extraction for {len(skills_by_consultant)} consultants")
        return skills_by_consultant

    def save_extracted_skills_to_repository(self, skills_by_consultant: Dict[str, List[str]]):
        """
        Save all extracted skills to the skill repository for future use

        Args:
            skills_by_consultant: Dictionary mapping consultant names to skills
        """
        logger.info("Saving extracted skills to repository")

        for consultant_name, skills in skills_by_consultant.items():
            if skills:
                # Add consultant skills to repository
                self.skill_repository.add_consultant_skills(consultant_name, skills)
                logger.info(f"Saved {len(skills)} skills for {consultant_name}")

        logger.info("Completed saving skills to repository")

# Example usage and testing
if __name__ == "__main__":
    # Test the enhanced AI matcher
    try:
        matcher = EnhancedAIConsultantMatcher()

        # Test connection
        if matcher.test_connection():
            print("✅ Enhanced AI Matcher initialized successfully")

            # Test skill extraction with a sample resume (if available)
            sample_consultants = [
                {
                    'name': 'Test Consultant',
                    'resume_path': 'resumes/sample_resume.pdf',  # Replace with actual path
                    'experience': '5',
                    'location': 'New York',
                    'visa_status': 'H1B',
                    'relocation': 'Yes'
                }
            ]

            # Test bulk skill extraction
            print("Testing bulk skill extraction...")
            skills_result = matcher.bulk_extract_skills_from_resumes(sample_consultants)
            print(f"Extracted skills: {skills_result}")

        else:
            print("❌ Enhanced AI Matcher connection failed")

    except Exception as e:
        print(f"❌ Error initializing Enhanced AI Matcher: {e}")
