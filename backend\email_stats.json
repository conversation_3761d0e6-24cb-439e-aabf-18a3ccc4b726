{"total_processed": 987, "total_replied": 880, "total_skipped": 1004, "total_errors": 196, "technology_matches": {".net": 182, "devops engineer with aws": 56, "certified sr. data engineer": 1, "data warehousing": 1, "design engineer": 3, "oracle fusion financials consultant": 88, "qa automation engineer": 2, "network engineer": 6, "software developer engineer": 1, "software developer-java": 67, "software developer": 1, "software embedded developer": 1, "sr. automation engineer": 1, "aws": 15, "java": 130, "java developer": 134, "devops": 34, "project manager": 51, "cloud": 42, "oracle": 25, "devops engineer": 7, "react js developer": 64, "sql & powerbi developer": 65, "etl": 5, "pega": 4, "embedded": 3, "azure": 15, "data engineer": 16, "data analyst": 8, "angular": 5, "ui developer": 3, "qa lead": 1, "cisco": 7, "react": 32, "ssis": 1, "java tech lead": 1, "sap sd consultant": 1, "oracle dba": 2, "program manager": 5, ".net developer": 4, "sap fico": 2, "sap sd": 2, "aws devops": 4, "agile project manager": 1, "node": 2, "power bi developer": 1, "c++": 8, "odata": 1, "front end developer": 2, "s4 hana": 1, "sap s4 hana": 1, "devops  with aws": 8, "design": 4, "sql & powerbi": 1, "network": 4, "ui": 2, "software": 17, "datastage": 1, "qa automation": 10, "front end": 2, "power bi": 1, "sas": 2, "AI Selected": 116}, "technology_emails": {}, "daily_stats": {"2025-05-24": {"processed": 122, "replied": 16, "skipped": 52, "errors": 110}, "2025-05-29": {"processed": 587, "replied": 586, "skipped": 549, "errors": 66}, "2025-05-30": {"processed": 278, "replied": 278, "skipped": 403, "errors": 20}}, "hourly_stats": {"2025-05-24 01": {"processed": 100, "replied": 0, "skipped": 0, "errors": 101}, "2025-05-24 02": {"processed": 15, "replied": 9, "skipped": 18, "errors": 8}, "2025-05-24 03": {"processed": 7, "replied": 7, "skipped": 34, "errors": 0}, "2025-05-24 17": {"processed": 0, "replied": 0, "skipped": 0, "errors": 1}, "2025-05-29 19": {"processed": 100, "replied": 100, "skipped": 0, "errors": 0}, "2025-05-29 20": {"processed": 95, "replied": 95, "skipped": 105, "errors": 0}, "2025-05-29 21": {"processed": 65, "replied": 65, "skipped": 135, "errors": 0}, "2025-05-29 22": {"processed": 188, "replied": 187, "skipped": 148, "errors": 66}, "2025-05-29 23": {"processed": 139, "replied": 139, "skipped": 161, "errors": 0}, "2025-05-30 00": {"processed": 81, "replied": 81, "skipped": 0, "errors": 20}, "2025-05-30 01": {"processed": 52, "replied": 52, "skipped": 48, "errors": 0}, "2025-05-30 02": {"processed": 112, "replied": 112, "skipped": 188, "errors": 0}, "2025-05-30 03": {"processed": 33, "replied": 33, "skipped": 167, "errors": 0}}, "last_run": "2025-05-30 03:15:20", "success_rate": 81.78, "response_time": []}