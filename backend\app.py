from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import json
import os
import time
import threading
import logging
from datetime import datetime
from werkzeug.utils import secure_filename
from resume_parser import ResumeParser
from email_handler import <PERSON>ail<PERSON>andler
from skill_repository import SkillRepository
from hotlist_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Configure logging for production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_resume.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure upload settings
# Use absolute path for upload folder
UPLOAD_FOLDER = os.path.abspath(os.path.join(os.path.dirname(__file__), 'resumes'))
HOTLIST_IMAGE_FOLDER = os.path.abspath(os.path.join(os.path.dirname(__file__), 'images'))
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'doc'}
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['HOTLIST_IMAGE_FOLDER'] = HOTLIST_IMAGE_FOLDER

# Print upload folder path for debugging
print(f"Upload folder path: {UPLOAD_FOLDER}")
print(f"Hotlist image folder path: {HOTLIST_IMAGE_FOLDER}")
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload folders if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(HOTLIST_IMAGE_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if file has an allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def allowed_image_file(filename):
    """Check if image file has an allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS

# Use absolute paths for configuration files
CONFIG_FILE = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'config.json'))
HOTLIST_FILE = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'hotlist.csv'))

# Cache for loaded data to reduce file I/O
_config_cache = None
_config_cache_time = 0
_consultants_cache = None
_consultants_cache_time = 0
_cache_ttl = 30  # Cache TTL in seconds

def load_config():
    """Load configuration with caching"""
    global _config_cache, _config_cache_time
    current_time = time.time()

    # Return cached config if it's still valid
    if _config_cache and (current_time - _config_cache_time) < _cache_ttl:
        return _config_cache

    try:
        with open(CONFIG_FILE, 'r') as f:
            _config_cache = json.load(f)
            _config_cache_time = current_time
            return _config_cache
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}

def save_config(config):
    """Save configuration and update cache"""
    global _config_cache, _config_cache_time

    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        _config_cache = config
        _config_cache_time = time.time()
        return True
    except Exception as e:
        print(f"Error saving config: {e}")
        return False

def load_consultants():
    """Load consultants from Excel hotlist with caching"""
    global _consultants_cache, _consultants_cache_time
    current_time = time.time()

    # Return cached consultants if still valid
    if _consultants_cache and (current_time - _consultants_cache_time) < _cache_ttl:
        return _consultants_cache

    try:
        # Initialize hotlist handler
        hotlist_handler = HotlistHandler(HOTLIST_FILE)
        _consultants_cache = hotlist_handler.get_all_consultants()
        _consultants_cache_time = current_time
        print(f"Loaded {len(_consultants_cache)} consultants from Excel hotlist")
        return _consultants_cache
    except Exception as e:
        print(f"Error loading consultants from Excel: {e}")
        return []

def save_consultants(consultants):
    """Save consultants to Excel hotlist and update cache"""
    global _consultants_cache, _consultants_cache_time

    try:
        # Remove duplicates by name (case-insensitive)
        unique_consultants = []
        seen_names = set()

        for consultant in consultants:
            # Skip consultants with no name
            if not consultant.get('name'):
                continue

            # Convert name to lowercase for case-insensitive comparison
            name_lower = consultant['name'].lower()

            # If we haven't seen this name before, add it to the unique list
            if name_lower not in seen_names:
                seen_names.add(name_lower)

                # Ensure years_experience is an integer or 0
                if 'years_experience' not in consultant or not consultant['years_experience']:
                    consultant['years_experience'] = 0
                elif not isinstance(consultant['years_experience'], int):
                    try:
                        consultant['years_experience'] = int(consultant['years_experience'])
                    except (ValueError, TypeError):
                        consultant['years_experience'] = 0

                # Add to unique consultants list
                unique_consultants.append(consultant)
            else:
                # If we've seen this name before, merge with existing consultant
                for existing in unique_consultants:
                    if existing['name'].lower() == name_lower:
                        # Keep the existing consultant's data, but update with any new data
                        # that might be missing in the existing record
                        for key, value in consultant.items():
                            if key not in existing or not existing[key]:
                                existing[key] = value

                        # Ensure years_experience is set
                        if 'years_experience' not in existing or not existing['years_experience']:
                            existing['years_experience'] = consultant.get('years_experience', 0)
                        break

        # Save the unique consultants to Excel
        hotlist_handler = HotlistHandler(HOTLIST_FILE)
        if hotlist_handler.save_consultants(unique_consultants):
            # Update cache
            _consultants_cache = unique_consultants
            _consultants_cache_time = time.time()

            print(f"Saved {len(unique_consultants)} unique consultants to Excel (removed {len(consultants) - len(unique_consultants)} duplicates)")
            return True
        else:
            print("Failed to save consultants to Excel")
            return False
    except Exception as e:
        print(f"Error saving consultants: {e}")
        return False

# Initialize repositories
skill_repository = SkillRepository()

# Global variables to track the bot thread and statistics
bot_thread = None
bot_running = False
bot_logs = []
start_time = time.time()  # Application start time for uptime calculation

# Statistics storage
email_stats = {
    "total_processed": 0,
    "total_replied": 0,
    "total_skipped": 0,
    "total_errors": 0,
    "technology_matches": {},  # Technology -> count of matches
    "technology_emails": {},   # Technology -> list of emails sent
    "daily_stats": {},         # Date -> {processed, replied, skipped, errors}
    "hourly_stats": {},        # Hour -> {processed, replied, skipped, errors}
    "last_run": None,          # Timestamp of last run
    "success_rate": 0,         # Success rate percentage
    "response_time": []        # List of response times in seconds
}

# Stats file path
STATS_FILE = 'email_stats.json'

# Load stats if they exist
def load_stats():
    """Load email statistics from file"""
    global email_stats
    try:
        if os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'r') as f:
                loaded_stats = json.load(f)
                # Update our stats with loaded data
                email_stats.update(loaded_stats)
                add_log(f"Loaded statistics from {STATS_FILE}", "info")
    except Exception as e:
        add_log(f"Error loading statistics: {str(e)}", "error")

# Save stats to file
def save_stats():
    """Save email statistics to file"""
    try:
        with open(STATS_FILE, 'w') as f:
            json.dump(email_stats, f, indent=2)
        add_log(f"Saved statistics to {STATS_FILE}", "info")
    except Exception as e:
        add_log(f"Error saving statistics: {str(e)}", "error")

# Update statistics with new results
def update_stats(results, technology_matches=None):
    """Update email statistics with new results

    Args:
        results (dict): Results from email processing
        technology_matches (dict): Technology matches from email processing
    """
    global email_stats

    # Update totals
    email_stats["total_processed"] += results.get("processed", 0)
    email_stats["total_replied"] += results.get("replied", 0)
    email_stats["total_skipped"] += results.get("skipped", 0)
    email_stats["total_errors"] += results.get("errors", 0)

    # Calculate success rate
    total_attempts = email_stats["total_replied"] + email_stats["total_errors"]
    if total_attempts > 0:
        email_stats["success_rate"] = round((email_stats["total_replied"] / total_attempts) * 100, 2)

    # Update last run timestamp
    email_stats["last_run"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Update daily stats
    today = datetime.now().strftime("%Y-%m-%d")
    if today not in email_stats["daily_stats"]:
        email_stats["daily_stats"][today] = {
            "processed": 0,
            "replied": 0,
            "skipped": 0,
            "errors": 0
        }

    email_stats["daily_stats"][today]["processed"] += results.get("processed", 0)
    email_stats["daily_stats"][today]["replied"] += results.get("replied", 0)
    email_stats["daily_stats"][today]["skipped"] += results.get("skipped", 0)
    email_stats["daily_stats"][today]["errors"] += results.get("errors", 0)

    # Update hourly stats
    hour = datetime.now().strftime("%Y-%m-%d %H")
    if hour not in email_stats["hourly_stats"]:
        email_stats["hourly_stats"][hour] = {
            "processed": 0,
            "replied": 0,
            "skipped": 0,
            "errors": 0
        }

    email_stats["hourly_stats"][hour]["processed"] += results.get("processed", 0)
    email_stats["hourly_stats"][hour]["replied"] += results.get("replied", 0)
    email_stats["hourly_stats"][hour]["skipped"] += results.get("skipped", 0)
    email_stats["hourly_stats"][hour]["errors"] += results.get("errors", 0)

    # Update technology matches
    if technology_matches:
        for tech, count in technology_matches.items():
            if tech in email_stats["technology_matches"]:
                email_stats["technology_matches"][tech] += count
            else:
                email_stats["technology_matches"][tech] = count

    # Save updated stats
    save_stats()

# Function to add logs
def add_log(message, level="info"):
    """Add a log message to the global logs list"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = {
        "message": message,
        "level": level,
        "timestamp": timestamp
    }
    bot_logs.append(log_entry)
    # Keep only the last 100 logs
    if len(bot_logs) > 100:
        bot_logs.pop(0)
    print(f"[{level.upper()}] {message}")

# Default configuration
default_config = {
    'email': '',
    'password': '',
    'label': 'JobRequirements',
    'hotlist_image': ''
}

# Ensure config file exists
if not os.path.exists(CONFIG_FILE):
    with open(CONFIG_FILE, 'w') as f:
        json.dump(default_config, f)

# Ensure hotlist file exists (create empty CSV if needed)
if not os.path.exists(HOTLIST_FILE):
    import pandas as pd
    # Create empty DataFrame with required columns
    df = pd.DataFrame(columns=['Name', 'skills', 'EXP', 'VISA', 'Location', 'Willing to Relocate'])
    df.to_csv(HOTLIST_FILE, index=False)
    print(f"Created empty hotlist file: {HOTLIST_FILE}")

# Initialize statistics on startup
try:
    load_stats()
    print(f"Initialized statistics tracking")
except Exception as e:
    print(f"Error initializing statistics: {e}")

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    if request.method == 'GET':
        try:
            config = load_config()

            # Ensure recent_days is set with a default value if not present
            if 'recent_days' not in config:
                config['recent_days'] = 1

            # Hide password in response
            if 'password' in config:
                config['password'] = config['password'][:4] + '...' if len(config['password']) > 4 else '***'

            return jsonify(config)
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    else:  # POST
        try:
            config = request.json

            # Validate required fields
            if not config.get('email') or not config.get('password'):
                return jsonify({'error': 'Email and password are required'}), 400

            # Ensure recent_days is a valid integer
            if 'recent_days' in config:
                try:
                    config['recent_days'] = int(config['recent_days'])
                    if config['recent_days'] < 1:
                        config['recent_days'] = 1
                except (ValueError, TypeError):
                    config['recent_days'] = 1
            else:
                config['recent_days'] = 1

            # Save config using our caching function
            if save_config(config):
                add_log(f"Configuration updated for {config.get('email')}", "info")
                return jsonify({'message': 'Configuration saved successfully'})
            else:
                add_log(f"Failed to save configuration for {config.get('email')}", "error")
                return jsonify({'error': 'Failed to save configuration'}), 500
        except Exception as e:
            add_log(f"Error saving configuration: {str(e)}", "error")
            return jsonify({'error': str(e)}), 500

@app.route('/api/gmail-labels', methods=['GET'])
def get_gmail_labels():
    """Get available Gmail labels"""
    try:
        # Load configuration using our caching function
        config = load_config()

        # Check if email and password are provided
        if not config.get('email'):
            return jsonify({'error': 'Email is required. Please update your configuration.'}), 400

        if not config.get('password'):
            return jsonify({'error': 'Password is required. Please update your configuration.'}), 400

        # Load consultants using our caching function
        consultants = load_consultants()

        # Create email handler
        email_handler = EmailHandler(
            email=config['email'],
            password=config['password'],
            label=config.get('label', 'JobRequirements'),
            consultants=consultants,
            hotlist_image=config.get('hotlist_image', '')
        )

        # Return available labels
        return jsonify({
            'labels': email_handler.available_labels,
            'current_label': config.get('label', '')
        })
    except Exception as e:
        print(f"Error fetching Gmail labels: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/parse-resumes', methods=['POST'])
def parse_resumes():
    try:
        print("Starting resume parsing...")

        # Create a new instance of ResumeParser
        parser = ResumeParser()

        # Parse all resumes
        consultants = parser.parse_all_resumes()

        print(f"Parsed {len(consultants)} consultants")

        # Save consultants data using our caching function
        if save_consultants(consultants):
            print(f"Saved consultants data to {HOTLIST_FILE}")
        else:
            print(f"Failed to save consultants data")
            return jsonify({'error': 'Failed to save consultant data'}), 500

        return jsonify({
            'message': f'Successfully parsed {len(consultants)} resumes',
            'consultants': consultants
        })
    except Exception as e:
        print(f"Error parsing resumes: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/consultants', methods=['GET'])
def get_consultants():
    """Get all consultants from CSV hotlist"""
    try:
        consultants = load_consultants()
        print(f"[DEBUG] API returning {len(consultants)} consultants")
        if consultants:
            print(f"[DEBUG] First consultant: {consultants[0].get('name', 'No name')} with skills: {consultants[0].get('skills', 'No skills')}")
        return jsonify({
            'consultants': consultants,
            'count': len(consultants)
        })
    except Exception as e:
        print(f"Error getting consultants: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/import-hotlist', methods=['POST'])
def import_hotlist():
    """Import consultant data from hotlist.csv file"""
    try:
        # Use the same HOTLIST_FILE path that's defined globally
        if not os.path.exists(HOTLIST_FILE):
            return jsonify({
                'success': False,
                'error': f'hotlist.csv file not found at {HOTLIST_FILE}. Please ensure the file exists.'
            }), 404

        # Simply reload the consultants from CSV using HotlistHandler
        # This will refresh the cache with the latest CSV data
        global _consultants_cache, _consultants_cache_time

        # Force reload by clearing cache
        _consultants_cache = None
        _consultants_cache_time = 0

        # Load fresh data from CSV
        consultants = load_consultants()

        if not consultants:
            return jsonify({
                'success': False,
                'error': 'No consultant data found in hotlist.csv'
            }), 400

        add_log(f"Successfully imported {len(consultants)} consultants from hotlist.csv", "success")

        return jsonify({
            'success': True,
            'count': len(consultants),
            'message': f'Successfully imported {len(consultants)} consultants from hotlist.csv'
        })

    except Exception as e:
        error_msg = f"Error importing hotlist: {str(e)}"
        add_log(error_msg, "error")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500





def process_emails_thread():
    """Function to run in a separate thread for processing emails"""
    global bot_running

    try:
        add_log("Starting email processing thread", "info")

        # Load statistics
        load_stats()

        # Load config using our caching function
        config = load_config()
        add_log(f"Loaded configuration", "info")

        # Load consultants using our caching function
        consultants = load_consultants()
        add_log(f"Loaded {len(consultants)} consultants", "info")

        if not consultants:
            add_log("No consultants found. Please parse resumes first.", "error")
            bot_running = False
            return

        # Check if email and password are provided
        if not config.get('email'):
            add_log("Email is missing in configuration", "error")
            bot_running = False
            return

        if not config.get('password'):
            add_log("Password is missing in configuration", "error")
            bot_running = False
            return

        # Initialize email handler
        try:
            # Check if hotlist image exists
            hotlist_image_path = None
            if config.get('hotlist_image'):
                hotlist_image_path = os.path.join(app.config['HOTLIST_IMAGE_FOLDER'], config['hotlist_image'])
                if not os.path.exists(hotlist_image_path):
                    add_log(f"Warning: Hotlist image not found at {hotlist_image_path}", "warning")
                    hotlist_image_path = None
                else:
                    add_log(f"Using hotlist image: {config['hotlist_image']}", "info")

            add_log(f"Initializing email handler with account: {config['email']}", "info")
            add_log(f"Using configuration file: {CONFIG_FILE}", "info")
            add_log(f"Using label: {config.get('label', 'JobRequirements')}", "info")

            # Print first few characters of password for debugging (never print full password)
            if config.get('password'):
                password_preview = config['password'][:4] + "..." if len(config['password']) > 4 else "***"
                add_log(f"Password starts with: {password_preview}", "info")
            else:
                add_log("Password is empty!", "error")

            # Set recent_days from config (default to 1 if not set)
            recent_days = config.get('recent_days', 1)
            add_log(f"Using recent_days setting: {recent_days} (will only process emails from the last {recent_days} day(s))", "info")

            email_handler = EmailHandler(
                email=config['email'],
                password=config['password'],
                label=config.get('label', 'JobRequirements'),
                consultants=consultants,
                hotlist_image=hotlist_image_path
            )

            # Set the recent_days parameter
            email_handler.recent_days = recent_days

            # Process emails
            add_log(f"Processing emails from label: {config.get('label', 'JobRequirements')}", "info")
            start_time = datetime.now()
            results = email_handler.process_emails()
            end_time = datetime.now()

            # Check if there was an authentication error
            if 'error_message' in results and 'Authentication failed' in results['error_message']:
                add_log(f"Authentication failed: {results['error_message']}", "error")
                add_log("Please check your email and password in the configuration.", "error")
                bot_running = False
                return

            # Calculate processing time
            processing_time = (end_time - start_time).total_seconds()

            # Get technology matches from the email handler
            technology_matches = {}
            if hasattr(email_handler, 'technology_matches'):
                technology_matches = email_handler.technology_matches
            else:
                # If email handler doesn't track technologies, try to extract from hotlist handler
                if hasattr(email_handler, 'hotlist_handler') and hasattr(email_handler.hotlist_handler, 'matched_technologies'):
                    technology_matches = email_handler.hotlist_handler.matched_technologies

            # Update statistics
            update_stats(results, technology_matches)

            # Add processing time to response times
            if processing_time > 0 and results['processed'] > 0:
                email_stats["response_time"].append(processing_time / results['processed'])
                # Keep only the last 100 response times
                if len(email_stats["response_time"]) > 100:
                    email_stats["response_time"] = email_stats["response_time"][-100:]

            # Log results with technology breakdown if available
            tech_log = ""
            if technology_matches:
                tech_log = " Technology matches: " + ", ".join([f"{tech}={count}" for tech, count in technology_matches.items()])

            add_log(f"Email processing completed in {processing_time:.2f} seconds. Processed: {results['processed']}, " +
                   f"Replied: {results['replied']}, Skipped: {results['skipped']}, Errors: {results['errors']}.{tech_log}", "success")

        except Exception as inner_e:
            add_log(f"Error processing emails: {str(inner_e)}", "error")
    except Exception as e:
        add_log(f"Error in email processing thread: {str(e)}", "error")

    # Mark bot as not running when finished
    bot_running = False
    add_log("Email processing thread stopped", "info")

@app.route('/api/start', methods=['POST'])
def start_bot():
    """Start the email processing bot in a separate thread"""
    global bot_thread, bot_running, bot_logs

    try:
        # Check if bot is already running
        if bot_running and bot_thread and bot_thread.is_alive():
            return jsonify({
                'message': 'Bot is already running',
                'status': 'running'
            })

        # Clear previous logs
        bot_logs = []

        # Start the bot in a separate thread
        bot_running = True
        bot_thread = threading.Thread(target=process_emails_thread)
        bot_thread.daemon = True  # Make thread a daemon so it exits when main thread exits
        bot_thread.start()

        add_log("Bot started successfully", "success")

        return jsonify({
            'message': 'Bot started successfully',
            'status': 'running'
        })
    except Exception as e:
        add_log(f"Error starting bot: {str(e)}", "error")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stop', methods=['POST'])
def stop_bot():
    """Stop the email processing bot"""
    global bot_running

    try:
        if bot_running:
            bot_running = False
            add_log("Bot stop requested. Waiting for current operations to complete...", "warning")
            return jsonify({
                'message': 'Bot stop requested. It will stop after current operation completes.',
                'status': 'stopping'
            })
        else:
            return jsonify({
                'message': 'Bot is not running',
                'status': 'stopped'
            })
    except Exception as e:
        add_log(f"Error stopping bot: {str(e)}", "error")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-replied-cache', methods=['POST'])
def clear_replied_cache():
    """Clear the replied emails cache to force reprocessing of all emails"""
    try:
        # Load config using our caching function
        config = load_config()

        # Check if email and password are provided
        if not config.get('email') or not config.get('password'):
            return jsonify({'error': 'Email and password are required in configuration'}), 400

        # Load consultants using our caching function
        consultants = load_consultants()

        # Create email handler
        email_handler = EmailHandler(
            email=config['email'],
            password=config['password'],
            label=config.get('label', 'JobRequirements'),
            consultants=consultants,
            hotlist_image=config.get('hotlist_image', '')
        )

        # Clear replied IDs cache
        email_handler.clear_replied_ids()

        add_log("Cleared replied emails cache. All emails will be reprocessed on next run.", "info")

        return jsonify({
            'message': 'Replied emails cache cleared successfully. All emails will be reprocessed on next run.',
            'status': 'success'
        })
    except Exception as e:
        add_log(f"Error clearing replied cache: {str(e)}", "error")
        return jsonify({'error': str(e)}), 500

@app.route('/api/try-requirement-label', methods=['POST'])
def try_requirement_label():
    """Try using the 'Requirement' label instead of 'Requirment'"""
    try:
        # Load config using our caching function
        config = load_config()

        # Update the label to "Requirement"
        config['label'] = 'Requirement'

        # Save the updated config using our caching function
        if not save_config(config):
            return jsonify({'error': 'Failed to save configuration'}), 500

        add_log("Updated label to 'Requirement'. Try starting the bot again.", "info")

        return jsonify({
            'message': "Updated label to 'Requirement'. Try starting the bot again.",
            'status': 'success'
        })
    except Exception as e:
        add_log(f"Error updating label: {str(e)}", "error")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def bot_status():
    """Get the current status of the bot and logs"""
    global bot_running, bot_logs

    try:
        status = 'running' if bot_running else 'stopped'

        return jsonify({
            'status': status,
            'logs': bot_logs
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Get comprehensive dashboard statistics"""
    try:
        # Load latest stats
        load_stats()

        # Calculate additional metrics
        total_emails = email_stats["total_processed"]
        success_rate = email_stats.get("success_rate", 0)
        avg_response_time = 0

        if email_stats.get("response_time"):
            avg_response_time = sum(email_stats["response_time"]) / len(email_stats["response_time"])

        # Get recent activity (last 7 days)
        recent_activity = {}
        for date, stats in email_stats.get("daily_stats", {}).items():
            recent_activity[date] = stats

        # Get top technologies
        top_technologies = []
        for tech, count in sorted(email_stats.get("technology_matches", {}).items(),
                                 key=lambda x: x[1], reverse=True)[:10]:
            top_technologies.append({"technology": tech, "count": count})

        # Get hourly distribution (last 24 hours)
        hourly_distribution = {}
        for hour, stats in email_stats.get("hourly_stats", {}).items():
            hourly_distribution[hour] = stats

        return jsonify({
            "overview": {
                "total_processed": total_emails,
                "total_replied": email_stats["total_replied"],
                "total_skipped": email_stats["total_skipped"],
                "total_errors": email_stats["total_errors"],
                "success_rate": success_rate,
                "avg_response_time": round(avg_response_time, 2),
                "last_run": email_stats.get("last_run")
            },
            "recent_activity": recent_activity,
            "top_technologies": top_technologies,
            "hourly_distribution": hourly_distribution,
            "technology_matches": email_stats.get("technology_matches", {}),
            "status": "running" if bot_running else "stopped"
        })
    except Exception as e:
        print(f"Error getting dashboard stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/reset-stats', methods=['POST'])
def reset_dashboard_stats():
    """Reset all dashboard statistics"""
    try:
        global email_stats

        # Reset all statistics
        email_stats = {
            "total_processed": 0,
            "total_replied": 0,
            "total_skipped": 0,
            "total_errors": 0,
            "technology_matches": {},
            "technology_emails": {},
            "daily_stats": {},
            "hourly_stats": {},
            "last_run": None,
            "success_rate": 0,
            "response_time": []
        }

        # Save reset stats
        save_stats()

        add_log("Dashboard statistics reset successfully", "info")

        return jsonify({
            "message": "Statistics reset successfully",
            "status": "success"
        })
    except Exception as e:
        add_log(f"Error resetting statistics: {str(e)}", "error")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint for production monitoring"""
    try:
        # Check system components
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "components": {
                "database": "healthy",
                "email_service": "healthy",
                "file_system": "healthy",
                "bot_service": "running" if bot_running else "stopped"
            },
            "metrics": {
                "uptime": round(time.time() - start_time, 2),
                "total_processed": email_stats.get("total_processed", 0),
                "success_rate": email_stats.get("success_rate", 0),
                "last_activity": email_stats.get("last_run", "Never")
            }
        }

        # Check if hotlist file exists
        if not os.path.exists(HOTLIST_FILE):
            health_status["components"]["database"] = "warning"
            health_status["status"] = "degraded"

        # Check if config file exists
        if not os.path.exists(CONFIG_FILE):
            health_status["components"]["email_service"] = "warning"
            health_status["status"] = "degraded"

        # Check upload directories
        if not os.path.exists(UPLOAD_FOLDER) or not os.path.exists(HOTLIST_IMAGE_FOLDER):
            health_status["components"]["file_system"] = "warning"
            health_status["status"] = "degraded"

        return jsonify(health_status)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@app.route('/api/system/info', methods=['GET'])
def system_info():
    """Get system information for monitoring"""
    try:
        import psutil
        import platform

        # Get system information
        system_info = {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('.').total,
                "free": psutil.disk_usage('.').free,
                "percent": psutil.disk_usage('.').percent
            },
            "application": {
                "name": "Auto Resume Application",
                "version": "2.0.0",
                "environment": "production" if not app.debug else "development",
                "debug_mode": app.debug
            }
        }

        return jsonify(system_info)
    except ImportError:
        # psutil not available, return basic info
        return jsonify({
            "platform": platform.system(),
            "python_version": platform.python_version(),
            "application": {
                "name": "Auto Resume Application",
                "version": "2.0.0",
                "environment": "production" if not app.debug else "development",
                "debug_mode": app.debug
            },
            "note": "Install psutil for detailed system metrics"
        })
    except Exception as e:
        logger.error(f"System info failed: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/consultants/<int:consultant_id>', methods=['GET', 'PUT', 'DELETE'])
def manage_consultant(consultant_id):
    """Get, update or delete a specific consultant"""
    try:
        # Load consultants using our caching function
        consultants = load_consultants()

        # Check if consultant_id is valid
        if consultant_id < 0 or consultant_id >= len(consultants):
            return jsonify({'error': 'Invalid consultant ID'}), 404

        if request.method == 'GET':
            # Return the consultant
            return jsonify(consultants[consultant_id])

        elif request.method == 'PUT':
            # Update the consultant
            data = request.json

            # Validate required fields
            if not data or 'name' not in data:
                return jsonify({'error': 'Name is required'}), 400

            # Update fields
            for key, value in data.items():
                # Allow resume_path updates when explicitly provided
                consultants[consultant_id][key] = value

            # Save updated consultants using our caching function
            if not save_consultants(consultants):
                return jsonify({'error': 'Failed to save consultant data'}), 500

            # Update skills repository if skills were updated
            if 'skills' in data:
                skill_repository.update_consultant_skills(
                    consultant_name=consultants[consultant_id]['name'],
                    skills=data['skills']
                )
                skill_repository.save_skills()

            return jsonify({
                'message': 'Consultant updated successfully',
                'consultant': consultants[consultant_id]
            })

        elif request.method == 'DELETE':
            # Get consultant name before deletion
            consultant_name = consultants[consultant_id]['name']

            # Remove consultant
            deleted_consultant = consultants.pop(consultant_id)

            # Save updated consultants using our caching function
            if not save_consultants(consultants):
                return jsonify({'error': 'Failed to save consultant data after deletion'}), 500

            # Remove skills from repository
            skill_repository.remove_consultant_skills(consultant_name)
            skill_repository.save_skills()

            return jsonify({
                'message': 'Consultant deleted successfully',
                'consultant': deleted_consultant
            })

    except Exception as e:
        print(f"Error managing consultant: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/skills', methods=['GET'])
def get_skills():
    """Get all skills or filter by category"""
    try:
        category = request.args.get('category')

        # Log the request for debugging
        print(f"Skills API request received. Category filter: {category}")

        if category:
            # Get skills for a specific category
            skills = skill_repository.get_skills_by_category(category)
            print(f"Found {len(skills)} skills in category '{category}'")
            return jsonify({
                'category': category,
                'skills': skills
            })
        else:
            # Get all skills
            all_skills = skill_repository.get_all_skills()
            top_skills = skill_repository.get_top_skills(20)
            categories = skill_repository.get_skill_categories()

            print(f"Returning all skills data: {len(all_skills)} skills, {len(categories)} categories")

            # Check if we have any skills
            if not all_skills:
                print("Warning: No skills found in the repository")

                # Try to initialize with some default skills if empty
                if not skill_repository.skills_data['all_skills']:
                    print("Initializing repository with default skills")
                    default_skills = ["Python", "JavaScript", "Java", "C#", "SQL"]
                    for skill in default_skills:
                        skill_repository.skills_data['all_skills'].add(skill)
                        skill_repository.skills_data['skill_frequency'][skill] = 1

                    # Save the default skills
                    skill_repository.save_skills()

                    # Update our response
                    all_skills = skill_repository.get_all_skills()
                    top_skills = skill_repository.get_top_skills(20)

            return jsonify({
                'all_skills': all_skills,
                'top_skills': [{'skill': skill, 'count': count} for skill, count in top_skills],
                'categories': categories,
                'total_count': len(all_skills)
            })
    except Exception as e:
        print(f"Error getting skills: {str(e)}")
        import traceback
        traceback.print_exc()

        # Return a more helpful error message
        return jsonify({
            'error': str(e),
            'message': 'Failed to load skills data. Please check the server logs for details.',
            'status': 'error'
        }), 500

@app.route('/api/skills', methods=['POST'])
def add_skill():
    """Add a new skill"""
    try:
        data = request.json
        skill = data.get('skill')
        consultant = data.get('consultant')
        category = data.get('category')
        update_excel = data.get('update_excel', True)

        if not skill:
            return jsonify({'error': 'Skill is required'}), 400

        # Check if skill already exists (case-insensitive)
        all_skills = skill_repository.get_all_skills()
        skill_exists = any(s.lower() == skill.lower() for s in all_skills)

        # Add the skill
        skill_repository.add_skill(skill, consultant, category, update_excel=update_excel)

        # Return appropriate message
        if skill_exists:
            return jsonify({
                'success': True,
                'message': f'Updated existing skill: {skill}',
                'was_duplicate': True
            })
        else:
            return jsonify({
                'success': True,
                'message': f'Added new skill: {skill}',
                'was_duplicate': False
            })
    except Exception as e:
        print(f"Error adding skill: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/skills/categories', methods=['GET'])
def get_skill_categories():
    """Get all skill categories with their skills"""
    try:
        categories = skill_repository.get_skill_categories()
        result = {}

        for category in categories:
            result[category] = skill_repository.get_skills_by_category(category)

        return jsonify(result)
    except Exception as e:
        print(f"Error getting skill categories: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/skills/consultant/<consultant_name>', methods=['GET'])
def get_consultant_skills(consultant_name):
    """Get skills for a specific consultant"""
    try:
        skills = skill_repository.get_consultant_skills(consultant_name)
        return jsonify({
            'consultant': consultant_name,
            'skills': skills,
            'count': len(skills)
        })
    except Exception as e:
        print(f"Error getting consultant skills: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/consultants/skill/<skill>', methods=['GET'])
def get_consultants_with_skill(skill):
    """Get all consultants who have a specific skill"""
    try:
        consultants = skill_repository.get_consultants_with_skill(skill)
        return jsonify({
            'skill': skill,
            'consultants': consultants,
            'count': len(consultants)
        })
    except Exception as e:
        print(f"Error getting consultants with skill: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/extract-skills-from-resumes', methods=['POST'])
def extract_skills_from_resumes():
    """Extract skills from all consultant resumes using Enhanced AI"""
    try:
        print("Starting enhanced skill extraction from resumes...")

        # Try to initialize enhanced AI matcher
        try:
            from enhanced_ai_matcher import EnhancedAIConsultantMatcher
            enhanced_matcher = EnhancedAIConsultantMatcher()
            print("✅ Enhanced AI Matcher initialized for skill extraction")
        except Exception as e:
            print(f"❌ Could not initialize Enhanced AI Matcher: {e}")
            return jsonify({'error': 'Enhanced AI Matcher not available'}), 500

        # Get all consultants
        consultants = load_consultants()
        if not consultants:
            return jsonify({'error': 'No consultants found'}), 404

        print(f"Found {len(consultants)} consultants for skill extraction")

        # Extract skills from all resumes
        skills_by_consultant = enhanced_matcher.bulk_extract_skills_from_resumes(consultants)

        # Save extracted skills to repository
        enhanced_matcher.save_extracted_skills_to_repository(skills_by_consultant)

        # Update consultant data with extracted skills
        updated_consultants = []
        for consultant in consultants:
            name = consultant.get('name', '')
            if name in skills_by_consultant:
                consultant['extracted_skills'] = skills_by_consultant[name]
                consultant['skill_count'] = len(skills_by_consultant[name])
            updated_consultants.append(consultant)

        # Save updated consultant data
        if save_consultants(updated_consultants):
            print("✅ Updated consultant data with extracted skills")

        # Prepare response
        total_skills_extracted = sum(len(skills) for skills in skills_by_consultant.values())
        consultants_with_skills = len([c for c in skills_by_consultant.values() if c])

        return jsonify({
            'message': 'Successfully extracted skills from resumes',
            'consultants_processed': len(consultants),
            'consultants_with_skills': consultants_with_skills,
            'total_skills_extracted': total_skills_extracted,
            'skills_by_consultant': skills_by_consultant
        })

    except Exception as e:
        print(f"Error extracting skills from resumes: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload-resume', methods=['POST'])
def upload_resume():
    """Upload a resume file - skills are extracted from the resume"""
    try:
        # Check if the post request has the file part
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # If user does not select file, browser also
        # submit an empty part without filename
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        # Get consultant name from form data
        consultant_name = request.form.get('consultant_name', '')
        if not consultant_name:
            return jsonify({'error': 'Consultant name is required'}), 400

        if file and allowed_file(file.filename):
            # Create a secure filename with the consultant name
            filename = secure_filename(file.filename)

            # Add consultant name to filename if not already present
            if consultant_name.lower() not in filename.lower():
                name_part = secure_filename(consultant_name)
                base, ext = os.path.splitext(filename)
                filename = f"{name_part}_{base}{ext}"

            # Save the file
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)

            print(f"Resume uploaded: {file_path}")

            # Parse the resume (only basic info, not skills)
            parser = ResumeParser()
            consultant = parser.parse_resume(file_path)

            # Update consultant name if provided
            if consultant and consultant_name:
                consultant['name'] = consultant_name

            # Save to consultants.json
            if consultant:
                # Load existing consultants using our caching function
                consultants = load_consultants()

                # Create a consultant with the parsed data
                # We'll use the JSON file directly as our database
                # If skills were extracted from the resume, update the skill count
                if 'skills' in consultant and consultant['skills']:
                    consultant['skill_count'] = len(consultant['skills'])

                    # Update skill repository
                    skill_repository.update_consultant_skills(
                        consultant_name=consultant['name'],
                        skills=consultant['skills']
                    )
                    skill_repository.save_skills()
                    print(f"Updated consultant {consultant_name} with {len(consultant['skills'])} skills from resume")

                # Check if consultant already exists
                exists = False
                existing_index = None

                for i, c in enumerate(consultants):
                    if c.get('name', '').lower() == consultant_name.lower():
                        # Found existing consultant
                        existing_index = i
                        exists = True
                        break

                if exists:
                    # Update existing consultant but preserve fields from the existing record
                    # that aren't in the new data
                    existing_consultant = consultants[existing_index]

                    # Fields to preserve if not in the new data
                    preserve_fields = [
                        'years_experience', 'location',
                        'visa_status', 'availability', 'relocation', 'summary'
                    ]

                    # Note: We don't preserve skills here since we want to use the skills from the resume

                    # Copy fields that should be preserved
                    for field in preserve_fields:
                        if field not in consultant and field in existing_consultant:
                            consultant[field] = existing_consultant[field]

                    # Always update the resume_path
                    consultant['resume_path'] = file_path

                    # Update the existing consultant
                    print(f"Updating existing consultant: {consultant_name}")
                    consultants[existing_index] = consultant
                else:
                    # Add new consultant
                    print(f"Adding new consultant: {consultant_name}")
                    consultants.append(consultant)

                # Save consultants using our caching function
                if not save_consultants(consultants):
                    print(f"Failed to save consultants data")
                    return jsonify({'error': 'Failed to save consultant data'}), 500

                return jsonify({
                    'message': f'Resume uploaded and parsed successfully for {consultant_name}',
                    'consultant': consultant
                })
            else:
                return jsonify({'error': 'Failed to parse resume'}), 500
        else:
            return jsonify({'error': f'File type not allowed. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}'}), 400
    except Exception as e:
        print(f"Error uploading resume: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes/<path:filename>')
def download_resume(filename):
    """Download a resume file"""
    try:
        # Print debug information
        print(f"Attempting to download resume: {filename}")
        print(f"Looking in folder: {app.config['UPLOAD_FOLDER']}")

        # Check if file exists
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")

            # Try to find the file with a case-insensitive search
            folder_files = os.listdir(app.config['UPLOAD_FOLDER'])
            for folder_file in folder_files:
                if folder_file.lower() == filename.lower():
                    print(f"Found file with different case: {folder_file}")
                    return send_from_directory(app.config['UPLOAD_FOLDER'], folder_file, as_attachment=True)

            return jsonify({'error': f'File not found: {filename}'}), 404

        # Send file with the as_attachment flag to download
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)
    except Exception as e:
        print(f"Error downloading resume: {str(e)}")
        return jsonify({'error': str(e)}), 404

@app.route('/api/upload-hotlist-image', methods=['POST'])
def upload_hotlist_image():
    """Upload a hotlist image"""
    try:
        # Check if the post request has the file part
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # If user does not select file, browser also
        # submit an empty part without filename
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        if file and allowed_image_file(file.filename):
            # Create a secure filename
            filename = secure_filename(file.filename)

            # Save the file
            file_path = os.path.join(app.config['HOTLIST_IMAGE_FOLDER'], filename)
            file.save(file_path)

            print(f"Hotlist image uploaded: {file_path}")

            # Update config with the new hotlist image path
            config = load_config()
            config['hotlist_image'] = filename

            # Save config using our caching function
            if not save_config(config):
                return jsonify({'error': 'Failed to save configuration'}), 500

            return jsonify({
                'message': 'Hotlist image uploaded successfully',
                'filename': filename
            })
        else:
            return jsonify({'error': f'File type not allowed. Allowed types: {", ".join(ALLOWED_IMAGE_EXTENSIONS)}'}), 400
    except Exception as e:
        print(f"Error uploading hotlist image: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/hotlist-image/<path:filename>')
def view_hotlist_image(filename):
    """View a hotlist image in the browser"""
    try:
        # Print debug information
        print(f"Attempting to view hotlist image: {filename}")
        print(f"Looking in folder: {app.config['HOTLIST_IMAGE_FOLDER']}")

        # Check if file exists
        file_path = os.path.join(app.config['HOTLIST_IMAGE_FOLDER'], filename)
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")

            # Try to find the file with a case-insensitive search
            folder_files = os.listdir(app.config['HOTLIST_IMAGE_FOLDER'])
            for folder_file in folder_files:
                if folder_file.lower() == filename.lower():
                    print(f"Found file with different case: {folder_file}")
                    return send_from_directory(app.config['HOTLIST_IMAGE_FOLDER'], folder_file)

            # List available files for debugging
            print(f"Available files in {app.config['HOTLIST_IMAGE_FOLDER']}:")
            for f in folder_files:
                print(f"  - {f}")

            return jsonify({'error': f'File not found: {filename}'}), 404

        # Send file without the as_attachment flag to view in browser
        return send_from_directory(app.config['HOTLIST_IMAGE_FOLDER'], filename)
    except Exception as e:
        print(f"Error viewing hotlist image: {str(e)}")
        return jsonify({'error': str(e)}), 404

@app.route('/api/view-resume/<path:filename>')
def view_resume(filename):
    """View a resume file in the browser"""
    try:
        # Print debug information
        print(f"Attempting to view resume: {filename}")
        print(f"Looking in folder: {app.config['UPLOAD_FOLDER']}")

        # Check if file exists
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")

            # Try to find the file with a case-insensitive search
            folder_files = os.listdir(app.config['UPLOAD_FOLDER'])
            for folder_file in folder_files:
                if folder_file.lower() == filename.lower():
                    print(f"Found file with different case: {folder_file}")
                    return send_from_directory(app.config['UPLOAD_FOLDER'], folder_file)

            # List available files for debugging
            print(f"Available files in {app.config['UPLOAD_FOLDER']}:")
            for f in folder_files:
                print(f"  - {f}")

            return jsonify({'error': f'File not found: {filename}'}), 404

        # Send file without the as_attachment flag to view in browser
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except Exception as e:
        print(f"Error viewing resume: {str(e)}")
        return jsonify({'error': str(e)}), 404

# Serve the frontend HTML file
@app.route('/')
def serve_frontend():
    """Serve the frontend HTML file"""
    try:
        # Get the absolute path to the frontend directory
        frontend_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'frontend'))
        print(f"Serving frontend from: {frontend_dir}")
        print(f"Looking for file: {os.path.join(frontend_dir, 'index.html')}")
        print(f"File exists: {os.path.exists(os.path.join(frontend_dir, 'index.html'))}")

        # List files in the frontend directory
        print("Files in frontend directory:")
        for file in os.listdir(frontend_dir):
            print(f"  - {file}")

        return send_from_directory(frontend_dir, 'index.html')
    except Exception as e:
        print(f"Error serving frontend: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/dashboard')
def serve_dashboard():
    """Serve the dashboard HTML file"""
    try:
        # Get the absolute path to the frontend directory
        frontend_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'frontend'))
        return send_from_directory(frontend_dir, 'dashboard.html')
    except Exception as e:
        print(f"Error serving dashboard: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Serve static files from the frontend directory
@app.route('/<path:path>')
def serve_static(path):
    """Serve static files from the frontend directory"""
    try:
        # Get the absolute path to the frontend directory
        frontend_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'frontend'))
        print(f"Serving static file: {path} from {frontend_dir}")
        return send_from_directory(frontend_dir, path)
    except Exception as e:
        print(f"Error serving static file: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 404

# Phase 2: Enhanced AI Matching API Endpoints
@app.route('/api/match-log', methods=['GET'])
def get_match_log():
    """Get the match decision log"""
    try:
        import os
        log_path = os.path.join(os.path.dirname(__file__), '..', 'match_log.json')

        if os.path.exists(log_path):
            with open(log_path, 'r') as f:
                match_log = json.load(f)
        else:
            match_log = []

        return jsonify({
            'match_log': match_log,
            'count': len(match_log)
        })
    except Exception as e:
        print(f"Error getting match log: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-jd-cache', methods=['POST'])
def clear_jd_cache():
    """Clear the JD cache"""
    try:
        import os
        cache_path = os.path.join(os.path.dirname(__file__), '..', 'jd_cache.json')

        if os.path.exists(cache_path):
            os.remove(cache_path)

        return jsonify({
            'message': 'JD cache cleared successfully',
            'success': True
        })
    except Exception as e:
        print(f"Error clearing JD cache: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/skill-categories', methods=['GET'])
def get_skill_categories():
    """Get skill categories for all consultants"""
    try:
        import os
        categories_path = os.path.join(os.path.dirname(__file__), '..', 'skill_categories.json')

        if os.path.exists(categories_path):
            with open(categories_path, 'r') as f:
                skill_categories = json.load(f)
        else:
            skill_categories = {}

        return jsonify({
            'skill_categories': skill_categories,
            'consultant_count': len(skill_categories)
        })
    except Exception as e:
        print(f"Error getting skill categories: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/jd-cache-stats', methods=['GET'])
def get_jd_cache_stats():
    """Get JD cache statistics"""
    try:
        import os
        cache_path = os.path.join(os.path.dirname(__file__), '..', 'jd_cache.json')

        if os.path.exists(cache_path):
            with open(cache_path, 'r') as f:
                jd_cache = json.load(f)

            # Calculate cache statistics
            total_entries = len(jd_cache)
            recent_entries = 0

            from datetime import datetime, timedelta
            now = datetime.now()

            for entry in jd_cache.values():
                if 'timestamp' in entry:
                    entry_time = datetime.fromisoformat(entry['timestamp'])
                    if (now - entry_time).days < 1:  # Recent = last 24 hours
                        recent_entries += 1
        else:
            total_entries = 0
            recent_entries = 0

        return jsonify({
            'total_entries': total_entries,
            'recent_entries': recent_entries,
            'cache_enabled': True
        })
    except Exception as e:
        print(f"Error getting JD cache stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Production configuration
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    # Use ASCII-safe characters for Windows compatibility
    print("=" * 60)
    print(f"AUTO RESUME APPLICATION - {'DEVELOPMENT' if debug_mode else 'PRODUCTION'} MODE")
    print("=" * 60)
    print(f"Main Application: http://{host}:{port}")
    print(f"Email Dashboard: http://{host}:{port}/dashboard")
    print(f"Skills Manager: http://{host}:{port}/skills.html")
    print("=" * 60)

    app.run(debug=debug_mode, host=host, port=port, threaded=True)
