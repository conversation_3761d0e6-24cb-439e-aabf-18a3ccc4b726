#!/usr/bin/env python3
"""
Show the new professional email format
"""

def show_email():
    """Show the new email format"""
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        matcher = EnhancedAIConsultantMatcher()
        
        # Mock data
        ai_response = {
            "job_summary": "a Senior DevOps Engineer with Azure DevOps and OpenShift expertise",
        }
        
        selected_consultants = [
            {
                "name": "Kondaru",
                "experience": "12",
                "location": "IL", 
                "visa_status": "H1B",
                "relocation": "Yes",
                "match_confidence": "High",
                "skill_match_percentage": 88,
                "key_matching_skills": ["Azure DevOps", "OpenShift", "Kubernetes", "CI/CD", "Docker"],
                "match_reasoning": "Strong background in Azure DevOps, OpenShift container orchestration, and CI/CD pipeline development"
            },
            {
                "name": "Laxman Gite",
                "experience": "10",
                "location": "NJ",
                "visa_status": "H1B",
                "relocation": "Yes", 
                "match_confidence": "High",
                "skill_match_percentage": 85,
                "key_matching_skills": ["DevOps", "Azure", "Kubernetes", "Terraform", "Jenkins"],
                "match_reasoning": "Extensive DevOps experience with Azure cloud platform and infrastructure automation"
            }
        ]
        
        client_filters = {
            "visa_requirements": ["H1B"],
            "location_requirements": ["Southfield, MI"]
        }
        
        email = matcher._generate_professional_email(ai_response, selected_consultants, client_filters)
        
        print("NEW PROFESSIONAL EMAIL FORMAT:")
        print("=" * 80)
        print(email)
        print("=" * 80)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    show_email()
