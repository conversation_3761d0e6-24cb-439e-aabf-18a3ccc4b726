import imaplib
import email
import smtplib
import os
import json
import re
import time
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from email.utils import formatdate

# Database integration for consultants
# No external dependencies needed

# Import AI matcher for intelligent consultant matching
try:
    from ai_matcher import AIConsultantMatcher
    AI_MATCHER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: AI Matcher not available: {e}")
    AI_MATCHER_AVAILABLE = False

class EmailHandler:
    def __init__(self, email, password, label, consultants, hotlist_image=None):
        self.email = email
        self.password = password
        self.label = label
        self.consultants = consultants
        self.hotlist_image = hotlist_image
        self.replied_ids_file = 'replied_ids.json'
        self.available_labels = []

        # Statistics tracking
        self.technology_matches = {}  # Technology -> count of matches
        self.emails_sent = 0
        self.emails_with_resumes = 0
        self.emails_with_hotlist = 0
        self.start_time = None
        self.end_time = None

        # Processing options
        self.only_unread = False  # Process all emails, not just unread ones
        self.max_emails = 100     # Maximum number of emails to process in one run
        self.recent_days = 1      # Only process emails from the last X days

        # Performance optimizations
        self.processed_emails_cache = set()
        self.cache_max_size = 1000
        self.last_email_time = 0
        self.min_email_interval = 2  # Minimum 2 seconds between emails
        self.connection_pool = {}  # Connection pooling
        self.batch_size = 10  # Process emails in batches

        # Fetch available labels when initialized
        self.fetch_available_labels()

        # Email signature
        self.signature_text = """
Best Regards,

Mukesh Saini
Technical Recruiter
Ph. No +1 ‪(484) 202-0772‬
Web: www.decisionsix.com
Email: <EMAIL>
LinkedIn: https://www.linkedin.com/in/mukeshsaini920/

Please email me if I miss your call 😊
"""

        self.signature_html = """
<div style="margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px;">
    <p style="margin: 0;">Best Regards,</p>
    <p style="margin: 5px 0;">
        <strong>Mukesh Saini</strong><br>
        Technical Recruiter<br>
        Ph. No +1 ‪(484) 202-0772‬<br>
        Web: <a href="http://www.decisionsix.com" style="color: #3498db;">www.decisionsix.com</a><br>
        Email: <a href="mailto:<EMAIL>" style="color: #3498db;"><EMAIL></a><br>
        LinkedIn: <a href="https://www.linkedin.com/in/mukeshsaini920/" style="color: #3498db;">https://www.linkedin.com/in/mukeshsaini920/</a>
    </p>
    <p style="margin: 5px 0; font-style: italic;">Please email me if I miss your call 😊</p>
</div>
"""

        # Initialize hotlist handler for CSV-based skill matching
        from hotlist_handler import HotlistHandler
        self.hotlist_handler = HotlistHandler('hotlist.csv')

        # Initialize AI matcher for intelligent consultant matching
        self.ai_matcher = None
        self.enhanced_ai_matcher = None
        if AI_MATCHER_AVAILABLE:
            try:
                # Try to initialize enhanced AI matcher first
                from enhanced_ai_matcher import EnhancedAIConsultantMatcher
                self.enhanced_ai_matcher = EnhancedAIConsultantMatcher()
                print("✅ Enhanced AI Consultant Matcher initialized successfully")
                print("🚀 Now using resume-based AI matching with skill extraction")
            except Exception as e:
                print(f"⚠️ Warning: Could not initialize Enhanced AI Matcher: {e}")
                try:
                    # Fallback to regular AI matcher
                    self.ai_matcher = AIConsultantMatcher()
                    print("✅ Regular AI Consultant Matcher initialized successfully")
                except Exception as e2:
                    print(f"⚠️ Warning: Could not initialize AI Matcher: {e2}")
                    print("Falling back to traditional keyword matching")
        else:
            print("⚠️ AI Matcher not available, using traditional keyword matching")

        # Load replied message IDs - we'll use a dictionary to store message IDs with timestamps
        # This will allow us to handle new emails from the same sender
        if os.path.exists(self.replied_ids_file):
            with open(self.replied_ids_file, 'r') as f:
                try:
                    data = json.load(f)
                    # Handle both old format (list) and new format (dict)
                    if isinstance(data, list):
                        # Convert old format to new format
                        self.replied_ids = {msg_id: {"timestamp": time.time(), "subject": ""} for msg_id in data}
                    else:
                        self.replied_ids = data
                except:
                    self.replied_ids = {}
        else:
            self.replied_ids = {}

    def save_replied_ids(self):
        """Save replied message IDs to file"""
        with open(self.replied_ids_file, 'w') as f:
            json.dump(self.replied_ids, f)
        print(f"Saved {len(self.replied_ids)} replied message IDs to {self.replied_ids_file}")

    def add_replied_id(self, msg_id, subject=""):
        """Add a message ID to the replied list with timestamp"""
        self.replied_ids[msg_id] = {
            "timestamp": time.time(),
            "subject": subject
        }
        self.save_replied_ids()

    def clear_replied_ids(self):
        """Clear all replied message IDs to force reprocessing of all emails"""
        old_count = len(self.replied_ids)
        self.replied_ids = {}
        self.save_replied_ids()
        print(f"Cleared {old_count} replied message IDs from cache")

    def is_already_replied(self, msg_id):
        """Check if a message has already been replied to"""
        # Performance optimization: Check cache first
        if msg_id in self.processed_emails_cache:
            return True

        # If we don't have this message ID, it hasn't been replied to
        if msg_id not in self.replied_ids:
            print(f"Message ID {msg_id} not found in replied_ids, will process it")
            return False

        # If we have it, check if it's older than 7 days (604800 seconds)
        # If it's older, we'll treat it as a new message
        current_time = time.time()
        msg_time = self.replied_ids[msg_id].get("timestamp", 0)
        msg_subject = self.replied_ids[msg_id].get("subject", "")

        # If the message is older than 7 days, remove it from replied_ids and return False
        if current_time - msg_time > 604800:
            print(f"Message ID {msg_id} is older than 7 days, removing from replied_ids and will process it")
            del self.replied_ids[msg_id]
            self.save_replied_ids()
            return False

        # Message has been replied to recently
        print(f"Message ID {msg_id} with subject '{msg_subject}' has already been replied to recently, skipping")

        # Add to cache for faster future lookups
        self.add_to_cache(msg_id)
        return True

    def add_to_cache(self, msg_id):
        """Add message ID to processed cache with size limit"""
        self.processed_emails_cache.add(msg_id)

        # Maintain cache size limit
        if len(self.processed_emails_cache) > self.cache_max_size:
            # Remove oldest entries (simple FIFO)
            oldest_entries = list(self.processed_emails_cache)[:100]
            for entry in oldest_entries:
                self.processed_emails_cache.discard(entry)

    def rate_limit_check(self):
        """Check if we should wait before sending another email"""
        current_time = time.time()
        time_since_last = current_time - self.last_email_time

        if time_since_last < self.min_email_interval:
            wait_time = self.min_email_interval - time_since_last
            print(f"Rate limiting: waiting {wait_time:.2f} seconds before sending next email")
            time.sleep(wait_time)

        self.last_email_time = time.time()

    def fetch_available_labels(self):
        """Fetch all available labels from Gmail"""
        try:
            print("Fetching available Gmail labels...")
            # Connect to Gmail IMAP server
            mail = imaplib.IMAP4_SSL('imap.gmail.com')
            mail.login(self.email, self.password)

            # Get list of all labels
            status, label_data = mail.list()

            if status == 'OK':
                # Parse label data
                self.available_labels = []
                for label_item in label_data:
                    try:
                        # Decode the label data
                        decoded_label = label_item.decode('utf-8')
                        print(f"Raw label data: {decoded_label}")

                        # Extract the label name using regex
                        # First try the pattern for quoted labels (most common)
                        match = re.search(r'"([^"]+)"$', decoded_label)
                        if match:
                            label_name = match.group(1)
                            self.available_labels.append(label_name)
                            print(f"Found label with quotes: {label_name}")
                        else:
                            # Try pattern for labels without quotes
                            match = re.search(r'\s+([^\s]+)$', decoded_label)
                            if match:
                                label_name = match.group(1)
                                self.available_labels.append(label_name)
                                print(f"Found label without quotes: {label_name}")
                            else:
                                # Try a more general pattern as a fallback
                                match = re.search(r'(?:\s|\()([^"\s\)]+)$', decoded_label)
                                if match:
                                    label_name = match.group(1)
                                    self.available_labels.append(label_name)
                                    print(f"Found label with fallback pattern: {label_name}")
                                else:
                                    print(f"Could not parse label from: {decoded_label}")
                    except Exception as e:
                        print(f"Error parsing label: {e}")

                # Add INBOX prefix versions for Gmail compatibility
                inbox_prefixed_labels = []
                for label in self.available_labels:
                    if not label.startswith('INBOX/') and label != 'INBOX':
                        inbox_prefixed_labels.append(f"INBOX/{label}")

                # Add the inbox prefixed versions
                self.available_labels.extend(inbox_prefixed_labels)

                # Remove duplicates and sort
                self.available_labels = sorted(list(set(self.available_labels)))

                print(f"Found {len(self.available_labels)} labels")
                if self.available_labels:
                    print(f"Sample labels: {', '.join(self.available_labels[:10])}...")
            else:
                print(f"Failed to fetch labels: {status}")

            # Logout
            mail.logout()

        except Exception as e:
            print(f"Error fetching labels: {e}")
            import traceback
            traceback.print_exc()
            self.available_labels = []

    def extract_job_description(self, message):
        """Extract job description from email message"""
        job_description = ""

        # If message is multipart
        if message.is_multipart():
            for part in message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # Skip attachments
                if "attachment" in content_disposition:
                    continue

                # Get text content
                if content_type == "text/plain":
                    payload = part.get_payload(decode=True)
                    if payload:
                        job_description += payload.decode('utf-8', errors='ignore')
        else:
            # If message is not multipart
            payload = message.get_payload(decode=True)
            if payload:
                job_description += payload.decode('utf-8', errors='ignore')

        return job_description

    # Removed skill extraction and matching methods as they're no longer needed
    # We now use direct technology matching from the subject line

    def send_reply_with_multiple_resumes(self, msg_id, to_email, subject, consultants_data, original_msg=None):
        """Send reply email with multiple consultant resumes when technology matches"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')  # Use alternative to support both plain text and HTML
            msg['From'] = self.email
            msg['To'] = to_email

            # Get the matched keyword from the first consultant (should be the same for all)
            keyword_matched = consultants_data[0].get('keyword_matched', '') if consultants_data else ''

            # Create a subject line that indicates multiple profiles
            if len(consultants_data) > 1:
                msg['Subject'] = f"Re: {subject} - {len(consultants_data)} Matching Profiles for {keyword_matched}"
            elif len(consultants_data) == 1:
                consultant_name = consultants_data[0].get('name', '')
                msg['Subject'] = f"Re: {subject} - {consultant_name} ({keyword_matched} Profile)"
            else:
                msg['Subject'] = f"Re: {subject}"

            msg['Date'] = formatdate(localtime=True)

            # Add reply headers to make it a proper reply
            if original_msg:
                # Get original message headers for threading
                original_message_id = original_msg.get('Message-ID', '')
                references = original_msg.get('References', '')

                # Add threading headers
                if original_message_id:
                    # Add In-Reply-To header
                    msg['In-Reply-To'] = original_message_id

                    # Add References header
                    if references:
                        msg['References'] = f"{references} {original_message_id}"
                    else:
                        msg['References'] = original_message_id

            # Get the matched keyword from the first consultant
            keyword_matched = consultants_data[0].get('keyword_matched', '') if consultants_data else ''

            # Debug print to see what data is being used in the email
            print(f"Sending email with {len(consultants_data)} matching consultants for keyword: {keyword_matched}")

            # Ensure keyword_matched is a string
            if keyword_matched is None:
                keyword_matched = ''
            keyword_matched = str(keyword_matched)

            # Create consultant profiles for the email
            consultant_profiles = []
            resume_paths = []

            for consultant_data in consultants_data:
                # Get consultant details with better error handling
                consultant_name = consultant_data.get('name', '')
                # Use full skills instead of just the matched keyword
                technology = consultant_data.get('skills', consultant_data.get('technology', ''))
                location = consultant_data.get('location', '')
                experience = consultant_data.get('experience', '')
                rate = consultant_data.get('rate', '')
                availability = consultant_data.get('availability', 'Immediate')
                visa = consultant_data.get('visa', '')
                relocation = consultant_data.get('relocation', '')
                resume_path = consultant_data.get('resume_path', '')

                # Debug print to see what data is being used in the email
                print(f"Consultant data for email: Name={consultant_name}, Tech={technology}, Location={location}, " +
                      f"Experience={experience}, Rate={rate}, Availability={availability}, Visa={visa}, " +
                      f"Relocation={relocation}")

                # Ensure no None values
                if consultant_name is None: consultant_name = ''
                if technology is None: technology = ''
                if location is None: location = ''
                if experience is None: experience = ''
                if rate is None: rate = ''
                if availability is None: availability = 'Immediate'
                if visa is None: visa = ''
                if relocation is None: relocation = ''

                # Convert any non-string values to strings
                consultant_name = str(consultant_name)
                technology = str(technology)
                location = str(location)
                experience = str(experience)
                rate = str(rate)
                availability = str(availability)
                visa = str(visa)
                relocation = str(relocation)

                # Add to profiles list
                consultant_profiles.append({
                    'name': consultant_name,
                    'technology': technology,
                    'location': location,
                    'experience': experience,
                    'rate': rate,
                    'availability': availability,
                    'visa': visa,
                    'relocation': relocation,
                    'resume_path': resume_path
                })

                # Add resume path to list if it exists
                if resume_path and os.path.exists(resume_path):
                    resume_paths.append(resume_path)

            # Extract original message details for plain text quoting
            original_from = original_msg.get('From', '') if original_msg else ''
            original_date = original_msg.get('Date', '') if original_msg else ''
            original_subject = original_msg.get('Subject', '') if original_msg else ''

            # Extract original message content for plain text
            original_text_content = ""
            if original_msg:
                # Get the original message body
                if original_msg.is_multipart():
                    for part in original_msg.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))

                        # Skip attachments
                        if "attachment" in content_disposition:
                            continue

                        # Get text content
                        if content_type == "text/plain":
                            payload = part.get_payload(decode=True)
                            if payload:
                                original_text_content = payload.decode('utf-8', errors='ignore')
                                break
                else:
                    # If message is not multipart
                    payload = original_msg.get_payload(decode=True)
                    if payload:
                        original_text_content = payload.decode('utf-8', errors='ignore')

                # Limit the length of the original content to avoid huge emails
                if len(original_text_content) > 1500:
                    original_text_content = original_text_content[:1500] + "...\n[Message truncated]"

                # Clean up the original content
                original_text_content = original_text_content.strip()

                # Format the original content for quoting in a Gmail-like style
                # Don't add quote markers to every line, just present it cleanly
                # This makes it look more like a standard email client reply

            # Create email body with improved content and signature
            body = f"""Hello,

Thank you for your job requirement. Based on the technology, I'd like to recommend consultant(s) who match your requirements.

"""

            # Add each consultant profile to the plain text body
            for i, profile in enumerate(consultant_profiles):
                body += f"""
Consultant Profile #{i+1}:
- Name: {profile['name']}
- Technology: {profile['technology']}
- Location: {profile['location']}
- Experience: {profile['experience']} years
- Rate: {profile['rate']}
- Availability: {profile['availability']}
- Visa Status: {profile['visa']}
- Willing to Relocate: {profile['relocation']}

"""

            body += f"""
Why these consultants are a good fit:
Each consultant has demonstrated expertise ,which matches your requirement. They are available immediately or on short notice.

Next Steps:
1. Please review the attached resume(s) for complete details on the consultants' experience and qualifications
2. If you'd like to schedule an interview or need additional information, please let me know


All matching consultant resumes are attached for your review. I look forward to your feedback.
{self.signature_text}
---------- Forwarded message ---------
From: {original_from}
Date: {original_date}
Subject: {original_subject}
To: {original_msg.get('To', '') if original_msg else ''}

{original_text_content}
"""
            # Add plain text version
            msg.attach(MIMEText(body, 'plain'))

            # Add HTML version with light theme and multiple consultant profiles for better visibility
            html_body = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; background-color: #ffffff; color: #333333; }}
                    .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                    .header {{ color: #2c3e50; margin-bottom: 20px; }}
                    .profile {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #3498db; }}
                    .profile-item {{ margin-bottom: 8px; }}
                    .highlight {{ font-weight: bold; color: #3498db; }}
                    .next-steps {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #2ecc71; }}
                    .next-steps ol {{ margin-left: 20px; padding-left: 0; }}
                    .footer {{ margin-top: 30px; color: #7f8c8d; font-size: 0.9em; }}
                    .original-message {{ margin-top: 30px; padding: 15px; border-top: 1px solid #e0e0e0; color: #505050; }}
                    .profile-header {{ display: flex; justify-content: space-between; align-items: center; }}
                    .profile-number {{ background-color: #3498db; color: #ffffff; padding: 2px 8px; border-radius: 12px; font-weight: bold; }}
                    .summary {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <p>Hello,</p>

                    <p>Thank you for your job requirement , I'd like to recommend consultant(s)</span> who match your requirements.</p>

                    <div class="summary">
                        <h3>Summary of Matching Consultants:</h3>

                        <p>All matching consultant resumes are attached to this email.</p>
                    </div>
            """

            # Add each consultant profile to the HTML body
            for i, profile in enumerate(consultant_profiles):
                html_body += f"""
                    <div class="profile">
                        <div class="profile-header">
                            <h3>Consultant Profile:</h3>
                            <span class="profile-number">#{i+1}</span>
                        </div>
                        <div class="profile-item"><strong>Name:</strong> {profile['name']}</div>
                        <div class="profile-item"><strong>Technology:</strong> {profile['technology']}</div>
                        <div class="profile-item"><strong>Location:</strong> {profile['location']}</div>
                        <div class="profile-item"><strong>Experience:</strong> {profile['experience']} years</div>
                        <div class="profile-item"><strong>Rate:</strong> {profile['rate']}</div>
                        <div class="profile-item"><strong>Availability:</strong> {profile['availability']}</div>
                        <div class="profile-item"><strong>Visa Status:</strong> {profile['visa']}</div>
                        <div class="profile-item"><strong>Willing to Relocate:</strong> {profile['relocation']}</div>
                    </div>
                """

            html_body += f"""
                    <p><strong>Why these consultants are a good fit:</strong><br>
                    Each consultant has demonstrated expertise in <span class="highlight" which matches your requirement. They are available immediately or on short notice.</p>

                    <div class="next-steps">
                        <h3>Next Steps:</h3>
                        <ol>
                            <li>Please review the attached resumes for complete details on the consultants' experience and qualifications</li>
                            <li>If you'd like to schedule an interview or need additional information, please let me know</li>

                        </ol>
                    </div>

                    <p>All matching consultant resumes are attached for your review. I look forward to your feedback.</p>

                    <div class="footer">
                        {self.signature_html}
                    </div>

                    <div class="original-message">
                        <div style="padding: 10px 0; color: #666;">
                            <div style="border-bottom: 1px solid #ddd; margin-bottom: 10px; padding-bottom: 5px;">
                                ---------- Forwarded message ---------
                            </div>
                            <div style="margin-bottom: 15px;">
                                <div><strong>From:</strong> {original_msg.get('From', '') if original_msg else ''}</div>
                                <div><strong>Date:</strong> {original_msg.get('Date', '') if original_msg else ''}</div>
                                <div><strong>Subject:</strong> {original_msg.get('Subject', '') if original_msg else ''}</div>
                                <div><strong>To:</strong> {original_msg.get('To', '') if original_msg else ''}</div>
                            </div>
                        </div>
                        <div style="padding: 10px; background-color: #f9f9f9; border-radius: 4px; color: #333; font-family: Arial, sans-serif;">
                            {original_text_content.replace('<', '&lt;').replace('>', '&gt;').replace('\\n', '<br>')}
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            msg.attach(MIMEText(html_body, 'html'))

            # Attach all resumes
            resumes_attached = 0
            for resume_path in resume_paths:
                if os.path.exists(resume_path):
                    try:
                        with open(resume_path, 'rb') as f:
                            attachment = MIMEApplication(f.read(), _subtype="pdf")
                            # Use a more descriptive filename that includes the consultant name
                            consultant_name = ""
                            for profile in consultant_profiles:
                                if profile['resume_path'] == resume_path:
                                    consultant_name = profile['name']
                                    break

                            # Create a clean filename
                            base_filename = os.path.basename(resume_path)
                            if consultant_name:
                                # If we have a consultant name, include it in the filename
                                clean_name = consultant_name.replace(' ', '_').replace(',', '').replace('(', '').replace(')', '')
                                attachment_filename = f"{clean_name}_{base_filename}"
                            else:
                                attachment_filename = base_filename

                            attachment.add_header('Content-Disposition', 'attachment', filename=attachment_filename)
                            msg.attach(attachment)
                            resumes_attached += 1
                            print(f"Attached resume: {resume_path}")
                    except Exception as e:
                        print(f"Error attaching resume {resume_path}: {e}")
                else:
                    print(f"Warning: Resume file not found at {resume_path}")

            if resumes_attached == 0:
                print(f"Warning: No resumes were attached to the email")

            # Send email using SSL for better security
            try:
                print(f"Attempting to send email to {to_email} with subject: {subject}")
                print(f"Using email account: {self.email}")

                with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                    print("Connected to SMTP server")
                    server.login(self.email, self.password)
                    print("Logged in to SMTP server")
                    server.send_message(msg)
                    print("Message sent successfully")

                print(f"Successfully sent reply with resume to {to_email} for subject: {subject}")

                # Add message ID to replied list with subject
                self.add_replied_id(msg_id, subject)

                return True
            except Exception as e:
                print(f"Error in SMTP connection or sending: {e}")
                return False
        except Exception as e:
            print(f"Error sending reply with resume: {e}")
            return False

    def match_subject_to_technology(self, subject):
        """Match email subject to technologies in CSV hotlist database

        Args:
            subject (str): Email subject line

        Returns:
            list: List of matching consultant profiles with keyword_matched field added
        """
        # Use hotlist handler for matching if available
        if self.hotlist_handler:
            return self.hotlist_handler.match_subject_to_technology(subject)

        # Fallback to original matching logic if hotlist handler is not available
        matching_consultants = []

        # Skip if no subject or consultants
        if not subject or not self.consultants:
            return matching_consultants

        # Convert subject to lowercase for case-insensitive matching
        subject_lower = subject.lower()

        # Extract all skills from consultants
        all_skills = set()
        for consultant in self.consultants:
            if 'skills' in consultant and consultant['skills']:
                for skill in consultant['skills']:
                    if skill and len(skill) > 1:  # Skip single character skills (like 'r')
                        all_skills.add(skill.lower())

        # Find skills that match the subject
        matched_skills = []
        for skill in all_skills:
            # Skip very short skills (likely false positives)
            if len(skill) < 2:
                continue

            # Check if skill is in subject
            if skill in subject_lower:
                matched_skills.append(skill)

        # If no direct matches, try partial matching for longer skills
        if not matched_skills:
            for skill in all_skills:
                # Only consider skills with 4+ characters for partial matching
                if len(skill) >= 4:
                    # Check if at least 4 characters of the skill appear consecutively in the subject
                    for i in range(len(skill) - 3):
                        if skill[i:i+4] in subject_lower:
                            matched_skills.append(skill)
                            break

        # Find consultants with matching skills
        if matched_skills:
            for consultant in self.consultants:
                if 'skills' not in consultant or not consultant['skills']:
                    continue

                # Check if any of the consultant's skills match
                for skill in consultant['skills']:
                    if not skill:
                        continue

                    skill_lower = skill.lower()
                    if skill_lower in matched_skills:
                        # Create a copy of the consultant data with the matched keyword
                        consultant_copy = consultant.copy()
                        consultant_copy['keyword_matched'] = skill

                        # Add required fields for email
                        if 'location' not in consultant_copy or not consultant_copy['location']:
                            consultant_copy['location'] = 'USA'
                        if 'years_experience' in consultant_copy:
                            consultant_copy['experience'] = str(consultant_copy['years_experience'])
                        else:
                            consultant_copy['experience'] = '5'
                        if 'visa_status' in consultant_copy:
                            consultant_copy['visa'] = consultant_copy['visa_status']
                        else:
                            consultant_copy['visa'] = 'H1B'
                        if 'rate' not in consultant_copy or not consultant_copy['rate']:
                            consultant_copy['rate'] = 'Negotiable'
                        if 'availability' not in consultant_copy or not consultant_copy['availability']:
                            consultant_copy['availability'] = 'Immediate'

                        # Add to matching consultants list (avoid duplicates)
                        if not any(c['name'] == consultant_copy['name'] for c in matching_consultants):
                            matching_consultants.append(consultant_copy)
                        break

        return matching_consultants

    def ai_match_consultant_to_job(self, job_description, subject):
        """Use Enhanced AI to match the best consultant to a job description based on actual resume content

        Args:
            job_description (str): Full job description from email
            subject (str): Email subject line

        Returns:
            tuple: (selected_consultant_dict, ai_generated_message, ai_response_data)
        """
        # Try enhanced AI matcher first (resume-based matching)
        if self.enhanced_ai_matcher:
            print("[INFO] Using Enhanced AI Matcher with resume analysis")
            return self._enhanced_ai_match(job_description, subject)

        # Fallback to regular AI matcher
        elif self.ai_matcher:
            print("[INFO] Using Regular AI Matcher")
            return self._regular_ai_match(job_description, subject)

        else:
            print("[INFO] No AI Matcher available, falling back to keyword matching")
            return None, None, None

    def _enhanced_ai_match(self, job_description, subject):
        """Enhanced AI matching using actual resume content"""
        try:
            # Get all consultants from hotlist
            all_consultants = []
            if self.hotlist_handler:
                all_consultants = self.hotlist_handler.get_all_consultants()

            if not all_consultants:
                print("[WARNING] No consultants available for enhanced AI matching")
                return None, None, None

            print(f"[INFO] Using Enhanced AI to analyze {len(all_consultants)} consultant resumes against job description")

            # Use enhanced AI to find the best match based on actual resume content
            consultant_name, ai_message, ai_response = self.enhanced_ai_matcher.enhanced_match_consultant_to_job(
                job_description, all_consultants
            )

            if not consultant_name or not ai_message:
                print("[WARNING] Enhanced AI matching failed to return valid results")
                return None, None, None

            # Find the selected consultant in our list
            selected_consultant = None
            for consultant in all_consultants:
                if consultant.get('name', '').lower() == consultant_name.lower():
                    selected_consultant = consultant.copy()
                    break

            if not selected_consultant:
                print(f"[WARNING] Could not find selected consultant '{consultant_name}' in consultant list")
                return None, None, None

            # Add AI matching metadata
            selected_consultant['ai_matched'] = True
            selected_consultant['ai_message'] = ai_message
            selected_consultant['match_confidence'] = ai_response.get('match_confidence', 'Unknown')
            selected_consultant['skill_match_percentage'] = ai_response.get('skill_match_percentage', 0)
            selected_consultant['match_reasoning'] = ai_response.get('match_reasoning', '')

            print(f"[INFO] ✅ Enhanced AI successfully matched consultant: {consultant_name}")
            print(f"[INFO] Match confidence: {ai_response.get('match_confidence', 'Unknown')}")
            print(f"[INFO] Skill match: {ai_response.get('skill_match_percentage', 0)}%")

            return selected_consultant, ai_message, ai_response

        except Exception as e:
            print(f"[ERROR] Enhanced AI matching failed: {e}")
            return None, None, None

    def _regular_ai_match(self, job_description, subject):
        """Regular AI matching using CSV data"""
        try:
            # Get all consultants from hotlist
            all_consultants = []
            if self.hotlist_handler:
                all_consultants = self.hotlist_handler.get_all_consultants()

            if not all_consultants:
                print("[WARNING] No consultants available for AI matching")
                return None, None, None

            print(f"[INFO] Using AI to match {len(all_consultants)} consultants to job description")

            # Use AI to find the best match
            consultant_name, ai_message, ai_response = self.ai_matcher.match_consultant_and_generate_reply(
                job_description, all_consultants
            )

            if not consultant_name or not ai_message:
                print("[WARNING] AI matching failed to return valid results")
                return None, None, None

            # Find the selected consultant in our list
            selected_consultant = None
            for consultant in all_consultants:
                if consultant.get('name', '').lower() == consultant_name.lower():
                    selected_consultant = consultant.copy()
                    # Add AI-specific fields
                    selected_consultant['keyword_matched'] = 'AI Selected'
                    selected_consultant['ai_generated_message'] = ai_message
                    selected_consultant['ai_confidence'] = ai_response.get('match_confidence', 'Unknown') if ai_response else 'Unknown'
                    selected_consultant['ai_key_skills'] = ai_response.get('key_matching_skills', []) if ai_response else []
                    break

            if not selected_consultant:
                print(f"[WARNING] AI selected consultant '{consultant_name}' not found in consultant list")
                return None, None, None

            print(f"[INFO] ✅ AI selected consultant: {consultant_name}")
            print(f"[INFO] AI confidence: {selected_consultant.get('ai_confidence', 'Unknown')}")
            print(f"[INFO] Key matching skills: {', '.join(selected_consultant.get('ai_key_skills', []))}")

            return selected_consultant, ai_message, ai_response

        except Exception as e:
            print(f"[ERROR] Error in AI consultant matching: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None

    def send_reply_with_ai_consultant(self, msg_id, to_email, subject, consultant_data, ai_message, original_msg=None):
        """Send reply email with AI-selected consultant and custom message"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')  # Use alternative to support both plain text and HTML
            msg['From'] = self.email
            msg['To'] = to_email

            # Create subject line
            consultant_name = consultant_data.get('name', '')
            msg['Subject'] = f"Re: {subject} - {consultant_name} (Recommended Profile Based on JD)"
            msg['Date'] = formatdate(localtime=True)

            # Add reply headers to make it a proper reply
            if original_msg:
                # Get original message headers for threading
                original_message_id = original_msg.get('Message-ID', '')
                references = original_msg.get('References', '')

                # Add threading headers
                if original_message_id:
                    # Add In-Reply-To header
                    msg['In-Reply-To'] = original_message_id

                    # Add References header
                    if references:
                        msg['References'] = f"{references} {original_message_id}"
                    else:
                        msg['References'] = original_message_id

            # Extract original message details for quoting
            original_from = original_msg.get('From', '') if original_msg else ''
            original_date = original_msg.get('Date', '') if original_msg else ''
            original_subject = original_msg.get('Subject', '') if original_msg else ''

            # Extract original message content
            original_text_content = ""
            if original_msg:
                original_text_content = self.extract_job_description(original_msg)
                # Limit the length of the original content
                if len(original_text_content) > 1500:
                    original_text_content = original_text_content[:1500] + "...\n[Message truncated]"
                original_text_content = original_text_content.strip()

            # Get consultant details
            consultant_name = consultant_data.get('name', '')
            technology = consultant_data.get('skills', '')
            location = consultant_data.get('location', '')
            experience = consultant_data.get('experience', '')
            rate = consultant_data.get('rate', 'Negotiable')
            availability = consultant_data.get('availability', 'Immediate')
            visa = consultant_data.get('visa', '')
            relocation = consultant_data.get('relocation', '')
            ai_confidence = consultant_data.get('ai_confidence', 'High')
            ai_key_skills = consultant_data.get('ai_key_skills', [])

            # Create plain text body with AI-generated message
            plain_body = f"""Hello,

Thank you for your job requirement. Based on JD of your requirements, I'd like to recommend our top consultant who is an excellent match for this position.

{ai_message}

Consultant Profile:
- Name: {consultant_name}
- Skills/Technologies: {technology}
- Location: {location}
- Experience: {experience} years
- Rate: {rate}
- Availability: {availability}
- Visa Status: {visa}
- Willing to Relocate: {relocation}

Match Confidence: {ai_confidence}
Key Matching Skills: {', '.join(ai_key_skills)}

Please find the detailed resume attached for your review. I'm confident this consultant will be a great addition to your team.

{self.signature_text}

---------- Forwarded message ---------
From: {original_from}
Date: {original_date}
Subject: {original_subject}
To: {original_msg.get('To', '') if original_msg else ''}

{original_text_content}
"""
            # Add plain text version
            msg.attach(MIMEText(plain_body, 'plain'))

            # Create HTML version
            html_body = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; background-color: #ffffff; color: #333333; }}
                    .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                    .ai-badge {{ background-color: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: bold; }}
                    .profile {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #4CAF50; }}
                    .profile-item {{ margin-bottom: 8px; }}
                    .highlight {{ font-weight: bold; color: #4CAF50; }}
                    .ai-message {{ background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #4CAF50; }}
                    .confidence {{ background-color: #fff3e0; padding: 10px; border-radius: 5px; margin-bottom: 15px; }}
                    .footer {{ margin-top: 30px; color: #7f8c8d; font-size: 0.9em; }}
                    .original-message {{ margin-top: 30px; padding: 15px; border-top: 1px solid #e0e0e0; color: #505050; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <p>Hello,</p>

                    <p>Thank you for your job requirement. Based on <span class="ai-badge">JOB Description</span> of your requirements, I'd like to recommend our matching consultant who is an excellent match for this position.</p>

                    <div class="ai-message">
                        <h3>Recommendation:</h3>
                        <p>{ai_message}</p>
                    </div>

                    <div class="profile">
                        <h3>Recommended Consultant Profile:</h3>
                        <div class="profile-item"><strong>Name:</strong> {consultant_name}</div>
                        <div class="profile-item"><strong>Skills/Technologies:</strong> {technology}</div>
                        <div class="profile-item"><strong>Location:</strong> {location}</div>
                        <div class="profile-item"><strong>Experience:</strong> {experience} years</div>
                        <div class="profile-item"><strong>Rate:</strong> {rate}</div>
                        <div class="profile-item"><strong>Availability:</strong> {availability}</div>
                        <div class="profile-item"><strong>Visa Status:</strong> {visa}</div>
                        <div class="profile-item"><strong>Willing to Relocate:</strong> {relocation}</div>
                    </div>

                    <div class="confidence">
                        <strong>Match Confidence:</strong> <span class="highlight">{ai_confidence}</span><br>
                        <strong>Key Matching Skills:</strong> {', '.join(ai_key_skills)}
                    </div>

                    <p>Please find the detailed resume attached for your review. I'm confident this consultant will be a great addition to your team.</p>

                    <div class="footer">
                        {self.signature_html}
                    </div>

                    <div class="original-message">
                        <div style="padding: 10px 0; color: #666;">
                            <div style="border-bottom: 1px solid #ddd; margin-bottom: 10px; padding-bottom: 5px;">
                                ---------- Forwarded message ---------
                            </div>
                            <div style="margin-bottom: 15px;">
                                <div><strong>From:</strong> {original_from}</div>
                                <div><strong>Date:</strong> {original_date}</div>
                                <div><strong>Subject:</strong> {original_subject}</div>
                                <div><strong>To:</strong> {original_msg.get('To', '') if original_msg else ''}</div>
                            </div>
                        </div>
                        <div style="padding: 10px; background-color: #f9f9f9; border-radius: 4px; color: #333; font-family: Arial, sans-serif;">
                            {original_text_content.replace('<', '&lt;').replace('>', '&gt;').replace('\\n', '<br>')}
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            msg.attach(MIMEText(html_body, 'html'))

            # Attach resume if available
            resume_path = consultant_data.get('resume_path', '')
            if resume_path and os.path.exists(resume_path):
                try:
                    with open(resume_path, 'rb') as f:
                        attachment = MIMEApplication(f.read(), _subtype="pdf")
                        # Create a clean filename
                        base_filename = os.path.basename(resume_path)
                        clean_name = consultant_name.replace(' ', '_').replace(',', '').replace('(', '').replace(')', '')
                        attachment_filename = f"{clean_name}_{base_filename}"
                        attachment.add_header('Content-Disposition', 'attachment', filename=attachment_filename)
                        msg.attach(attachment)
                        print(f"Attached resume: {resume_path}")
                except Exception as e:
                    print(f"Error attaching resume {resume_path}: {e}")
            else:
                print(f"Warning: Resume file not found at {resume_path}")

            # Send email using SSL
            try:
                print(f"Attempting to send AI-powered email to {to_email}")
                with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                    server.login(self.email, self.password)
                    server.send_message(msg)
                    print("AI-powered message sent successfully")

                print(f"Successfully sent AI reply with consultant {consultant_name} to {to_email}")

                # Add message ID to replied list
                self.add_replied_id(msg_id, subject)
                return True

            except Exception as e:
                print(f"Error sending AI-powered email: {e}")
                return False

        except Exception as e:
            print(f"Error creating AI-powered reply: {e}")
            return False

    def send_reply_with_hotlist(self, msg_id, to_email, subject, original_msg=None):
        """Send reply email with hotlist table when no technology match is found"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')  # Use alternative to support both plain text and HTML
            msg['From'] = self.email
            msg['To'] = to_email
            msg['Subject'] = f"Re: {subject}"
            msg['Date'] = formatdate(localtime=True)

            # Add reply headers to make it a proper reply
            if original_msg:
                # Get original message headers for threading
                original_message_id = original_msg.get('Message-ID', '')
                references = original_msg.get('References', '')

                # Add threading headers
                if original_message_id:
                    # Add In-Reply-To header
                    msg['In-Reply-To'] = original_message_id

                    # Add References header
                    if references:
                        msg['References'] = f"{references} {original_message_id}"
                    else:
                        msg['References'] = original_message_id

            # Use the existing hotlist image in the project
            hotlist_image_path = os.path.join(os.path.dirname(__file__), 'images', 'WhatsApp_Image_2025-05-19_at_6.57.39_PM.jpeg')

            # Print debug info about the hotlist image
            print(f"Using hotlist image: {hotlist_image_path}")
            print(f"Image exists: {os.path.exists(hotlist_image_path)}")

            # Extract original message details for plain text quoting
            original_from = original_msg.get('From', '') if original_msg else ''
            original_date = original_msg.get('Date', '') if original_msg else ''
            original_subject = original_msg.get('Subject', '') if original_msg else ''

            # Extract original message content for plain text
            original_text_content = ""
            if original_msg:
                # Get the original message body
                if original_msg.is_multipart():
                    for part in original_msg.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))

                        # Skip attachments
                        if "attachment" in content_disposition:
                            continue

                        # Get text content
                        if content_type == "text/plain":
                            payload = part.get_payload(decode=True)
                            if payload:
                                original_text_content = payload.decode('utf-8', errors='ignore')
                                break
                else:
                    # If message is not multipart
                    payload = original_msg.get_payload(decode=True)
                    if payload:
                        original_text_content = payload.decode('utf-8', errors='ignore')

                # Limit the length of the original content to avoid huge emails
                if len(original_text_content) > 1500:
                    original_text_content = original_text_content[:1500] + "...\n[Message truncated]"

                # Clean up the original content
                original_text_content = original_text_content.strip()

                # Format the original content for quoting in a Gmail-like style
                # Don't add quote markers to every line, just present it cleanly
                # This makes it look more like a standard email client reply

            # Create plain text body with signature
            plain_body = f"""Hello,

Thank you for your job requirement. I'd like to share our current consultant hotlist with you.

Please see the hotlist image in the HTML version of this email.

Please let me know if you need any additional information or have any questions.
{self.signature_text}

---------- Forwarded message ---------
From: {original_from}
Date: {original_date}
Subject: {original_subject}
To: {original_msg.get('To', '') if original_msg else ''}

{original_text_content}
"""
            # Add plain text version
            msg.attach(MIMEText(plain_body, 'plain'))

            # Create HTML body with improved styling
            html_body = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; background-color: #ffffff; color: #333333; }}
                    .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                    .header {{ color: #2c3e50; margin-bottom: 20px; }}
                    .message {{ margin-bottom: 20px; }}
                    .highlight {{ font-weight: bold; color: #3498db; }}
                    .hotlist-container {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }}
                    .footer {{ margin-top: 30px; color: #7f8c8d; font-size: 0.9em; }}
                    .original-message {{ margin-top: 30px; padding: 15px; border-top: 1px solid #e0e0e0; color: #505050; }}
                    .info-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #3498db; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <p>Hello,</p>

                    <div class="info-box">
                        <h3>Consultant Hotlist</h3>
                        <p>Thank you for your job requirement. I'd like to share our current consultant hotlist with you.</p>
                        <p>Please see the hotlist below with our available consultants. If you see any consultant that might be a good fit for your requirement, please let me know and I'll send you their detailed resume.</p>
                    </div>

                    <div class="hotlist-container">
                        <img src="cid:hotlist_image" alt="Hotlist Image" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    </div>

                    <p>Please let me know if you need any additional information or have any questions about any of the consultants listed.</p>

                    <div class="footer">
                        {self.signature_html}
                    </div>

                    <div class="original-message">
                        <div style="padding: 10px 0; color: #666;">
                            <div style="border-bottom: 1px solid #ddd; margin-bottom: 10px; padding-bottom: 5px;">
                                ---------- Forwarded message ---------
                            </div>
                            <div style="margin-bottom: 15px;">
                                <div><strong>From:</strong> {original_msg.get('From', '') if original_msg else ''}</div>
                                <div><strong>Date:</strong> {original_msg.get('Date', '') if original_msg else ''}</div>
                                <div><strong>Subject:</strong> {original_msg.get('Subject', '') if original_msg else ''}</div>
                                <div><strong>To:</strong> {original_msg.get('To', '') if original_msg else ''}</div>
                            </div>
                        </div>
                        <div style="padding: 10px; background-color: #f9f9f9; border-radius: 4px; color: #333; font-family: Arial, sans-serif;">
                            {original_text_content.replace('<', '&lt;').replace('>', '&gt;').replace('\\n', '<br>')}
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            # Add HTML version
            msg.attach(MIMEText(html_body, 'html'))

            # Always attach the hotlist image
            try:
                with open(hotlist_image_path, 'rb') as img_file:
                    img = MIMEImage(img_file.read())
                    img.add_header('Content-ID', '<hotlist_image>')
                    img.add_header('Content-Disposition', 'inline')
                    msg.attach(img)
                    print(f"Attached hotlist image: {hotlist_image_path}")
            except Exception as img_error:
                print(f"Error attaching hotlist image: {img_error}")

            # Send email using SSL for better security
            try:
                print(f"Attempting to send hotlist email to {to_email} with subject: {subject}")
                print(f"Using email account: {self.email}")

                with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                    print("Connected to SMTP server")
                    server.login(self.email, self.password)
                    print("Logged in to SMTP server")
                    server.send_message(msg)
                    print("Hotlist message sent successfully")

                print(f"Successfully sent reply with hotlist to {to_email} for subject: {subject}")

                # Add message ID to replied list with subject
                self.add_replied_id(msg_id, subject)

                return True
            except Exception as e:
                print(f"Error in SMTP connection or sending hotlist: {e}")
                import traceback
                traceback.print_exc()
                return False
        except Exception as e:
            print(f"Error sending reply with hotlist: {e}")
            return False

    # Legacy send_reply method removed as we now use the new methods

    def process_emails(self):
        """Process emails from the specified label"""
        results = {
            'processed': 0,
            'replied': 0,
            'skipped': 0,
            'errors': 0
        }

        # Reset statistics
        self.technology_matches = {}
        self.emails_sent = 0
        self.emails_with_resumes = 0
        self.emails_with_hotlist = 0
        self.start_time = None
        self.end_time = None

        try:
            # Record start time
            self.start_time = formatdate(localtime=True)

            # Connect to Gmail IMAP server
            mail = imaplib.IMAP4_SSL('imap.gmail.com')
            try:
                mail.login(self.email, self.password)
                print(f"Successfully logged in to Gmail with account: {self.email}")
            except imaplib.IMAP4.error as auth_error:
                error_msg = str(auth_error)
                if "AUTHENTICATIONFAILED" in error_msg:
                    print(f"Authentication failed: Invalid email or password. Please check your credentials.")
                    self.end_time = formatdate(localtime=True)
                    results['errors'] += 1
                    results['error_message'] = "Authentication failed: Invalid email or password. Please check your credentials."
                    return results
                else:
                    print(f"Login error: {error_msg}")
                    self.end_time = formatdate(localtime=True)
                    results['errors'] += 1
                    results['error_message'] = f"Login error: {error_msg}"
                    return results

            # Select the label/folder
            print(f"Attempting to select label: {self.label}")

            # Try different variations of the label name
            label_variations = [
                f'"{self.label}"',  # With quotes
                self.label,         # Without quotes
                f'"INBOX/{self.label}"',  # With INBOX prefix and quotes
                f'INBOX/{self.label}',    # With INBOX prefix without quotes
                f'"{self.label.upper()}"',  # Uppercase with quotes
                self.label.upper(),         # Uppercase without quotes
                f'"{self.label.lower()}"',  # Lowercase with quotes
                self.label.lower(),         # Lowercase without quotes
            ]

            # Add common typo variations
            if self.label.lower() == 'requirment':
                # Try "Requirement" instead of "Requirment" (common typo)
                label_variations.extend([
                    '"Requirement"',
                    'Requirement',
                    '"INBOX/Requirement"',
                    'INBOX/Requirement'
                ])
            elif self.label.lower() == 'requirement':
                # Try "Requirment" instead of "Requirement" (common typo)
                label_variations.extend([
                    '"Requirment"',
                    'Requirment',
                    '"INBOX/Requirment"',
                    'INBOX/Requirment'
                ])

            # Add INBOX and All Mail as last fallbacks
            label_variations.append('INBOX')
            label_variations.append('"[Gmail]/All Mail"')
            label_variations.append('[Gmail]/All Mail')

            # Try to find exact matches first
            exact_matches = []
            partial_matches = []

            # Add all available labels to the variations
            for available_label in self.available_labels:
                if available_label.lower() == self.label.lower():
                    # Exact match (case-insensitive)
                    exact_matches.append(available_label)
                elif self.label.lower() in available_label.lower():
                    # Partial match
                    partial_matches.append(available_label)

            # Add exact matches at the beginning
            for label in exact_matches:
                if label not in label_variations:
                    label_variations.insert(0, label)

            # Then add partial matches
            for label in partial_matches:
                if label not in label_variations:
                    label_variations.insert(len(exact_matches), label)

            selected = False
            for label_variation in label_variations:
                try:
                    print(f"Trying to select label: {label_variation}")
                    status, count = mail.select(label_variation)
                    if status == 'OK':
                        print(f"Successfully selected label: {label_variation}")
                        print(f"Found {int(count[0])} messages in this label")
                        selected = True
                        break
                except Exception as label_error:
                    print(f"Error selecting label {label_variation}: {label_error}")

            if not selected:
                # Try to select the parent label if this is a child label
                if '/' in self.label:
                    parent_label = self.label.split('/')[0]
                    print(f"Trying parent label: {parent_label}")
                    try:
                        status, count = mail.select(f'"{parent_label}"')
                        if status == 'OK':
                            print(f"Successfully selected parent label: {parent_label}")
                            print(f"Found {int(count[0])} messages in this label")
                            selected = True
                        else:
                            print(f"Could not select parent label. Falling back to INBOX")
                            mail.select('INBOX')
                    except Exception as e:
                        print(f"Error selecting parent label: {e}")
                        mail.select('INBOX')
                else:
                    print(f"Could not select any label variation. Falling back to INBOX")
                    mail.select('INBOX')

                print(f"Available labels: {', '.join(self.available_labels)}")

            # Search for emails in the label with date filtering
            # We'll use a direct approach to get emails in reverse order (newest first)

            # Determine search criteria based on settings
            if self.only_unread:
                base_criteria = 'UNSEEN'
            else:
                base_criteria = 'ALL'

            print(f"Searching for {base_criteria} emails in the label")

            # If we're filtering by recent days, calculate the date cutoff
            if self.recent_days > 0:
                # Calculate cutoff date for recent emails
                cutoff_date = datetime.now() - timedelta(days=self.recent_days)
                date_str = cutoff_date.strftime("%d-%b-%Y")

                # Search for emails since the cutoff date
                print(f"Filtering emails since {date_str} (last {self.recent_days} days)")
                search_criteria = f'({base_criteria} SINCE {date_str})'
            else:
                search_criteria = base_criteria

            # First, get the total number of messages in the mailbox
            try:
                status, count_data = mail.select(readonly=True)
                if status == 'OK':
                    total_messages = int(count_data[0])
                    print(f"Total messages in mailbox: {total_messages}")

                    # Reselect the mailbox in read-write mode
                    mail.close()
                    mail.select(self.label)
                else:
                    total_messages = 0
            except Exception as e:
                print(f"Error getting message count: {e}")
                total_messages = 0

            # Get all message IDs in reverse order (newest first)
            try:
                # Try to search with the criteria
                if total_messages > 0:
                    # Get message sequence numbers in reverse order
                    # This gets the newest messages first
                    status, messages = mail.search(None, search_criteria)

                    if status != 'OK' or not messages[0]:
                        # If no messages found with date filter, fall back to all messages
                        print(f"No messages found with criteria: {search_criteria}. Falling back to ALL")
                        status, messages = mail.search(None, base_criteria)
                else:
                    # Fallback if we couldn't get the total count
                    status, messages = mail.search(None, search_criteria)

                    if status != 'OK' or not messages[0]:
                        # If no messages found with date filter, fall back to all messages
                        print(f"No messages found with criteria: {search_criteria}. Falling back to ALL")
                        status, messages = mail.search(None, base_criteria)
            except Exception as search_error:
                # If search with date fails, fall back to basic search
                print(f"Error searching with criteria {search_criteria}: {search_error}")
                print("Falling back to basic search")
                status, messages = mail.search(None, base_criteria)

            if status != 'OK':
                print(f"Error searching for messages: {status}")
                return results

            # Get list of message IDs
            message_ids = messages[0].split()

            # If no messages found, return early
            if not message_ids:
                print("No messages found in the selected label")
                return results

            # Get the total count for logging
            total_message_count = len(message_ids)
            print(f"Found {total_message_count} messages in total")

            # Reverse the list to get newest first
            # IMAP returns messages in ascending order (oldest first)
            message_ids = list(reversed(message_ids))

            print(f"Processing emails from newest to oldest (reversed message order)")

            # Limit to max_emails
            if len(message_ids) > self.max_emails:
                print(f"Found {len(message_ids)} emails, limiting to {self.max_emails}")
                print(f"To process more emails, increase the max_emails value (currently {self.max_emails})")
                message_ids = message_ids[:self.max_emails]
            else:
                print(f"Found {len(message_ids)} emails to process")

            # Print the first few message IDs for debugging
            if message_ids:
                # Print the first few and last few message IDs to verify order
                first_ids = [msg_id.decode('utf-8') for msg_id in message_ids[:3]]
                last_ids = [msg_id.decode('utf-8') for msg_id in message_ids[-3:]] if len(message_ids) > 3 else []

                print(f"First few message IDs (should be newest): {', '.join(first_ids)}")
                if last_ids:
                    print(f"Last few message IDs (should be oldest): {', '.join(last_ids)}")

                # Print numeric values to verify they're in descending order
                try:
                    numeric_first = [int(id) for id in first_ids]
                    numeric_last = [int(id) for id in last_ids] if last_ids else []
                    print(f"Numeric first IDs: {numeric_first}")
                    if numeric_last:
                        print(f"Numeric last IDs: {numeric_last}")

                    # Verify order
                    if numeric_first and numeric_last and numeric_first[0] > numeric_last[0]:
                        print("✓ Message IDs are correctly ordered (newest first)")
                    elif numeric_first and numeric_last:
                        print("⚠ Message IDs may not be in correct order!")
                except ValueError:
                    # IDs might not be numeric
                    print("Message IDs are not numeric, can't verify order")
            else:
                print("No message IDs found to process")

            for msg_id in message_ids:
                try:
                    # Get message data
                    status, msg_data = mail.fetch(msg_id, '(RFC822)')

                    if status != 'OK':
                        print(f"Error fetching message {msg_id}: {status}")
                        results['errors'] += 1
                        continue

                    # Parse message
                    raw_email = msg_data[0][1]
                    msg = email.message_from_bytes(raw_email)

                    # Get message ID
                    message_id = msg.get('Message-ID', '')

                    # Get email subject and sender before checking if already replied
                    subject = msg.get('Subject', 'Job Requirement')
                    to_email = msg.get('Reply-To', msg.get('From', ''))

                    print(f"Processing email - ID: {message_id}, Subject: '{subject}', From: {to_email}")

                    # Skip if already replied recently
                    if self.is_already_replied(message_id):
                        print(f"Skipping already replied message: {message_id}, Subject: '{subject}'")
                        results['skipped'] += 1
                        continue

                    print(f"Will process email - ID: {message_id}, Subject: '{subject}', From: {to_email}")

                    try:
                        # Reload hotlist data to ensure we have the latest consultant information
                        if self.hotlist_handler:
                            self.hotlist_handler.load_consultants()
                            print(f"[INFO] Reloaded {len(self.hotlist_handler.consultants)} consultants from hotlist for email processing")

                        # Extract full job description from email for analysis
                        job_description = self.extract_job_description(msg)
                        print(f"[INFO] Extracted job description: {len(job_description)} characters")

                        # Try AI matching first if available
                        ai_consultant = None
                        ai_message = None
                        if self.ai_matcher and job_description:
                            print(f"[INFO] Attempting AI-powered consultant matching for subject: '{subject}'")
                            ai_consultant, ai_message, ai_response = self.ai_match_consultant_to_job(job_description, subject)

                            if ai_consultant and ai_message:
                                print(f"[INFO] ✅ AI successfully matched consultant: {ai_consultant.get('name', 'Unknown')}")

                                # Send AI-powered reply
                                if self.send_reply_with_ai_consultant(message_id, to_email, subject, ai_consultant, ai_message, original_msg=msg):
                                    results['replied'] += 1
                                    self.emails_sent += 1
                                    self.emails_with_resumes += 1
                                    print(f"Successfully sent AI-powered reply with consultant {ai_consultant.get('name', 'Unknown')}")

                                    # Track AI matches
                                    ai_skill = ai_consultant.get('keyword_matched', 'AI Selected')
                                    if ai_skill in self.technology_matches:
                                        self.technology_matches[ai_skill] += 1
                                    else:
                                        self.technology_matches[ai_skill] = 1

                                    # Continue to next email since we successfully processed this one
                                    results['processed'] += 1
                                    continue
                                else:
                                    results['errors'] += 1
                                    print(f"Failed to send AI-powered reply")
                            else:
                                print(f"[INFO] AI matching failed, falling back to keyword matching")

                        # Fallback to traditional keyword matching if AI fails or is not available
                        print(f"[INFO] Using traditional keyword matching for subject: '{subject}'")
                        matching_consultants = self.match_subject_to_technology(subject)

                        # Log the matching process
                        print(f"[INFO] Subject: '{subject}' - Found {len(matching_consultants) if matching_consultants else 0} matching consultants")

                        # Validate that we have actual skill matches (not just random consultants)
                        valid_matches = []
                        if matching_consultants:
                            for consultant in matching_consultants:
                                # Check if consultant has a valid keyword_matched field
                                keyword_matched = consultant.get('keyword_matched', '')
                                consultant_name = consultant.get('name', 'Unknown')
                                consultant_skills = consultant.get('skills', '')

                                if keyword_matched and keyword_matched.strip():
                                    # Double-check that the keyword is actually in the consultant's skills
                                    if keyword_matched.lower() in consultant_skills.lower():
                                        valid_matches.append(consultant)
                                        print(f"[INFO] ✅ Valid match: {consultant_name} for skill '{keyword_matched}' (found in skills: '{consultant_skills}')")
                                    else:
                                        print(f"[WARNING] ❌ Consultant {consultant_name} matched for '{keyword_matched}' but skill not found in '{consultant_skills}' - excluding")
                                else:
                                    print(f"[WARNING] ❌ Consultant {consultant_name} has no valid keyword match - excluding")

                        if valid_matches and len(valid_matches) > 0:
                            # Found valid technology match in subject, send all matching consultants in one email
                            print(f"[INFO] Sending {len(valid_matches)} consultants with valid skill matches for subject: {subject}")

                            # Track technology matches
                            for consultant in valid_matches:
                                keyword = consultant.get('keyword_matched', '')
                                if keyword:
                                    if keyword in self.technology_matches:
                                        self.technology_matches[keyword] += 1
                                    else:
                                        self.technology_matches[keyword] = 1

                            # Send a single email with all matching consultants
                            if self.send_reply_with_multiple_resumes(message_id, to_email, subject, valid_matches, original_msg=msg):
                                results['replied'] += 1
                                self.emails_sent += 1
                                self.emails_with_resumes += 1
                                print(f"Successfully replied to email with {len(valid_matches)} matching consultants")
                            else:
                                results['errors'] += 1
                                print(f"Failed to reply to email with matching consultants")
                        else:
                            # No valid technology match in subject, send hotlist
                            print(f"[INFO] No valid technology match found in subject: '{subject}' - sending hotlist image only")

                            # Send hotlist image as fallback
                            if self.send_reply_with_hotlist(message_id, to_email, subject, original_msg=msg):
                                results['replied'] += 1
                                self.emails_sent += 1
                                self.emails_with_hotlist += 1
                                print(f"Successfully sent hotlist to {to_email}")
                            else:
                                results['errors'] += 1
                                print(f"Failed to send hotlist to {to_email}")
                    except Exception as e:
                        print(f"Error processing email with subject '{subject}': {str(e)}")
                        import traceback
                        traceback.print_exc()
                        results['errors'] += 1

                    results['processed'] += 1

                except Exception as e:
                    print(f"Error processing message {msg_id}: {e}")
                    results['errors'] += 1

            # Logout
            mail.logout()

        except Exception as e:
            print(f"Error connecting to email server: {e}")
            results['errors'] += 1

        # Record end time
        self.end_time = formatdate(localtime=True)

        # Print statistics
        print(f"Email processing statistics:")
        print(f"- Emails sent: {self.emails_sent}")
        print(f"- Emails with resumes: {self.emails_with_resumes}")
        print(f"- Emails with hotlist: {self.emails_with_hotlist}")
        print(f"- Technology matches: {self.technology_matches}")

        return results
