#!/usr/bin/env python3
"""
Simple test for email formatting function
"""

def test_email_formatting():
    """Test the email formatting function directly"""
    print("📧 TESTING EMAIL FORMATTING FUNCTION")
    print("=" * 60)
    
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize matcher
        matcher = EnhancedAIConsultantMatcher()
        
        # Mock AI response
        ai_response = {
            "job_summary": "a Senior DevOps Engineer with Azure DevOps and OpenShift expertise",
            "selected_consultants": [
                {
                    "name": "<PERSON>nda<PERSON>",
                    "match_confidence": "High",
                    "skill_match_percentage": 88,
                    "key_matching_skills": ["Azure DevOps", "OpenShift", "Kubernetes", "CI/CD", "Docker"],
                    "match_reasoning": "Strong background in Azure DevOps, OpenShift container orchestration, and CI/CD pipeline development"
                },
                {
                    "name": "Laxman Gite", 
                    "match_confidence": "High",
                    "skill_match_percentage": 85,
                    "key_matching_skills": ["DevOps", "Azure", "Kubernetes", "Terraform", "Jenkins"],
                    "match_reasoning": "Extensive DevOps experience with Azure cloud platform and infrastructure automation"
                }
            ]
        }
        
        # Mock selected consultants
        selected_consultants = [
            {
                "name": "Kondaru",
                "experience": "12",
                "location": "IL",
                "visa_status": "H1B",
                "relocation": "Yes",
                "match_confidence": "High",
                "skill_match_percentage": 88,
                "key_matching_skills": ["Azure DevOps", "OpenShift", "Kubernetes", "CI/CD", "Docker"],
                "match_reasoning": "Strong background in Azure DevOps, OpenShift container orchestration, and CI/CD pipeline development"
            },
            {
                "name": "Laxman Gite",
                "experience": "10",
                "location": "NJ",
                "visa_status": "H1B", 
                "relocation": "Yes",
                "match_confidence": "High",
                "skill_match_percentage": 85,
                "key_matching_skills": ["DevOps", "Azure", "Kubernetes", "Terraform", "Jenkins"],
                "match_reasoning": "Extensive DevOps experience with Azure cloud platform and infrastructure automation"
            }
        ]
        
        # Mock client filters
        client_filters = {
            "visa_requirements": ["H1B"],
            "location_requirements": ["Southfield, MI"]
        }
        
        # Generate professional email
        email = matcher._generate_professional_email(ai_response, selected_consultants, client_filters)
        
        print("📧 GENERATED PROFESSIONAL EMAIL:")
        print("=" * 60)
        print(email)
        print("=" * 60)
        
        # Check email quality
        checks = {
            "Has opening": email.startswith("Thank you for your job requirement"),
            "Has job summary": "Based on" in email,
            "Has recommendations": "**Recommendation" in email,
            "Has multiple consultants": "Consultant #1" in email and "Consultant #2" in email,
            "Has skills": "Skills/Technologies:" in email,
            "Has experience": "Experience:" in email,
            "Has match confidence": "Match Confidence:" in email,
            "Has client requirements": "Client Requirements Noted:" in email,
            "Has closing": "Best regards" in email,
            "Has signature": "Mukesh Saini" in email
        }
        
        passed = sum(checks.values())
        total = len(checks)
        
        print(f"\n📊 EMAIL QUALITY CHECK: {passed}/{total}")
        for check, result in checks.items():
            status = "✅" if result else "❌"
            print(f"  {status} {check}")
        
        if passed >= total * 0.8:
            print(f"\n🎉 EMAIL QUALITY: EXCELLENT!")
            return True
        else:
            print(f"\n⚠️ EMAIL QUALITY: NEEDS IMPROVEMENT")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_email_formatting()
    exit(0 if success else 1)
