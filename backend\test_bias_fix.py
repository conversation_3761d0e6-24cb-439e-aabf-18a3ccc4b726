#!/usr/bin/env python3
"""
Test Enhanced AI Matching Bias Fix
Verifies that the system no longer always selects the same consultants
and properly randomizes selections.
"""

import os
import sys
import json
import logging
import time
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_bias_fix():
    """Test that bias has been fixed in consultant selection"""
    print("🧪 TESTING ENHANCED AI MATCHING BIAS FIX")
    print("=" * 80)
    
    try:
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize components
        print("🔧 Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False
        
        print("✅ AI connection successful")
        
        # Clear cache to force fresh matching
        print("🗑️ Clearing JD cache to force fresh matching...")
        matcher.clear_jd_cache()
        
        # Load consultants
        consultants = load_test_consultants()
        if not consultants:
            print("❌ No test consultants loaded")
            return False
        
        print(f"📋 Loaded {len(consultants)} test consultants")
        
        # Test with diverse job descriptions
        test_cases = get_diverse_test_cases()
        
        selection_results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n" + "="*80)
            print(f"🔍 TEST {i}: {test_case['title']}")
            print("="*80)
            
            selected_names = test_single_case(matcher, test_case, consultants, i)
            
            if selected_names:
                selection_results[test_case['title']] = selected_names
                print(f"✅ Selected: {', '.join(selected_names)}")
            else:
                print(f"❌ No matches found")
            
            # Brief pause between tests
            if i < len(test_cases):
                print(f"\n⏱️ Waiting 3 seconds before next test...")
                time.sleep(3)
        
        # Analyze results for bias
        print(f"\n🔍 BIAS ANALYSIS:")
        print("="*80)
        analyze_selection_bias(selection_results)
        
        print(f"\n🎉 BIAS FIX TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error in bias fix test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_case(matcher, test_case, consultants, test_num):
    """Test a single case and return selected consultant names"""
    try:
        title = test_case['title']
        description = test_case['description']
        expected_skills = test_case['expected_skills']
        
        print(f"📋 Job Title: {title}")
        print(f"📄 Expected Skills: {', '.join(expected_skills)}")
        
        # Run Enhanced AI Matching
        print(f"\n🤖 Running Enhanced AI Matching...")
        selected_consultants, ai_message, ai_response = matcher.enhanced_match_consultant_to_job(
            description, consultants
        )
        
        if selected_consultants and len(selected_consultants) > 0:
            selected_names = []
            for consultant in selected_consultants:
                name = consultant.get('name', 'Unknown')
                confidence = consultant.get('match_confidence', 'Unknown')
                skill_match = consultant.get('skill_match_percentage', 0)
                selected_names.append(name)
                print(f"   👤 {name} - {confidence} confidence ({skill_match}% skill match)")
            
            return selected_names
        else:
            return []
        
    except Exception as e:
        print(f"❌ Error testing case {test_num}: {e}")
        return []

def get_diverse_test_cases():
    """Get diverse test cases to check for bias"""
    return [
        {
            'title': 'Python Data Scientist',
            'description': '''
            We are looking for a Python Data Scientist with machine learning expertise.
            
            Required Skills:
            - 5+ years of Python programming experience
            - Strong experience with pandas, numpy, scikit-learn
            - Machine learning model development and deployment
            - Data visualization with matplotlib/seaborn
            - SQL database experience
            - Experience with Jupyter notebooks
            
            Location: Remote
            Experience Level: Senior (5+ years)
            ''',
            'expected_skills': ['Python', 'Machine Learning', 'pandas', 'numpy', 'SQL']
        },
        {
            'title': 'Java Spring Boot Developer',
            'description': '''
            Java Developer needed for microservices development.
            
            Required Skills:
            - 6+ years of Java development experience
            - Strong experience with Spring Boot and Spring Framework
            - Microservices architecture experience
            - REST API development
            - Maven/Gradle build tools
            - JUnit testing experience
            
            Location: New York, NY
            Experience Level: Senior (6+ years)
            ''',
            'expected_skills': ['Java', 'Spring Boot', 'Microservices', 'REST API', 'Maven']
        },
        {
            'title': 'React Frontend Developer',
            'description': '''
            Frontend Developer specializing in React applications.
            
            Required Skills:
            - 4+ years of React.js development experience
            - Strong JavaScript/TypeScript skills
            - Experience with Redux or Context API
            - HTML5, CSS3, responsive design
            - Experience with modern build tools (Webpack, Vite)
            - Git version control
            
            Location: San Francisco, CA
            Experience Level: Mid-Senior (4+ years)
            ''',
            'expected_skills': ['React', 'JavaScript', 'TypeScript', 'Redux', 'HTML5', 'CSS3']
        },
        {
            'title': 'DevOps AWS Engineer',
            'description': '''
            DevOps Engineer with AWS cloud expertise.
            
            Required Skills:
            - 5+ years of DevOps experience
            - Strong AWS services experience (EC2, S3, RDS, Lambda)
            - Docker and Kubernetes expertise
            - CI/CD pipeline development (Jenkins, GitLab CI)
            - Infrastructure as Code (Terraform, CloudFormation)
            - Linux system administration
            
            Location: Austin, TX
            Experience Level: Senior (5+ years)
            ''',
            'expected_skills': ['AWS', 'DevOps', 'Docker', 'Kubernetes', 'Terraform', 'Jenkins']
        },
        {
            'title': 'QA Automation Engineer',
            'description': '''
            QA Automation Engineer for test framework development.
            
            Required Skills:
            - 4+ years of QA automation experience
            - Strong experience with Selenium WebDriver
            - Test framework development (TestNG, JUnit)
            - API testing with Postman/REST Assured
            - Performance testing experience
            - Continuous integration testing
            
            Location: Chicago, IL
            Experience Level: Senior (4+ years)
            ''',
            'expected_skills': ['QA', 'Selenium', 'TestNG', 'API Testing', 'Automation', 'Performance Testing']
        }
    ]

def analyze_selection_bias(selection_results):
    """Analyze selection results for bias patterns"""
    if not selection_results:
        print("❌ No selection results to analyze")
        return
    
    # Count how many times each consultant was selected
    consultant_counts = {}
    total_selections = 0
    
    for job_title, selected_names in selection_results.items():
        for name in selected_names:
            consultant_counts[name] = consultant_counts.get(name, 0) + 1
            total_selections += 1
    
    print(f"📊 Selection Statistics:")
    print(f"   Total job descriptions tested: {len(selection_results)}")
    print(f"   Total consultant selections: {total_selections}")
    print(f"   Unique consultants selected: {len(consultant_counts)}")
    
    # Sort by selection frequency
    sorted_consultants = sorted(consultant_counts.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n📈 Consultant Selection Frequency:")
    for name, count in sorted_consultants:
        percentage = (count / total_selections) * 100
        print(f"   {name}: {count} times ({percentage:.1f}%)")
    
    # Check for bias (if any consultant is selected more than 60% of the time)
    if sorted_consultants:
        top_consultant, top_count = sorted_consultants[0]
        top_percentage = (top_count / len(selection_results)) * 100
        
        if top_percentage > 60:
            print(f"\n⚠️ POTENTIAL BIAS DETECTED:")
            print(f"   {top_consultant} was selected in {top_percentage:.1f}% of cases")
            print(f"   This suggests the system may still have selection bias")
        else:
            print(f"\n✅ BIAS CHECK PASSED:")
            print(f"   Most selected consultant ({top_consultant}) appeared in {top_percentage:.1f}% of cases")
            print(f"   This indicates good diversity in consultant selection")
    
    # Check for diversity across different job types
    print(f"\n🎯 Job-Specific Analysis:")
    for job_title, selected_names in selection_results.items():
        print(f"   {job_title}: {', '.join(selected_names)}")

def load_test_consultants():
    """Load consultant data from CSV"""
    consultants = []
    
    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Skip header
    for line in lines[1:]:
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]
            
            # Try to find matching resume file
            resume_path = find_resume_file(name)
            
            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)
    
    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None
    
    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')
    
    # List all resume files
    resume_files = os.listdir(resumes_dir)
    
    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')
        
        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)
    
    return None

if __name__ == "__main__":
    success = test_bias_fix()
    sys.exit(0 if success else 1)
