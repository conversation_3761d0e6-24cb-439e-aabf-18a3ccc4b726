#!/usr/bin/env python3
"""
Test Enhanced AI Matching with 5 Random Job Descriptions
Tests both matching and non-matching scenarios with fallback to hotlist
"""

import os
import sys
import json
import logging
import time
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_5_random_jds():
    """Test with 5 diverse job descriptions"""
    print("🧪 TESTING ENHANCED AI MATCHING WITH 5 RANDOM JOB DESCRIPTIONS")
    print("=" * 80)

    try:
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher

        # Initialize components
        print("🔧 Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()

        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False

        print("✅ AI connection successful")

        # Load consultants
        consultants = load_test_consultants()
        if not consultants:
            print("❌ No test consultants loaded")
            return False

        print(f"📋 Loaded {len(consultants)} test consultants")

        # Define 5 diverse job descriptions
        job_descriptions = get_5_random_job_descriptions()

        print(f"\n🎯 Testing with {len(job_descriptions)} diverse job descriptions...")

        # Test each JD
        for i, jd_data in enumerate(job_descriptions, 1):
            print(f"\n" + "="*80)
            print(f"🔍 TEST {i}: {jd_data['title']}")
            print("="*80)

            test_single_jd(matcher, jd_data, consultants, i)

            # Brief pause between tests
            if i < len(job_descriptions):
                print(f"\n⏱️ Waiting 3 seconds before next test...")
                time.sleep(3)

        print(f"\n🎉 ALL 5 JOB DESCRIPTION TESTS COMPLETED!")
        return True

    except Exception as e:
        print(f"❌ Error in 5 JD testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_jd(matcher, jd_data, consultants, test_num):
    """Test a single job description"""
    try:
        title = jd_data['title']
        description = jd_data['description']
        expected_match = jd_data['expected_match']

        print(f"📋 Job Title: {title}")
        print(f"📄 Description Preview: {description[:200]}...")
        print(f"🎯 Expected Result: {'MATCH' if expected_match else 'NO MATCH (should fallback to hotlist)'}")

        # Test Phase 2 Enhanced Matching
        print(f"\n🤖 Running Enhanced AI Matching...")
        selected_consultants, ai_message, ai_response = matcher.enhanced_match_consultant_to_job(
            description, consultants
        )

        if selected_consultants and len(selected_consultants) > 0:
            print(f"✅ ENHANCED AI MATCH FOUND!")
            print(f"👥 Selected {len(selected_consultants)} consultant(s):")

            for j, consultant in enumerate(selected_consultants, 1):
                print(f"   {j}. {consultant.get('name', 'Unknown')}")
                print(f"      Confidence: {consultant.get('match_confidence', 'Unknown')}")
                print(f"      Skill Match: {consultant.get('skill_match_percentage', 0)}%")
                print(f"      Key Skills: {', '.join(consultant.get('key_matching_skills', [])[:5])}")
                print(f"      Reasoning: {consultant.get('match_reasoning', 'No reasoning')[:100]}...")

            print(f"\n📧 AI Generated Email Preview:")
            print("-" * 50)
            print(ai_message[:300] + "..." if len(ai_message) > 300 else ai_message)
            print("-" * 50)

            # Verify expectation
            if expected_match:
                print(f"✅ RESULT: As expected - Enhanced AI found suitable match(es)")
            else:
                print(f"⚠️ RESULT: Unexpected - Enhanced AI found match when none expected")

        else:
            print(f"❌ NO ENHANCED AI MATCH FOUND")

            # Test fallback to traditional hotlist matching
            print(f"\n🔄 Testing fallback to traditional hotlist matching...")

            # Simulate traditional hotlist fallback logic
            fallback_result = test_fallback_matching(description, consultants)

            if fallback_result:
                print(f"✅ FALLBACK SUCCESS: Traditional matching found consultant")
                print(f"👤 Fallback Selected: {fallback_result.get('name', 'Unknown')}")
                print(f"📧 Fallback Email: {fallback_result.get('email_preview', 'No preview')[:200]}...")
            else:
                print(f"❌ FALLBACK FAILED: No consultant found via traditional matching")

            # Verify expectation
            if not expected_match:
                print(f"✅ RESULT: As expected - No enhanced match, fallback attempted")
            else:
                print(f"⚠️ RESULT: Unexpected - Expected enhanced match but none found")

        # Show cache and log status
        print(f"\n📊 System Status:")
        print(f"   JD Cache Entries: {len(matcher.jd_cache)}")
        print(f"   Match Log Entries: {len(matcher.match_log)}")
        print(f"   Skill Categories: {len(matcher.skill_categories)}")

    except Exception as e:
        print(f"❌ Error testing JD {test_num}: {e}")
        import traceback
        traceback.print_exc()

def test_fallback_matching(job_description, consultants):
    """Test traditional fallback matching (simulated)"""
    try:
        # Simulate traditional keyword-based matching
        # Look for basic technology keywords in job description
        keywords = ['java', '.net', 'python', 'react', 'angular', 'aws', 'azure', 'sql']

        jd_lower = job_description.lower()
        found_keywords = [kw for kw in keywords if kw in jd_lower]

        if found_keywords and consultants:
            # Find consultant with matching skills
            for consultant in consultants:
                consultant_skills = consultant.get('skills', '').lower()
                for keyword in found_keywords:
                    if keyword in consultant_skills:
                        return {
                            'name': consultant.get('name', 'Unknown'),
                            'email_preview': f"Traditional hotlist match for {keyword}",
                            'match_type': 'traditional_keyword',
                            'matched_keyword': keyword
                        }

        # If no specific match, return hotlist fallback
        return {
            'name': 'Hotlist',
            'email_preview': 'Sending complete hotlist as no specific match found',
            'match_type': 'hotlist_fallback'
        }

    except Exception as e:
        print(f"⚠️ Fallback matching error: {e}")
        return None

def get_5_random_job_descriptions():
    """Get 5 diverse job descriptions for testing"""
    return [
        {
            'title': 'Senior .NET Full Stack Developer',
            'description': '''
            We are seeking a Senior .NET Full Stack Developer for our enterprise applications.

            Required Skills:
            - 8+ years of .NET Core/.NET Framework experience
            - Strong C# programming skills
            - Experience with ASP.NET MVC and Web API
            - Frontend development with Angular or React
            - SQL Server database experience
            - Knowledge of Azure cloud services
            - Experience with DevOps practices and CI/CD

            Location: New York, NY (Hybrid)
            Visa: H1B welcome
            Experience Level: Senior (8+ years)
            ''',
            'expected_match': True  # Should match our .NET consultants
        },
        {
            'title': 'Java Spring Boot Microservices Developer',
            'description': '''
            Looking for an experienced Java developer to work on microservices architecture.

            Required Skills:
            - 6+ years of Java development experience
            - Strong experience with Spring Boot and Spring Framework
            - Microservices architecture and design patterns
            - Experience with Docker and Kubernetes
            - Knowledge of AWS or Azure cloud platforms
            - RESTful API development
            - Experience with Maven/Gradle

            Location: Remote (US timezone)
            Visa: H1B sponsorship available
            Experience Level: Senior (6+ years)
            ''',
            'expected_match': True  # Should match our Java consultants
        },
        {
            'title': 'Blockchain Solidity Smart Contract Developer',
            'description': '''
            Seeking a Blockchain Developer specializing in Solidity smart contracts.

            Required Skills:
            - 5+ years of Solidity programming experience
            - Deep understanding of Ethereum blockchain
            - Experience with Web3.js and Truffle framework
            - Knowledge of DeFi protocols and NFT standards
            - Experience with Hardhat development environment
            - Understanding of gas optimization techniques
            - Cryptography and security best practices

            Location: San Francisco, CA
            Visa: US Citizens only
            Experience Level: Expert (5+ years blockchain)
            ''',
            'expected_match': False  # Unlikely to match - very specialized
        },
        {
            'title': 'DevOps Engineer - AWS Cloud Infrastructure',
            'description': '''
            We need a DevOps Engineer to manage our AWS cloud infrastructure.

            Required Skills:
            - 5+ years of DevOps and cloud infrastructure experience
            - Strong experience with AWS services (EC2, S3, Lambda, ECS, RDS)
            - Expertise in containerization (Docker, Kubernetes)
            - Infrastructure as Code (Terraform, CloudFormation)
            - CI/CD pipeline development (Jenkins, GitLab CI)
            - Monitoring and logging (CloudWatch, ELK stack)
            - Scripting skills (Python, Bash, PowerShell)

            Location: Austin, TX (Remote friendly)
            Visa: Any valid work authorization
            Experience Level: Senior (5+ years)
            ''',
            'expected_match': True  # Should match our DevOps consultants
        },
        {
            'title': 'Quantum Computing Research Scientist',
            'description': '''
            Research position for Quantum Computing algorithm development.

            Required Skills:
            - PhD in Physics, Computer Science, or Mathematics
            - 3+ years of quantum computing research experience
            - Proficiency in Qiskit, Cirq, or similar quantum frameworks
            - Strong background in quantum mechanics and linear algebra
            - Experience with quantum algorithms (Shor's, Grover's, VQE)
            - Programming skills in Python and C++
            - Publications in quantum computing journals

            Location: Boston, MA (On-site required)
            Visa: US Citizens or Green Card holders only
            Experience Level: Research/PhD level
            ''',
            'expected_match': False  # Very unlikely to match - highly specialized research
        }
    ]

def load_test_consultants():
    """Load consultant data from CSV"""
    consultants = []

    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []

    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Skip header
    for line in lines[1:]:
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]

            # Try to find matching resume file
            resume_path = find_resume_file(name)

            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)

    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None

    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')

    # List all resume files
    resume_files = os.listdir(resumes_dir)

    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')

        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)

    return None

if __name__ == "__main__":
    success = test_5_random_jds()
    sys.exit(0 if success else 1)
