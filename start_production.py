#!/usr/bin/env python3
"""
Auto Resume Application - Production Startup Script
Optimized for production deployment with error handling and monitoring
"""

import os
import sys
import time
import signal
import logging
import subprocess
import psutil
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = False

    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        logger.info("Checking dependencies...")

        try:
            # Check Python packages
            import flask
            import pandas
            import nltk
            import tqdm
            import psutil
            logger.info("All Python dependencies are installed")

            # Check if hotlist.csv exists
            if not os.path.exists('hotlist.csv'):
                logger.warning("hotlist.csv not found - creating sample file")
                self.create_sample_hotlist()

            # Check if config.json exists
            if not os.path.exists('config.json'):
                logger.warning("config.json not found - creating default config")
                self.create_default_config()

            return True

        except ImportError as e:
            logger.error(f"Missing dependency: {e}")
            logger.info("Run: pip install -r backend/requirements.txt")
            return False

    def create_sample_hotlist(self):
        """Create a sample hotlist.csv file"""
        sample_data = """Name,skills,EXP,VISA,Location,Willing to Relocate
John Doe,Java Developer,5,H1B,New York,Yes
Jane Smith,Python Developer,3,USC,California,No
Mike Johnson,.NET Developer,7,GC,Texas,Yes"""

        with open('hotlist.csv', 'w') as f:
            f.write(sample_data)
        logger.info("Created sample hotlist.csv")

    def create_default_config(self):
        """Create default configuration file"""
        import json

        default_config = {
            "email": "",
            "password": "",
            "label": "JobRequirements",
            "hotlist_image": "",
            "recent_days": 1
        }

        with open('config.json', 'w') as f:
            json.dump(default_config, f, indent=2)
        logger.info("Created default config.json")

    def optimize_system(self):
        """Apply system optimizations for production"""
        logger.info("Applying system optimizations...")

        try:
            # Set process priority
            current_process = psutil.Process()
            current_process.nice(psutil.NORMAL_PRIORITY_CLASS if os.name == 'nt' else 0)

            # Set environment variables for production
            os.environ['FLASK_ENV'] = 'production'
            os.environ['PYTHONOPTIMIZE'] = '1'
            os.environ['PYTHONDONTWRITEBYTECODE'] = '1'

            logger.info("System optimizations applied")

        except Exception as e:
            logger.warning(f"Could not apply all optimizations: {e}")

    def start_backend(self):
        """Start the backend Flask application"""
        logger.info("Starting backend server...")

        try:
            # Change to backend directory
            backend_dir = Path(__file__).parent / 'backend'

            # Start backend with optimized settings
            cmd = [
                sys.executable, 'app.py'
            ]

            self.backend_process = subprocess.Popen(
                cmd,
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a moment to check if it started successfully
            time.sleep(3)

            if self.backend_process.poll() is None:
                logger.info("Backend server started successfully")
                return True
            else:
                _, stderr = self.backend_process.communicate()
                logger.error(f"Backend failed to start: {stderr}")
                return False

        except Exception as e:
            logger.error(f"Error starting backend: {e}")
            return False

    def check_backend_health(self):
        """Check if backend is responding"""
        try:
            import requests
            response = requests.get('http://localhost:5000/api/config', timeout=5)
            return response.status_code == 200
        except:
            return False

    def monitor_processes(self):
        """Monitor backend and frontend processes"""
        logger.info("Starting process monitoring...")

        while self.running:
            try:
                # Check backend process
                if self.backend_process and self.backend_process.poll() is not None:
                    logger.error("Backend process died, restarting...")
                    self.start_backend()

                # Check backend health
                if not self.check_backend_health():
                    logger.warning("Backend health check failed")

                # Log system stats every 5 minutes
                if int(time.time()) % 300 == 0:
                    self.log_system_stats()

                time.sleep(10)  # Check every 10 seconds

            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Monitor error: {e}")
                time.sleep(30)

    def log_system_stats(self):
        """Log system performance statistics"""
        try:
            # CPU and Memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            logger.info(f"System Stats - CPU: {cpu_percent}%, Memory: {memory.percent}%")

            # Process-specific stats
            if self.backend_process:
                try:
                    process = psutil.Process(self.backend_process.pid)
                    process_memory = process.memory_info().rss / 1024 / 1024  # MB
                    logger.info(f"Backend Process - Memory: {process_memory:.1f}MB")
                except:
                    pass

        except Exception as e:
            logger.warning(f"Could not get system stats: {e}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info("Received shutdown signal, stopping services...")
        self.stop()

    def stop(self):
        """Stop all services gracefully"""
        self.running = False

        if self.backend_process:
            logger.info("Stopping backend server...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=10)
                logger.info("Backend stopped gracefully")
            except subprocess.TimeoutExpired:
                logger.warning("Force killing backend process")
                self.backend_process.kill()

        logger.info("All services stopped")

    def start(self):
        """Start the production environment"""
        logger.info("Starting Auto Resume Application in Production Mode")
        logger.info("=" * 60)

        # Check dependencies
        if not self.check_dependencies():
            logger.error("Dependency check failed")
            return False

        # Apply optimizations
        self.optimize_system()

        # Start backend
        if not self.start_backend():
            logger.error("Failed to start backend")
            return False

        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.running = True

        # Print startup information
        logger.info("=" * 60)
        logger.info("AUTO RESUME APPLICATION - PRODUCTION MODE")
        logger.info("=" * 60)
        logger.info("Backend API: http://localhost:5000")
        logger.info("Frontend UI: http://localhost:5000")
        logger.info("Dashboard: http://localhost:5000/dashboard")
        logger.info("Skills Manager: http://localhost:5000/skills.html")
        logger.info("=" * 60)
        logger.info("Press Ctrl+C to stop the application")
        logger.info("=" * 60)

        # Start monitoring
        try:
            self.monitor_processes()
        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
        finally:
            self.stop()

        return True

def main():
    """Main entry point"""
    manager = ProductionManager()

    try:
        success = manager.start()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
