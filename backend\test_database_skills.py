#!/usr/bin/env python3
"""
Test Database Skills Usage
Verifies that the system uses existing skills from database instead of extracting.
"""

import os
import sys
import time

def test_database_skills_usage():
    """Test that the system uses database skills instead of extracting"""
    print("🗄️ TESTING DATABASE SKILLS USAGE")
    print("=" * 60)
    
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        print("🔧 Initializing matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test with a known consultant
        consultant_name = "Laxman Gite"
        resume_path = "resumes/Laxman_Gite.pdf"
        
        if not os.path.exists(resume_path):
            print(f"❌ Resume file not found: {resume_path}")
            return False
        
        print(f"👤 Testing with: {consultant_name}")
        print(f"📄 Resume path: {resume_path}")
        
        # Test 1: Check if skills exist in database
        print(f"\n🗄️ CHECKING DATABASE:")
        db_skills = matcher.skill_repository.get_consultant_skills(consultant_name)
        print(f"📊 Skills in database for '{consultant_name}': {len(db_skills)}")
        if db_skills:
            print(f"📋 Sample skills: {', '.join(db_skills[:10])}{'...' if len(db_skills) > 10 else ''}")
        
        # Test 2: Check cache
        print(f"\n💾 CHECKING CACHE:")
        cache_skills = matcher.skills_cache.get(resume_path, [])
        print(f"📊 Skills in cache for '{resume_path}': {len(cache_skills)}")
        if cache_skills:
            print(f"📋 Sample cached skills: {', '.join(cache_skills[:10])}{'...' if len(cache_skills) > 10 else ''}")
        
        # Test 3: Use the new database method
        print(f"\n🚀 TESTING NEW DATABASE METHOD:")
        start_time = time.time()
        
        retrieved_skills = matcher._get_consultant_skills_from_database(consultant_name, resume_path)
        
        retrieval_time = time.time() - start_time
        
        print(f"⏱️ Retrieval time: {retrieval_time:.3f} seconds")
        print(f"📊 Skills retrieved: {len(retrieved_skills)}")
        if retrieved_skills:
            print(f"📋 Sample retrieved skills: {', '.join(retrieved_skills[:10])}{'...' if len(retrieved_skills) > 10 else ''}")
        
        # Test 4: Test with consultant formatting
        print(f"\n📧 TESTING CONSULTANT FORMATTING:")
        
        test_consultants = [
            {
                'name': consultant_name,
                'experience': '10',
                'location': 'NJ',
                'visa_status': 'H1B',
                'relocation': 'Yes',
                'resume_path': resume_path
            }
        ]
        
        start_time = time.time()
        consultant_data = matcher._format_consultant_data_with_resume_analysis(test_consultants)
        formatting_time = time.time() - start_time
        
        print(f"⏱️ Formatting time: {formatting_time:.3f} seconds")
        print(f"📄 Formatted data length: {len(consultant_data)} characters")
        
        # Check if it mentions extraction or database usage
        if "not in cache" in consultant_data.lower():
            print(f"⚠️ WARNING: Still extracting skills (mentions 'not in cache')")
        else:
            print(f"✅ SUCCESS: Using existing skills (no extraction messages)")
        
        # Analysis
        print(f"\n📊 ANALYSIS:")
        
        if retrieval_time < 0.1:
            print(f"✅ FAST RETRIEVAL: {retrieval_time:.3f}s (database/cache)")
        else:
            print(f"⚠️ SLOW RETRIEVAL: {retrieval_time:.3f}s (likely extracting)")
        
        if formatting_time < 1.0:
            print(f"✅ FAST FORMATTING: {formatting_time:.3f}s (using database)")
        else:
            print(f"⚠️ SLOW FORMATTING: {formatting_time:.3f}s (likely extracting)")
        
        if retrieved_skills:
            print(f"✅ SKILLS FOUND: {len(retrieved_skills)} skills retrieved")
        else:
            print(f"❌ NO SKILLS: No skills found for consultant")
        
        # Overall assessment
        if retrieval_time < 0.1 and formatting_time < 1.0 and retrieved_skills:
            print(f"\n🎉 DATABASE USAGE: EXCELLENT!")
            print(f"   ✅ Fast retrieval from database")
            print(f"   ✅ No skill extraction needed")
            print(f"   ✅ Skills successfully found")
            return True
        else:
            print(f"\n⚠️ DATABASE USAGE: NEEDS IMPROVEMENT")
            if retrieval_time >= 0.1:
                print(f"   ❌ Slow retrieval (likely extracting)")
            if formatting_time >= 1.0:
                print(f"   ❌ Slow formatting (likely extracting)")
            if not retrieved_skills:
                print(f"   ❌ No skills found")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_skills_usage()
    sys.exit(0 if success else 1)
