#!/usr/bin/env python3
"""
Test script for the Enhanced AI Consultant Matcher
This script tests the new resume-based AI matching functionality
"""

import os
import sys
import json
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_ai_matcher():
    """Test the Enhanced AI Consultant Matcher"""
    try:
        print("🚀 Testing Enhanced AI Consultant Matcher...")
        
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize the matcher
        print("Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        print("Testing AI connection...")
        if matcher.test_connection():
            print("✅ AI connection successful")
        else:
            print("❌ AI connection failed")
            return False
        
        # Test with sample consultant data
        sample_consultants = [
            {
                'name': '<PERSON>',
                'resume_path': 'resumes/john_doe.pdf',  # This would need to exist for real testing
                'experience': '5',
                'location': 'New York, NY',
                'visa_status': 'H1B',
                'relocation': 'Yes',
                'skills': 'Python, JavaScript, React, Node.js'  # Fallback skills from CSV
            },
            {
                'name': 'Jane Smith',
                'resume_path': 'resumes/jane_smith.pdf',  # This would need to exist for real testing
                'experience': '8',
                'location': 'San Francisco, CA',
                'visa_status': 'Green Card',
                'relocation': 'Remote Only',
                'skills': 'Java, Spring Boot, AWS, Docker'  # Fallback skills from CSV
            }
        ]
        
        # Sample job description
        sample_job_description = """
        We are looking for a Senior Full Stack Developer with the following requirements:
        
        Required Skills:
        - 5+ years of experience in Python development
        - Strong experience with React and JavaScript
        - Experience with Node.js and backend development
        - Knowledge of cloud platforms (AWS preferred)
        - Experience with containerization (Docker)
        
        Location: New York, NY (Hybrid work available)
        Visa Status: H1B sponsorship available
        """
        
        print("\n📋 Testing enhanced AI matching...")
        print("Job Description:", sample_job_description[:200] + "...")
        print(f"Testing with {len(sample_consultants)} consultants")
        
        # Test enhanced matching
        consultant_name, ai_message, ai_response = matcher.enhanced_match_consultant_to_job(
            sample_job_description, sample_consultants
        )
        
        if consultant_name and ai_message:
            print(f"\n✅ Enhanced AI Matching Results:")
            print(f"Selected Consultant: {consultant_name}")
            print(f"Match Confidence: {ai_response.get('match_confidence', 'Unknown')}")
            print(f"Skill Match Percentage: {ai_response.get('skill_match_percentage', 0)}%")
            print(f"Key Matching Skills: {', '.join(ai_response.get('key_matching_skills', []))}")
            print(f"Match Reasoning: {ai_response.get('match_reasoning', 'No reasoning provided')}")
            print(f"\nGenerated Email Message:")
            print("-" * 50)
            print(ai_message)
            print("-" * 50)
            return True
        else:
            print("❌ Enhanced AI matching failed to return results")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required dependencies are installed:")
        print("- google-generativeai")
        print("- PyPDF2")
        print("- python-docx")
        return False
    except Exception as e:
        print(f"❌ Error testing Enhanced AI Matcher: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_skill_extraction():
    """Test skill extraction functionality"""
    try:
        print("\n🧠 Testing skill extraction...")
        
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        matcher = EnhancedAIConsultantMatcher()
        
        # Test with sample resume text (simulating extracted content)
        sample_resume_text = """
        John Doe
        Senior Software Engineer
        
        TECHNICAL SKILLS:
        Programming Languages: Python, JavaScript, Java, TypeScript
        Web Technologies: React, Angular, Node.js, Express.js
        Databases: PostgreSQL, MongoDB, Redis
        Cloud Platforms: AWS, Azure, Google Cloud Platform
        DevOps: Docker, Kubernetes, Jenkins, GitLab CI/CD
        Other: REST APIs, GraphQL, Microservices, Agile Development
        
        EXPERIENCE:
        Senior Software Engineer at Tech Corp (2020-Present)
        - Developed scalable web applications using React and Node.js
        - Implemented microservices architecture using Docker and Kubernetes
        - Worked with AWS services including EC2, S3, and Lambda
        
        Software Engineer at StartupXYZ (2018-2020)
        - Built REST APIs using Python and Django
        - Managed PostgreSQL databases and optimized queries
        - Collaborated in Agile development environment
        """
        
        # Create a temporary file for testing
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
            temp_file.write(sample_resume_text)
            temp_file_path = temp_file.name
        
        try:
            # Test skill extraction (this would normally work with PDF/DOCX files)
            print("Testing skill extraction from resume content...")
            
            # For testing purposes, we'll simulate the skill extraction
            # In real usage, this would extract from actual PDF/DOCX files
            sample_consultants = [
                {
                    'name': 'John Doe',
                    'resume_path': temp_file_path,
                    'experience': '5',
                    'location': 'New York, NY',
                    'visa_status': 'H1B',
                    'relocation': 'Yes'
                }
            ]
            
            # Test bulk skill extraction
            skills_result = matcher.bulk_extract_skills_from_resumes(sample_consultants)
            
            print(f"✅ Skill extraction completed")
            print(f"Extracted skills for {len(skills_result)} consultants:")
            
            for consultant_name, skills in skills_result.items():
                print(f"  {consultant_name}: {len(skills)} skills")
                if skills:
                    print(f"    Sample skills: {', '.join(skills[:5])}{'...' if len(skills) > 5 else ''}")
            
            return True
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ Error testing skill extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resume_parser():
    """Test the resume parser functionality"""
    try:
        print("\n📄 Testing Resume Parser...")
        
        from resume_parser import ResumeParser
        
        parser = ResumeParser()
        print("✅ Resume Parser initialized successfully")
        
        # Test text cleaning
        sample_text = "  This   is    a   test   text   with   extra   spaces  \n\n\n  "
        cleaned_text = parser._clean_text(sample_text)
        print(f"Text cleaning test: '{sample_text}' -> '{cleaned_text}'")
        
        # Test basic info extraction
        sample_resume_text = """
        John Doe
        Email: <EMAIL>
        Phone: (*************
        LinkedIn: linkedin.com/in/johndoe
        GitHub: github.com/johndoe
        """
        
        basic_info = parser.extract_basic_info(sample_resume_text)
        print(f"✅ Basic info extraction test:")
        print(f"  Emails: {basic_info['emails']}")
        print(f"  Phones: {basic_info['phones']}")
        print(f"  LinkedIn: {basic_info['linkedin']}")
        print(f"  GitHub: {basic_info['github']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Resume Parser: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_skill_repository():
    """Test the skill repository functionality"""
    try:
        print("\n🗄️ Testing Skill Repository...")
        
        from skill_repository import SkillRepository
        
        # Create a test repository
        repo = SkillRepository('test_skills.json')
        print("✅ Skill Repository initialized successfully")
        
        # Test adding skills
        test_skills = ['Python', 'JavaScript', 'React', 'Node.js', 'AWS']
        repo.add_consultant_skills('Test Consultant', test_skills)
        
        # Test retrieving skills
        retrieved_skills = repo.get_consultant_skills('Test Consultant')
        print(f"✅ Added and retrieved {len(retrieved_skills)} skills for Test Consultant")
        
        # Test getting consultants with a skill
        consultants_with_python = repo.get_consultants_with_skill('Python')
        print(f"✅ Found {len(consultants_with_python)} consultants with Python skill")
        
        # Clean up test file
        try:
            test_file = os.path.join(os.path.dirname(__file__), '..', 'test_skills.json')
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Skill Repository: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Enhanced AI Consultant Matcher Test Suite")
    print("=" * 50)
    
    tests = [
        ("Resume Parser", test_resume_parser),
        ("Skill Repository", test_skill_repository),
        ("Enhanced AI Matcher", test_enhanced_ai_matcher),
        ("Skill Extraction", test_skill_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced AI Matcher is ready to use.")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
