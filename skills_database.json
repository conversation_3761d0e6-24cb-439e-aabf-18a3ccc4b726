{"all_skills": ["PivotTables", "Data Gateway", "SSIS", "JSP", "Single Page Application (SPA)", "Device Drivers", "Auto Scaling", "<PERSON><PERSON><PERSON><PERSON>", "Lambda", "GitLab", "Hybrid Solution Architecture", "SAS Visual Investigator", "Domain Driven Development (DDD)", "SQL Server 2014", "C# Programming Certified Professional", "SAP Sales and Distribution (SD)", "High-Performance Architecture Design", "IFRS (International Financial Reporting Standards)", "Customer Accounting", "JAX-WS", "<PERSON><PERSON><PERSON>", "Azure Virtual Network", "Activities", "Business Process Optimization", "SQR 6.0", "YAML Pipelines", "Real-time Cash Visibility System", "Data Cleaning", "Windows Server 2016", "SEO Optimization", "Process Management", "Google Tag Manager", "OpenID Connect", "EN ISO 50308", "Technical Architecture Documentation", "List-View", "ETL", ".NET Core 6.0", "Data Integration", "Microsoft Certified Professional", "NuGet", "Data Processing", "Repository Design Pattern", "Putty", "AutoCAD", "Amazon CloudFormation", "Multi-AZ VPC", "Domain Driven Design (DDD)", "Prince2", "Certified SAP Functional Consultant", "GitHub", "Package locking", "<PERSON><PERSON><PERSON>", "Continuous Integration and Continuous Deployment (CI/CD)", "Senior System Architect", "Predictive Modeling", "Mantine UI", "Amazon Route 53", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Customer Telephony Integration (CTI) testing", "SAS Visual Analytics", "Microsoft Power BI", "CloudFront", "Declarative Rules", "Toad", "AWS Lake Formation", "MS SQL Server 2019", "Regression", "Oracle Autonomous Data Warehouse (ADW)", "Visual Studio 2012", "Data Transforms", "ALV Reports", "Ad-hoc Testing", "IICS", "Azure API Management", "Peoplesoft Financials", "Oracle Cloud Accounts Payable", "Test-Driven Development (TDD)", "Bitbucket", "Android App Development", "Micro-services", ".NET Framework 2.0", "Regression Analysis", "Web Testing", "Azure Active Directory (Azure AD)", "SAP Accounts Receivable (AR)", "RACI Matrix", "Product Life Cycle Management (PLM)", "SmartSheet", "OTBI", "JUnits", "Data Architecture", "AWR", "Data Masking", "Gre", "Test Coverage", "<PERSON><PERSON>", "UDP", "OCI AutoScaling", "Test Planning", "Circuit Breaker", "SQL Server 2017", "SAP MM Functionalities", "WebLogic 11g", "Tortoise SVN", "JWT", "ITIL V3 Foundation", "Amplify", "Saga", "Spring Framework", "Oracle 11g", "SQL Server 2016", "Prototype", "Module Pool Programming", "Defect Tracking", "WinSCP", "SAP Accounts Payable (AP)", "OKTA", "Dependency Inversion Principle", "Artifactory", "Hive", "C# 8.0", "MicroStrategy", "Amazon ELB", "Redux Toolkit", "Business Intelligence", "Oracle Fusion Financials", "ICS", "Client Management", "Pega 8.4", "Database testing", "Statistical Analysis", "Big Data", "User Interface Testing", "Artificial Intelligence", "FDMEE", "Pega Group Benefits Insurance Framework", "Servlets", "Test Strategy Creation", "WCF", "Quality Management (QM)", "Azure Logic Apps", "BAPI", "Oracle Financials Cloud Accounts Payable", "Data Pump", "PCBS", "Index Design", "Materialized Views", "Machinery Directive 2006/42/EC", "Power Query", "Cisco Catalyst 9800 Wireless Controller", "Ubus", "QMI", "Tomcat", "OCI CDN", "Amazon Lambda", "txText Control", "Angular 7", "AG Grid", "Teradata Certified Administrator (V2R5)", "Sitecore", "Waterfall Charts", "Angular Reactive Forms", "Spark", "<PERSON><PERSON><PERSON>", "Struts 2", "<PERSON>", "FI-AP", "Amazon Web Services (AWS)", "OpenTelemetry", "Data Security", "DB2", "Sheet Metal Design", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "Column <PERSON>", "AT&T Echo controller", "Hyperion Financial Management (HFM)", "Oracle Enterprise Manager 12c", "Perfecto", "Service-Oriented Architecture (SOA)", "PII", "Microsoft Excel", "Pig", "IAM", "Configuration Management", "Azure Data Factory", "Visual Studio", "Oracle Cloud General Ledger", "Oracle Cloud", "<PERSON>", "Apache Airflow", "Procurement Processes", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Oracle Database Administrator Certified", "Data Capture (CDC)", "Test Strategy", "High-Throughput System Architecture", "SAP Script", "Direct Connect", "ATC", "Encryption", "NoSQL Databases", "<PERSON><PERSON>", "Informatica 10.2", "ADFS (Active Directory Federation Services)", "Elastic Beanstalk", "<PERSON> Data Modeler", "Spring MVC", "ServiceNow", "SPDD", "FI-GL (FICO)", "Fiori Elements", "EC2", "VS Code", "Cloud Migration", "CI/CD Pipeline", "JCL", "Production Support (L3)", "WebSphere", "Snowpipe", "Code Review", "REST", "Enhancement Points", "OpenWRT", "DBMS", "Dynatrace", "IBM BMP", "Data Marts", "SSAS", "Oracle Data Guard", "ASME Y14.5", "@task", "Azure Pipelines", "Data Intelligence", "Process Flows", "Transportable Tablespaces", "PLA", "MS Excel", "<PERSON><PERSON><PERSON>", "LeanFT", "Hypothesis Testing", "AI-powered Automation", "BTP", "SAS Data Integration Studio", "AWS Glue", "JDK 8", "Windows 2007/2008/2010", "Visual Studio Code", "ReactJS", "Streamset", "TestNG", "AWS API Gateway", "DevOps", "<PERSON><PERSON><PERSON>", "<PERSON>", "Bank Reconciliation", "Terada<PERSON>", "<PERSON><PERSON>xing", "Machined Parts Design", "OCI IAM", "Relational Databases", "Microsoft .NET", "Cognito", "Order-to-Delivery Process Optimization", "Kali Linux", "ETL Development", "Mac filtering", "Adobe Forms", "Ruleset locking", "ElasticSearch", "Hyperion FRS", "Cash Pooling", "Case Management", "Oracle", "Mechanical Product Design", "High Throughput System Architecture Design", "Prototype Model", "<PERSON><PERSON>", "SAP Fiori List Report Application", "Pega Rules Process Engine", "Red Hat Linux Enterprise 8", "RAP", "SQL Server 2005", "Windows Services", "Sanity", "Intelligent Data Management Cloud (IDMC)", "HP Quality Center", "SolidWorks", "IDoc", "Continuous Integration", "Circuit Breaker <PERSON>", "YANG", "SQL", "CosmosDB", "ETL Processes", "ECS", "<PERSON><PERSON>", "IP", "DFA", "JAX-RS", "AireOS controller", "PFTC", "API testing", "RESTful APIs", ".NET Framework 4.7", "Windows", "TypeScript", "JDK 17", "SAP Scripts", "EJB", "Shell Scripting", "VSAM", "CDS Views", "Internet of Things (IoT)", "Azure APIM", "AWS Step Functions", "Razor View Engine", "MuleSoft", "XAML", "UI Testing", "SQL Server 2012", "Star Schema", "Gulp", "Test Execution", "Caliburn.Micro", "<PERSON><PERSON><PERSON>", "CDN", "Reverse Engineering", "Tridion CMS 8.5", "Logic Apps", "Azure Storage", "Team Foundation Server (TFS)", "PI/PO", "Speedometer Charts", "Firebase Cloud Services", "MySQL Aurora", "Salesforce Platform Administrator (Certification - In process)", "IoT", "Azure Service Bus", "SQL*Trace", "Moq", "SAP AP/AR Transactions", "Antifactory", "Silverlight", "OCA - Oracle Database Administrator Certified", "Python Worksheet", "SLA Development", "Microservices testing", "5G", "AWS Aurora Postgres", "Cross-functional Collaboration", "Microsoft Azure", "Purchase Order (PO) Management", "Oracle Cloud I-Receivables", "Visual Studio 2010", "HTML5", "Spring", "CTI Testing", "ADO.NET", "ASP.NET Core 8.0", "WCF RESTful", "POLARIS", "Amazon Redshift", "<PERSON><PERSON> (Project Management Professional)", "PowerShell", "Rally", "DataMart", "Ka<PERSON><PERSON>", "Abstract Factory Design Pattern", "Oracle 19c", "Business 360", "Excel", "Software Architecture", "SeeTest (Experitest)", "<PERSON><PERSON><PERSON>", "Data Structures", "Git", "GL", "Data Management", "SAP Materials Management (MM)", "Fact Tables", "Oracle 8x", "AMDP", "SCM", "CE Marking", "Oracle 19c Database Administrator Training from Koenig Database Administration", "OAuth", "Inventory Planning", "VB.NET", "OUM Methodology", "XML Parser", "Dimensional Modeling", "Design and Analysis of Algorithms", "Service Virtualization", "SeeTest/Experitest", "Performance Testing", "Unix Shell Scripting", "GlassFish", "API Gateway", "E-commerce Architecture", "Visual Studio 2019", "Snowsight", "ISO 27000", "Dispute and Deduction Management", "Amazon RDS", "Parameters", "SAP Fiori Launchpad", "IBM DB2", "Data Flow Architectures", "Kendo UI", "Microsoft Excel Spreadsheets", "Custom Visuals (Power BI)", "Wix", "Drill Down", "Automation Testing", "Infrastructure as Code (IaC)", "Cash Management", "<PERSON><PERSON>", "Data Flows", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Amazon Elastic Container Registry (ECR)", "Apache Tomcat", "Account Payables", "MVC Design Pattern", "Exadata X9M-2", "AP/AR Transactions", "Financial Data Management (FDMEE)", "Onshore Rigging Calculations", "Apache", "Mainframes testing", "Oracle Cloud I-Expenses", "Salesforce Platform Administrator", "SAP ECC", "<PERSON>adata", "Data Decryption", "Ad hoc Testing", "Data Governance", "Triggers", "Order to Cash Management (OTC)", "Object-Oriented Programming", "Amazon Elastic Kubernetes Service (EKS)", "AML Compliance", "AWS Elastic Kubernetes Service (EKS)", "Cascading Filters", "Automated Bank Reconciliation", "OAuth 2.0", "MathCAD", "C", "CSV", "Oracle PL/SQL", "MCA", "Record to Report (R2R)", "DM", "Amazon Athena", "HP", "Regression Testing", "Pub/Sub", "Oracle TDE", "The Complete Python Developer", "VPC Design", "GAAP (Generally Accepted Accounting Principles)", "C# 10.0", "Integration Testing", "<PERSON><PERSON><PERSON>", "Component-Based Architecture", "JSF", "AT interfaces", "Tridion CMS 2011", "Data Migration", "Qualcomm SDX hardware", "Red Hat Linux", "Federated Database Design", "FI-AR", "Resin", "Mesh Networking", "EN ISO 14122", "Talwar controller", "DAX", "SSO", "Dojo", ".NET MAUI", "EZTrieve", "Application Design", "Oracle E-Business Suite R12", "Terraform", "Mainframe Testing", "NETCONF", "OCI Object Storage", "Data Compliance", "Windows Server", "Visual Studio 2013", "Enterprise Reporting", "NodeJS", "Automatic Payment Program (F110)", "Azure Cloud", "Business Workflow", "Oracle GoldenGate (implied)", "<PERSON><PERSON>", "Database Performance Tuning", "SAP Treasury Modules", ".NET Framework", "Smoke Testing", "MySQL", "ASP.NET Core", "Risk Management", "Product Assortment Management", "Accounts Payable", "Oracle OCI", "Functions", "VPN", "Ping Identity", "SSRS", "CVS", "AZ900: Microsoft Azure Fundamentals", "Amazon SQS", "React.js", "Microsoft Azure Certified Professional", "FI-GL Transactions", "Defect Management", "Django", "JR<PERSON>el", "AWS CloudFormation", "General <PERSON><PERSON>", "ASP.NET MVC", "Azure Functions", "E-Commerce", "C#", "SSO Testing", "Ethernet", "ITIL Foundation", "NoSQL", "IDMC", "EKS", "gRPC", "SEO", "SAP FI-GL Transactions", "GD&T", "SoapUI", "Manual Testing", "Apache ECharts", "Factory Design Pattern", "Scenario Assessment", "SQL Server 2008", "AIF", "Informatica Cloud Services (IICS)", "JavaScript", "WAF", "Predictive Analysis", "FullStory", "Test Automation", "CAPWAP", "RFCs", "IntelliJ IDEA", "Business 360 Console", "Hangfire", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "Oracle 19c RAC", "Smart View", "<PERSON> (GL)", "Oracle DBA", "STATSPACK", "Scrum Master", "CQRS", "Key Performance Indicators (KPIs)", "Security Groups", "Object Storage", "Vite", "MudBlazor", "Predictive Forecasting", "Oracle Grid Control 11g", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "Technical Design", "Entity Framework 7.0", "<PERSON><PERSON><PERSON>", "Visual Studio 2005", "LTE", "Apache POI", "MS SQL Server", "SAP Security", "HTTP (TLS)", "SAP NetWeaver Gateway", "Generally Accepted Accounting Principles (GAAP)", "Queue Processors", "Datadog", "Data Mart", "Mobile Application Automation", "Vendor/Customer Open Item Management", "PSM", "BDC", "SQS", "Blazor", "LDAP", "CentOS", "JBehave", "Time Series Forecasting", "Azure Developer", "Fixed Assets", "Elastic Load Balancers", "SVN", "Oracle Database (11g, 10g, 9i, 8x)", "Saga Pattern", "Virtualization", "DBT", "Sub Ledger Accounting (SLA)", "N-tier applications", "GraphQL", "S3 (Amazon S3)", "Azure DevOps", "HP/IBM Power E850", "Real-time Data Analytics Solution Architecture", "UG-NX", "Object-Oriented Programming (OOP)", "Google Data Analytics Professional Certificate", "Agile Project Management", "Exploratory testing", ".NET 6", "TDD", "Cloud Data Catalog", "JRockit Mission Control", "SAP Solution Design", "WRICEF Documentation", "Web Jobs", "Virtual Network", "Exadata CS DBX7", "Production Support", "Key Management", "Oracle 8i", "Agile", "PCI", "Log4j", "HP/IBM Power E980", "Oracle Financial Accounting Hub", "CDL", "Test Scripting", "DNVGL", "Customer Exits", "SAP S/4HANA", "Amazon Glue", "Power BI", "Screen Flows", "J2EE", "NUnit", "Blue Yonder", "I-Receivables", "OIDC", "Service Extension", "Query Performance Optimization", "LINQ to SQL", "SAP UI5", "WLAN", "Erro<PERSON>", "Active Directory", "Microsoft BI Stack", "Automated Data Entry", "Desktop Application Testing", "CMMI Level 5", "OOAD", "Groovy", "Microsoft Project", "ETL Design", "MS Access", "AWS Auto Scaling", "Service Level Agreement (SLAs)", "SQL*Plus", "AWS Certified Solutions Architect - Associate", "Oracle (PL/SQL)", "<PERSON><PERSON>", "BP Integrations", "Pega Product Builder", "MIS Management", "Cisco Access Points", "AWS Storage Services", "T-SQL", "Data Analytics", "IOS Testing", "SAP Controlling (CO)", "File-Aid", "ASP", "Webpack", "Azure Log Analytics", "Docker Registries", "Dimensions", "Oracle Cloud Cash Management", "Functional Testing", "DDD", "OBIEE", "AGGrid", "Order Management", "Gateway Aggregation", "Advanced Java Development", "SAP Profit Center Accounting (PCBS)", "Requirements Prioritization", "SQL Server Reporting Services (SSRS)", "Summary-View Reports", "IIS", "Compatibility Testing", "SQLite", "Styled Components", "Telerik", "AKS", "PHP", "SOA", "Visual Studio 2017", "Subversion", "IVR Testing", "XML", "CDH", "Requirement Gathering", "Microsoft SQL Server", "ActiveMQ", "Blob Storage", "IBM MQ", "Product Validation", "OLTP", "Inhouse Cash Management", "Amazon EBS", "Logility", "Product locking", "SAML", "Pega Marketing Consultant", "<PERSON><PERSON>", "Azure", "Amazon ECS", "Blueprints", "Oracle 12c", "AIM Methodology", "Slowly Changing Dimensions (SCD)", "WebLogic", "Athena", "Resource Management", "CAPM", "SAP Warehouse Management", "Python Scripting", "Rule Resolution", "Network Security", "Event Hub", "ALE IDOCs", "Account Receivables", "Sufi", "Data Wrangling", "Tibco", "Informatica Power Center", "GitLab Pipelines", "SQL Server 2000", "PySpark", "HttpClient", "Application Insights", "Flask", "GraphQL APIs", "Quartz", "Java JDK", "Data Enrichment", "Postman", "Regulatory Reporting", "BSP", "InstallShield", "AWS CDK", "ELT", "Java Mail", "SOAP", "ICRT", "Firehose", "AWS", "Multi-AZ", "YAML Pipeline", "JPA", "Database Partitioning", "ATOM (Mainframes/AS400 automation tool)", "Containerization", "Data Dictionary (DDIC)", "ios-xe asr 1K router", "Web API", "Certified Professional Data Engineer", "Spring Boot", "SAP", "Unit testing", "Flow Actions", "Databricks", "Oracle Enterprise Manager Grid Control 11g", "Enterprise Class Structure", "SAP UI5 Framework", "Data Transformation", "SeeTest", "Autonomous Data Warehouse (ADW)", "Active Reports", "REST Assured", "Azure Service Fabric", "MS SharePoint Server", "NestJS", "Selenium WebDriver", "Peoplesoft Supply Chain Management", "CRM", "Google Analytics", "OLAP", "Bluetooth", "Pega 7.2.2", "System Architect", "Functions (Database)", "Amazon Firehose", "Inbound/Outbound Proxy", "Azure App Services", "SAP MM", "App Studio", "Advanced Analytics", "JUnit", "Domain Driven Design", "JDeveloper", "Flat Files", "RFID", "AR Processing & Reporting", "Cisco Aironet outdoor mesh access points", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "NumPy", "Cobol", "Factory Pattern", "Snow Pipe", "SPAU", "OData", "Database Migration Service", "LINQ to Objects", "Salesforce", "Oracle Cloud Infrastructure (OCI)", "Sparx Enterprise Architect", "Java Development (Certification)", "Data Modeling", "Dimension Modeling", "V<PERSON>am Backup and Recovery", "Azure AD", "Business Process Mapping", "Data Validation", "WiFi", "Tridion CMS 2009", "Scalability", "EN-13155", "Amazon IAM", ".NET Framework 4.5", "High Availability", "Oracle 10g", "CSS3", "Web Application Testing", "Oracle 8i/11i", "Azure Entra ID", "Alert Management", "<PERSON><PERSON>", "SAP FI", "BRF+", "Peoplesoft FSCM", "Material Design", "AZ-104", "Test Case Execution", "Oracle RAC", "JDBC", "Billing", "Pega 7.4", "Amazon Auto Scaling", "Redux", "Pricing Strategies", "Snowflake", "SQL Server Integration Services (SSIS)", "Azure Container Registry", "Oracle Cloud Accounts Receivable", "Test Plan Development", "DFM", "HP Service Manager (HPSM)", "AWS Certified Developer Associate", "Azure Storage Services", "App Services", "Azure Blob Storage", "IoT Systems", "Test Driven Development", "Peoplesoft HCM", "El<PERSON>", "ITIL Foundation 2011", "Container-based Architecture", "Data Automation", "ISO27001 Compliance", "Autonomous Transaction Processing (ATP)", ".NET Core", "PFTC Roles", "Amazon ECR (Elastic Container Registry)", "Object Oriented Programming", "Mobile Testing", "<PERSON><PERSON><PERSON>", "Row-Level Security (RLS)", "F110 Automatic Payment Program", "BottleRocket", "Angular 12", "Waterfall Methodologies", "OCA-Oracle Database Administrator Certified", "Dimension Tables", "Ezetrieves", "Clipboard", "ISO 27001", "Test Case Development", "API Development (ICS, ICRT)", "Production Planning (PP)", "M Language", "Strapi CMS", "Cypress", "Linux", "FIT-GAP Analysis", "<PERSON><PERSON>", "Admin Studio", "Oracle 11g/12c", "OOPS", "Python", "Entity Framework", "Test Case Design", "A<PERSON>os", "Disaster Recovery", "Serverless Architecture", "Open API", "Orchestration", "Web Application Automation", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "Service Lifting Tool Design", "<PERSON><PERSON>", "Microsoft technologies", "Neb<PERSON>", "Classification", "Pega 7.3", "JSON", "AngularJS", "Multi-tier Distributed Applications", "Cloud Application Integration", "Build/Release Management", "Azure Container Registry (ACR)", "Pega Marketing Consultant (Certification)", "UFT", "Query Plan Optimization", "Appium", "TCP", "Data Pipelining", "Java Development", "Pivotal Cloud Foundry", "SCRUM", "Google Cloud Platform (GCP)", "Test effort estimation", "Spring JPA", "Selenium Grid", "Snowf<PERSON>a", "j<PERSON><PERSON><PERSON>", "HTTP", "MongoDB", "Stakeholder Management", "Intercompany", "AWS Certified Solutions Architect Associate", "SAP Material Master Data Management", "Talend", "SWR", "Component Localization", "Extended Warehouse Management (EWM)", "Technical Architecture", "Fault Tolerance", ".NET Core 8.0", "ISO 20000", "RDS", "Fivetran", "Azure Cloud Architectures", "MVVM", "Real-time Data Analytics", "Oracle 9i", "Subversion (SVN)", "Continuous Integration/Continuous Deployment (CI/CD)", "Oracle 21c", "Coded UI", "AKS (Azure Kubernetes Service)", "Server-Side Encryption", "Dashboards", "Normalization", "Trifacta", "Step Functions", "Spark SQL", "QTP", "SonarQube", "Backend for Frontend (BFF)", "CATIA", "Azure App Service", "CICS", "MVC", "Oracle Allocations", "AWS CLI", "Oracle 11i", "Source Code Management (SCM)", "Amazon CloudFront", "AZ-304", "Oracle Fusion Applications", "Data Security and Compliance", "Kubernetes", "Material Master Data Management", "Senior System Architect (Certification)", "HFM", "Tracer", "Power Pivot", "SAP Financial Accounting (FI)", "Eclipse", "Pivotal Cloud Foundry (PCF)", "<PERSON><PERSON>", "Stored Procedures", "I-Expenses", "End-to-End testing", "Confluent <PERSON><PERSON><PERSON>", "Delivery Management", "Red Hat Linux 8", "CSS", "OAuth Testing", "Business Process Management (BPM)", "Test Estimation", "Dependency Injection", "Nx Monorepo", "Test Management", "Solution Architecture", "Software Development Life Cycle (SDLC)", "ASP.NET Core 6.0", "Program Management", "Cisco Aironet", "Swift", "AJAX", "Bootstrap", "R", "Power Automate", "SAP Procurement Processes", "Snow SQL", "Amazon S3", "Elastic APM", "Mechanical Component Design", "Oracle 19c Data Guard", "Agile Methodology", "CloudWatch", "Data Loader", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Abstract Factory Pattern", "SAP Certified Development Associate - SAP Fiori Application Developer", "Azure Application Insights", "Interactive Dashboards", "Selenium", "Agile Methodologies", "Advanced Planner Optimizer (APO)", "Microsoft Office", "EF Core", "Swagger", "Document review", "SDLC", "Oracle Financials Cloud Receivables", "NetBeans", "SAP Best Practices", "Salesforce APIs", "OCI Load Balancing", "RMAN", "Waterfall", "Unix", "Build.xml", "Visual Studio 2008", "Next.js", "SOLID principles", "Cash Discount Management", "Test Schedule Creation", "Order-to-Delivery Process", "EXPLAIN PLAN", "AI", "Team Foundation Server", "Web services testing", "Drill Through", "Application Load Balancer", "Azure SQL", "AWS Lambda", "ANT", "Getting Started with Power BI", "Service Lifting Tools Design", "AeroScout tags", "Tailwind CSS", "JMS Hermes", "Pareto Charts", "Real-Time Data Analytics Solution Architecture Design", "Data Collection", "Object-Oriented ABAP (OOABAP)", "Microsoft Project (MPP)", "IntelliJ", "Material UI", "SSIS (SQL Server Integration Services)", "Amazon Lake Formation", "S3", "Data Warehousing", "Coded UI Testing", "Repository Pattern", "OCI-Oracle Cloud Infrastructure Foundations Associate", "API Management", "GCP", "OTT", "Operating Systems", "WPF", "Quality Center", "IBM Power E980", "OAuth2", "System Testing", "2D Drawings Review", "Visual Studio 2003", "Stack Up Analysis", "Visio", "React Router", "SQL Server Data Tools", "Bug Reporting", "System Integration", "Azure Key Vault", "Lambda Expressions", "Oracle Database Administration", "iText", "Query Optimization", ".NET", "React", "SAP AR Processing & Reporting", ".NET Core Apps", "BPP Documentation", "Test Driven Development (TDD)", "Crystal Reports", "AWS SAM", "Java", "ADFS", "Data Profiling", "JMeter", "Jersey REST", "Data Storage Strategies", "Warehouse Management", "Log Analytics", "CI/CD", "C++", "SAP Integration", "Azure SQL Database", "Prism", "VMware", "ASAP Methodology", "C# 9.0", "Vendor/Customer Open Items", "Azure Active Directory", "Strategic Planning", "Spring REST", "GitHub Copilot", "User Acceptance testing", "Cloud Data Governance", "Stimulsoft", "RTR", "Oracle Cloud Financials", "High Throughput System Architecture", "Cost Optimization", "RICEF", "Azure Data Lake", "Mobile Web Testing", "Talwar Simulator", "API", "Pega 8", "Order to Cash (O2C)", "Amazon EKS", "ShadCN UI", "Sections", "IBM Infosphere DataStage", "BrowserStack", "Test Plan Creation", "SSRS (SQL Server Reporting Services)", "Snowpark", "Hibernate", "ES6", "LED Manager", "ASP.NET", "ASPX", "ETL testing", "HPSM", "Informatica Data Management Center (IDMC)", "Linux Shell Scripting", "RabbitMQ", "SQLTrace", "Report Definitions", "Data Analysis", "Snowpark API", "Oracle Cloud Infrastructure", "Android Testing", "International Financial Reporting Standards (IFRS)", "Netezza", "Cisco Prime Infrastructure", "Crontab", "PMP", "Data Warehouse testing", "Dashboarding", "Domain-Driven Design (DDD)", "SmartView", "Azure SQL Server", "Reports", "System Architect (Certification)", "SAP Cash Management (CM)", "Performance Tuning", "Node.js", "Omniture", "Oracle Financials Cloud General Ledger", "Microservices", "Financial Reporting Studio (FRS)", "Android Studio", "<PERSON><PERSON><PERSON>ber", "HP ALM", "IBM LTO 8", "Project Management", "Data Modernization", "Technical Design Documentation", "<PERSON><PERSON>", "Maven <PERSON>", "Amazon SNS", "App Insights", "Bamboo", "Financial Reporting", "<PERSON><PERSON><PERSON><PERSON>", "Web Services", "Karate Framework", "Amazon Web Services", "XML Web Services", "Informatica Intelligent Cloud Services (IICS)", "JSTL", "Power BI Desktop", "GDB", "Design Standardization", "PCBS (Profit Center Budgeting System)", "Test Script Development", "AWS Certified Developer - Associate", "Tridion CMS 2013", "AWS-Kops (EKS)", "YAML", "DynamoDB", "Oracle Cloud Budgetary Control", "AWS Aurora", "Apache Kafka", "Service Bus", "Message Queue", "Data Mining", "Angular", "Data Pipelines", "Database Tuning", "PL/SQL", "HTML", "MyEclipse", "Table Storage", "Decision Rules", "WebJobs", "Service Registration", "Windows Application Migration", "Harvest", "Auto-scaling", "<PERSON>er", "Oracle Cloud Fixed Assets", "PostgreSQL", "DWH", "Oracle Real Application Cluster", "<PERSON>", "FRS", "E-commerce Architecture Design", "Spring IOC", "Oracle Enterprise Manager", "Spring Data", "SiteMinder", "RDBMS", "Interactive Voice Response (IVR) testing", "Sanity Testing", "Data Encryption", "Dev Studio", "Design Patterns", "Oracle 19c Database Administrator", "Selenium RC", "Azure Data Factory (ADF)", "Test Design", "Angular 10", "OpenSearch", "User Acceptance Testing (UAT)", "ISTQB Certified Tester Foundation Level", "DB2 8.1", "DevExpress", "KPI Development", "SAP SD", "Agile Transformation", "Redis", "Business Intelligence Publisher (BIP)", "Physical Database Design", "Procure to Pay (P2P)", "Demand Forecasting", "Cosmos DB", "Insurance", "2D Drawing Review", "Red Hat Linux Enterprise 7", "Agents", "Real-time Data Integration", "Visual Studio 2022", "CloudFormation", "Event-Driven Architecture", "Browser compatibility testing", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "Project Planning", "Confluence", "<PERSON><PERSON>", "LINQ", "ClearCase", "JMS", "<PERSON><PERSON>", "Choreography Pattern", ".NET Framework 3.5", "Oracle Export/Import", "TFS", "Spiral Model", "Risk-based testing", "Oracle Transactional Business Intelligence (OTBI)", "OCI VCN", "SAP Profit Center Accounting (PCA)", "Tridion CMS", "Power BI Service", "Sigma", "Data Visualization", "Amazon VPC", "Ansible", "Amazon EC2", "OCI WAF", "VCN", "Angular 8", "Design FMEA", "Real-time Data Ingestion", "Smart Forms", "Deep Learning", "SQL Server", "OCI Auto Scaling", "802.11", "Web IDE", "Web Dynpro ABAP", "Pa<PERSON><PERSON>", "Sass", "Amazon Web Services (S3, EC2, Lambda)", "Profit Center Accounting (PCA)", "Container-based Architecture Design", "Indexing", "VPC", "SAP CO", "Salesforce Platform Developer", "AWS Certified SysOps Administrator Associate", "Procure to Pay (PTP)", "Machine Learning", "Salesforce Platform Developer (Certification - In process)", "Cloud Data Integration", "Kafka", "ABAP", "Choreography", "Event Grid", "Azure Monitor", "SharePoint", "Business Objects (BO)", "Model-View-Controller (MVC)", "AWS S3", "Amazon CloudWatch", "Cost Analysis", "Visual Studio 2015", "TKPROF", "Oracle Autonomous Transaction Processing (ATP)", "WebLogic 12c", "JDK", "Financial Analysis", "IBM LTO 9", "BPPs (Business Process Procedures) Documentation", "ISO/IEC 27001", "BIP", "AWS Certified SysOps Administrator - Associate", "UML", "SSMS", "IBM Power E850", "CA DevTest", "Selenium IDE", "Oracle 19c Database Administrator Training", "ADDM", "Red Hat Linux 7", "Informatica Data Management Cloud (IDMC)", "Advanced Java Development (Certification)", "Mac filter configuration", "Design Calculations", "Release Management", "Client-server applications", "ATOM", "SAFe", "AWS Athena", "REST APIs", "MS Azure", "Amazon DynamoDB", "Accounts Receivable", "Requirements mapping", "Pega 8.4.1", "ACCELQ", "Agile (SCRUM)", "Sankey Diagrams", "Data Quality", "IAM Role Management", "macOS", "Mechanical Design", "SNS", "Brainbench C# 5.0", "BADIs", "A<PERSON> (EKS)", "User Exits", "Informatica PowerCenter", "Microservices Architecture", "AZ-204", "TortoiseSVN", "ANSYS", "RBAC", "Azure Kubernetes Service (AKS)", "LambdaTest", ".NET Framework 4.0", "Avro", "Continuous Deployment", "TLS", "Cisco catalyst 3750 Switch", "WebLogic 14c", "Angular 9"], "skill_frequency": {"Python": 83, "JavaScript": 60, "Java": 67, "C#": 38, "SQL": 80, ".NET 6": 7, "ASP.NET Core": 22, "ASP.NET MVC": 17, "Angular": 58, "Web API": 29, "Azure": 40, "Azure Functions": 29, "Azure Developer": 7, "Azure Logic Apps": 6, "Azure Service Bus": 13, "Azure API Management": 1, "Azure Storage": 13, "Cosmos DB": 12, "Redis Cache": 7, "Azure Active Directory (Azure AD)": 2, "Azure Virtual Network": 1, "Azure Application Insights": 1, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 13, "Azure Container Registry": 7, "Azure Service Fabric": 7, "Azure Data Lake": 11, "YAML Pipelines": 5, "Docker": 44, "Kubernetes": 38, "CI/CD": 49, "Microservices": 27, "Serverless Architecture": 12, "HTML": 50, "CSS": 33, "jQuery": 41, "Event Grid": 7, "Event Hub": 7, "SQL Server": 47, "MySQL": 53, "Snowflake": 23, "T-SQL": 21, "PL/SQL": 24, "Stored Procedures": 25, "Triggers": 10, "Functions (Database)": 1, "Amazon Web Services (AWS)": 45, "Microsoft Azure": 20, "Agile Methodology": 13, "Design Patterns": 17, "Microservices Architecture": 8, "Federated Database Design": 5, "Container-based Architecture": 4, "High-Throughput System Architecture": 2, "Real-time Data Analytics Solution Architecture": 4, "E-commerce Architecture": 5, "Hybrid Solution Architecture": 5, "VPC Design": 3, "Direct Connect": 5, "VPN": 5, "Query Performance Optimization": 6, "Data Modeling": 41, "Microsoft Certified Professional": 2, "WPF": 6, "MVC": 7, "MS Azure": 5, "WCF": 22, "Blob Storage": 6, "Table Storage": 6, "App Services": 6, "Redis": 12, "App Insights": 6, "Azure APIM": 6, "Logic Apps": 12, "AZ900: Microsoft Azure Fundamentals": 6, "Caliburn.Micro": 6, "Prism": 6, "Entity Framework 7.0": 6, "XML Parser": 6, "LINQ": 15, "Stimulsoft": 6, "Angular Reactive Forms": 6, "HttpClient": 6, "NUnit": 12, "Coded UI Testing": 5, "ADO.NET": 23, "SQL Server Reporting Services (SSRS)": 8, "Strapi CMS": 5, "Windows Services": 11, "WCF RESTful": 6, "MS SQL Server 2019": 4, "PostgreSQL": 44, "SQLite": 6, "Oracle (PL/SQL)": 3, "MS Access": 12, "InstallShield": 6, "GitHub": 40, "TFS": 17, "SVN": 33, "IIS": 6, "Apache Tomcat": 6, "DevExpress": 6, "Brainbench C# 5.0": 5, "MVVM": 2, "ASP.NET": 17, ".NET Framework": 12, "Entity Framework": 16, "EF Core": 6, "Razor View Engine": 6, "Bootstrap": 33, "CosmosDB": 6, "ElasticSearch": 16, "Apache Kafka": 23, "ActiveMQ": 6, "Pivotal Cloud Foundry": 5, "Azure App Service": 10, "Node.js": 38, "React": 25, "OAuth2": 5, "Swagger": 12, "OOPS": 6, "SOLID principles": 11, "Team Foundation Server (TFS)": 6, "Git": 52, "Jira": 68, "Azure DevOps": 40, "Moq": 6, "Agile Methodologies": 8, "SCRUM": 23, "Waterfall Methodologies": 6, "Test-Driven Development (TDD)": 10, "C++": 17, "Service Bus": 6, "API Management": 6, "YAML Pipeline": 2, "Azure AD": 5, "Virtual Network": 6, "Application Insights": 6, "Log Analytics": 6, "Key Vault": 6, "Functions": 8, "Software Architecture": 3, "Micro-services": 2, "High Throughput System Architecture": 2, "Microsoft Azure Certified Professional": 2, "MCA": 2, "Open API": 5, "C": 17, ".NET Core": 14, "AJAX": 17, "AngularJS": 5, "ReactJS": 15, "XML": 28, "HTML5": 21, "Sass": 5, "TypeScript": 37, "JSON": 42, "DynamoDB": 17, "OpenSearch": 5, "EC2": 19, "CloudFront": 5, "IAM": 19, "ECS": 5, "SQS": 16, "SNS": 16, "Lambda": 9, "API Gateway": 18, "RDS": 5, "CloudWatch": 20, "Step Functions": 8, "Elastic Cache": 5, "NodeJS": 5, "AGGrid": 4, "txText Control": 5, "ASPX": 5, "SOAP": 38, "RESTful APIs": 5, "Crystal Reports": 11, "Active Reports": 5, "SSRS": 17, "SSIS": 16, "YAML": 5, "Terraform": 16, "DDD": 1, "TDD": 1, "Agile": 59, "NuGet": 4, "Object-Oriented Programming (OOP)": 8, "VB.NET": 5, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 5, "OpenTelemetry": 5, "FullStory": 5, "Google Analytics": 10, "ASP.NET Core 6.0": 2, "ASP.NET Core 8.0": 2, ".NET MAUI": 5, "XAML": 5, "C# 8.0": 2, "C# 9.0": 2, "C# 10.0": 2, "Web Services": 5, "REST": 21, "Angular 7": 2, "Angular 8": 2, "Angular 9": 2, "Angular 10": 2, "Angular 12": 2, "Material Design": 5, ".NET Framework 2.0": 2, ".NET Framework 3.5": 2, ".NET Framework 4.0": 2, ".NET Framework 4.5": 2, ".NET Framework 4.7": 4, "CI/CD Pipeline": 5, "Splunk": 15, "RabbitMQ": 5, "Amazon DynamoDB": 10, "Kendo UI": 10, "Amazon EC2": 24, "AWS Lambda": 17, "Azure App Services": 6, "WebJobs": 1, "Azure Active Directory": 5, "ServiceNow": 14, "HP Service Manager (HPSM)": 3, "Service-Oriented Architecture (SOA)": 3, "OAuth 2.0": 6, "OKTA": 10, "Azure Entra ID": 5, "Bitbucket": 22, "Team Foundation Server": 5, "Subversion (SVN)": 8, "TortoiseSVN": 7, "Visual Studio 2003": 2, "Visual Studio 2005": 2, "Visual Studio 2008": 2, "Visual Studio 2010": 2, "Visual Studio 2012": 2, "Visual Studio 2013": 2, "Visual Studio 2015": 2, "Visual Studio 2017": 2, "Visual Studio 2019": 2, "Visual Studio 2022": 2, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 5, "OpenID Connect": 1, "Ping Identity": 1, "Salesforce APIs": 1, "CQRS": 6, "Saga Pattern": 4, "Choreography Pattern": 3, "Gateway Aggregation": 4, "Circuit Breaker Pattern": 4, "Message Queue": 1, "MuleSoft": 5, "Kafka": 16, "Tibco": 5, "AKS (Azure Kubernetes Service)": 2, "MVC Design Pattern": 4, "Repository Pattern": 4, "Dependency Inversion Principle": 5, "Dependency Injection": 5, "Factory Pattern": 4, "Abstract Factory Pattern": 4, "Tridion CMS 2009": 2, "Tridion CMS 2011": 2, "Tridion CMS 2013": 2, "Tridion CMS 8.5": 2, "Sitecore": 5, "SEO Optimization": 4, "Omniture": 5, "Google Tag Manager": 5, "SQL Server 2000": 2, "SQL Server 2005": 2, "SQL Server 2008": 2, "SQL Server 2012": 2, "SQL Server 2014": 2, "SQL Server 2017": 2, "Azure SQL Server": 2, "Oracle PL/SQL": 12, "Selenium": 5, "Azure Data Factory": 5, "PMP (Project Management Professional)": 2, "Agile (SCRUM)": 3, "Kanban": 9, "AZ-104": 4, "AZ-204": 4, "AZ-304": 4, "Machine Learning": 11, "Deep Learning": 4, "Predictive Analysis": 4, "Artificial Intelligence": 4, "IoT Systems": 1, ".NET": 11, "gRPC": 5, "SSIS (SQL Server Integration Services)": 5, "SSRS (SQL Server Reporting Services)": 2, "LINQ to SQL": 5, "LINQ to Objects": 5, "Lambda Expressions": 5, "S3 (Amazon S3)": 2, "Amazon Elastic Kubernetes Service (EKS)": 5, "Amazon ECR (Elastic Container Registry)": 2, "Elastic Beanstalk": 10, "Application Load Balancer": 4, "NoSQL": 5, "Datadog": 5, "Azure Container Registry (ACR)": 5, "Azure Kubernetes Service (AKS)": 6, "Azure Blob Storage": 11, "Blazor": 5, "MudBlazor": 5, "Telerik": 5, "Redux": 5, "Hangfire": 5, "ADFS (Active Directory Federation Services)": 2, "Tableau": 38, "DB2": 21, "SAP": 10, "IDoc": 5, "Logility": 3, "Blue Yonder": 3, "CloudFormation": 5, "VPC": 5, "Jenkins": 49, "SonarQube": 10, "Antifactory": 4, "AWS Elastic Kubernetes Service (EKS)": 5, "ANT": 21, "Maven": 45, "Shell Scripting": 13, "Ansible": 10, "PowerShell": 11, "Tomcat": 26, "JBoss": 16, "WebLogic": 16, "WebSphere": 15, "Windows Server": 5, "Red Hat Linux": 4, "Unix": 16, "CentOS": 4, "VMware": 5, "Elastic Load Balancers": 4, "Waterfall": 29, "Batch Scripting": 4, "Amazon ECS": 5, "Amazon S3": 23, "Amazon EBS": 5, "Amazon VPC": 5, "Amazon ELB": 5, "Amazon SNS": 6, "Amazon RDS": 5, "Amazon IAM": 5, "Amazon Route 53": 5, "AWS CloudFormation": 8, "AWS Auto Scaling": 3, "Amazon CloudFront": 5, "Amazon CloudWatch": 6, "AWS CLI": 4, "Vault": 5, "Docker Hub": 5, "Docker Registries": 4, "AWS Kops (EKS)": 3, "Groovy": 5, "GitLab": 17, "Apache": 2, "Grafana": 5, "Pivotal Cloud Foundry (PCF)": 6, "Infrastructure as Code (IaC)": 5, "Configuration Management": 10, "Containerization": 5, "Orchestration": 5, "Build/Release Management": 5, "Source Code Management (SCM)": 4, "HTTP (TLS)": 1, "Key Management": 1, "Encryption": 1, "J2EE": 12, "SAFe": 5, "Confluence": 15, "Microsoft Project": 4, "SmartSheet": 5, "DevOps": 5, "Warehouse Management": 5, "CMMI Level 5": 10, "PMP": 7, "PSM": 4, "Agile Project Management": 5, "Scrum Master": 5, "Program Management": 10, "Project Management": 9, "Project Planning": 10, "Risk Management": 10, "Cost Analysis": 10, "Resource Management": 5, "Stakeholder Management": 5, "Delivery Management": 5, "Client Management": 5, "Release Management": 5, "Microsoft Excel": 7, "Azure Cloud": 12, "Cobol": 14, "Ezetrieves": 4, "IBM BMP": 5, "ISO 27001": 5, "DBT": 6, "AWS": 18, "Azure Data Factory (ADF)": 6, "Databricks": 12, "Database Migration Service": 6, "AWS Glue": 15, "Fivetran": 6, "Snow SQL": 6, "Streamset": 6, "Snowpark": 6, "Column Masking": 5, "Data Encryption": 5, "Data Decryption": 5, "Data Masking": 5, "Data Governance": 7, "Hive": 12, "Pig": 6, "Sqoop": 6, "PySpark": 8, "Sigma": 6, "Apache Airflow": 6, "Informatica Power Center": 1, "Talend": 12, "Peoplesoft FSCM": 5, "Peoplesoft HCM": 6, "Oracle": 25, "MS SQL Server": 12, "OLTP": 11, "OLAP": 11, "Data Warehousing": 32, "Data Architecture": 4, "Data Integration": 15, "ELT": 5, "ETL": 26, "Data Quality": 17, "Real-time Data Ingestion": 5, "Snow Pipe": 4, "Confluent Kafka": 5, "Snowsight": 6, "SQR 6.0": 3, "Avro": 5, "Parquet": 5, "CSV": 8, "Index Design": 6, "Query Plan Optimization": 5, "Data Analysis": 9, "Business Intelligence": 13, "Data Management": 4, "ETL Processes": 5, "Excel": 7, "Power BI": 22, "DAX": 11, "Statistical Analysis": 4, "Regression": 4, "Hypothesis Testing": 5, "Predictive Modeling": 5, "Time Series Forecasting": 5, "Classification": 5, "Data Cleaning": 5, "Data Transformation": 12, "Data Automation": 5, "PivotTables": 5, "Power Query": 11, "Pandas": 5, "NumPy": 5, "SQL Server Integration Services (SSIS)": 5, "R": 5, "Google Data Analytics Professional Certificate": 5, "Getting Started with Power BI": 5, "The Complete Python Developer": 5, "ISTQB Certified Tester Foundation Level": 5, "Informatica PowerCenter": 16, "IICS": 1, "IDMC": 1, "IBM Infosphere DataStage": 5, "SAS Data Integration Studio": 5, "Oracle 11g": 9, "Oracle 10g": 9, "Oracle 9i": 4, "Oracle 8x": 4, "Microsoft SQL Server": 5, "Amazon Redshift": 5, "Data Migration": 9, "Data Modernization": 5, "Data Enrichment": 5, "Data Validation": 9, "Data Processing": 4, "Data Pipelining": 5, "Data Visualization": 9, "Enterprise Reporting": 4, "Dashboarding": 2, "AWS Athena": 5, "AWS Lake Formation": 3, "Microsoft Power BI": 5, "OBIEE": 5, "SAS Visual Investigator": 5, "SAS Visual Analytics": 5, "Erwin Data Modeler": 11, "Sparx Enterprise Architect": 5, "RDBMS": 10, "Star Schema": 17, "Snowflake Schema": 17, "Slowly Changing Dimensions (SCD)": 5, "Normalization": 5, "Flat Files": 3, "Predictive Forecasting": 3, "Alert Management": 3, "Regulatory Reporting": 3, "AML Compliance": 1, "Data Intelligence": 3, "Scenario Assessment": 1, "MIS Management": 3, "MS Excel": 3, "Data Security": 4, "Data Wrangling": 5, "Visual Studio": 8, "Mechanical Product Design": 5, "Mechanical Component Design": 2, "System Integration": 5, "Sheet Metal Design": 5, "Machined Parts Design": 5, "Design Standardization": 5, "Component Localization": 5, "Cost Optimization": 5, "Design Calculations": 5, "Cross-functional Collaboration": 4, "Onshore Rigging Calculations": 5, "Service Lifting Tool Design": 4, "Process Management": 5, "UG-NX": 5, "SolidWorks": 5, "CATIA": 5, "AutoCAD": 5, "ANSYS": 5, "Design FMEA": 5, "DFM": 5, "DFA": 5, "GD&T": 5, "Stack Up Analysis": 5, "ASME Y14.5": 5, "2D Drawing Review": 3, "MathCAD": 5, "CE Marking": 5, "DNVGL": 5, "EN-13155": 5, "Machinery Directive 2006/42/EC": 5, "EN ISO 50308": 5, "EN ISO 14122": 5, "Reverse Engineering": 5, "Informatica Cloud Services (IICS)": 6, "Intelligent Data Management Cloud (IDMC)": 6, "Netezza": 6, "Teradata": 7, "Windows": 17, "Spark": 6, "Data Profiling": 6, "Business 360 Console": 5, "Cloud Data Governance": 6, "Cloud Data Catalog": 5, "Data Marts": 3, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 6, "API": 4, "ICS": 4, "ICRT": 4, "Nifi": 6, "Technical Design Documentation": 2, "Technical Architecture Documentation": 2, "Production Support": 8, "Code Review": 8, "Redux Toolkit": 5, "Axios": 5, "SWR": 5, "Formik": 5, "React Router": 5, "CSS3": 11, "ES6": 2, "Material UI": 5, "Tailwind CSS": 5, "PHP": 5, "Amazon Lambda": 6, "Gulp": 5, "Grunt": 5, "Webpack": 5, "GitHub Copilot": 3, "JWT": 5, "RBAC": 4, "Software Development Life Cycle (SDLC)": 1, "Spring Boot": 22, "Struts 2": 5, "Spring IOC": 5, "Spring MVC": 10, "Spring Data": 5, "Spring REST": 5, "Jersey REST": 5, "JSF": 5, "Apache POI": 5, "iText": 5, "Servlets": 11, "JSP": 11, "JDBC": 11, "JAX-WS": 5, "JAX-RS": 5, "Java Mail": 5, "JMS": 11, "JUnits": 4, "IBM MQ": 5, "Amazon EKS": 5, "Cucumber": 11, "Cypress": 11, "Dojo Toolkit": 4, "MongoDB": 17, "Quartz": 5, "Hibernate": 11, "Spring JPA": 5, "Putty": 5, "WinSCP": 5, "Bamboo": 5, "AWS Aurora Postgres": 4, "EJB": 6, "JSTL": 6, "JPA": 6, "Struts": 6, "Spring Framework": 5, "NestJS": 11, "WebLogic 11g": 5, "GlassFish": 11, "Resin": 6, "Oracle 11g/12c": 5, "IBM DB2": 6, "Eclipse": 6, "NetBeans": 6, "JDeveloper": 6, "IntelliJ": 5, "MyEclipse": 6, "VS Code": 6, "Toad": 18, "Visio": 6, "UML": 6, "CVS": 6, "SoapUI": 12, "JMS Hermes": 6, "JUnit": 31, "Log4j": 6, "JRockit Mission Control": 6, "JMeter": 6, "JRebel": 6, "Spiral": 5, "Prototype": 5, "Google Cloud Platform (GCP)": 12, "ITIL Foundation 2011": 1, "AWS Certified Solutions Architect Associate": 5, "AWS Certified Developer Associate": 5, "AWS Certified SysOps Administrator Associate": 5, "Dynatrace": 6, "LDAP": 6, "SiteMinder": 6, "SAML": 6, "Harvest": 6, "Nx Monorepo": 6, "OOAD": 5, "SOA": 7, "Single Page Application (SPA)": 5, "AWS CDK": 12, "@task": 6, "GitLab Pipelines": 4, "Oracle DBA": 4, "Oracle OCI": 4, "Oracle 19c": 5, "Oracle 12c": 5, "Oracle 21c": 5, "Oracle RAC": 3, "Oracle Data Guard": 5, "Oracle Enterprise Manager": 3, "Oracle TDE": 5, "Data Pump": 5, "Oracle Cloud Infrastructure": 3, "RMAN": 5, "Linux Shell Scripting": 5, "Crontab": 5, "AWR": 5, "ADDM": 5, "EXPLAIN PLAN": 5, "SQL*Trace": 2, "TKPROF": 5, "STATSPACK": 5, "WebLogic 14c": 5, "WebLogic 12c": 5, "JDK": 3, "SQL Server 2016": 6, "Veeam Backup and Recovery": 5, "Red Hat Linux 7": 3, "Red Hat Linux 8": 3, "Exadata": 5, "IBM LTO 9": 5, "IBM LTO 8": 5, "OCI IAM": 4, "OCI VCN": 4, "OCI Object Storage": 4, "OCI Load Balancing": 4, "OCI Auto Scaling": 3, "OCI CDN": 4, "OCI WAF": 4, "Autonomous Data Warehouse (ADW)": 4, "Autonomous Transaction Processing (ATP)": 4, "ITIL V3 Foundation": 5, "Prince2": 5, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 3, "Oracle 19c Database Administrator Training": 2, "Teradata Certified Administrator (V2R5)": 5, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 5, "Oracle E-Business Suite R12": 6, "Oracle Cloud Financials": 5, "Oracle Cloud General Ledger": 2, "Oracle Cloud Accounts Payable": 2, "Oracle Cloud Accounts Receivable": 2, "Oracle Cloud Fixed Assets": 2, "Oracle Cloud Cash Management": 2, "Oracle Cloud I-Expenses": 2, "Oracle Cloud Budgetary Control": 6, "Oracle Financial Accounting Hub": 6, "Oracle Transactional Business Intelligence (OTBI)": 5, "Financial Reporting Studio (FRS)": 5, "Smart View": 5, "Data Loader": 6, "Hyperion FRS": 6, "Business Process Management (BPM)": 5, "AIM Methodology": 6, "OUM Methodology": 6, "Sub Ledger Accounting (SLA)": 6, "Windows 2007/2008/2010": 6, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 6, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 6, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 6, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 6, "BIP": 5, "Pega Rules Process Engine": 5, "Pega Group Benefits Insurance Framework": 5, "Pega Product Builder": 2, "Pega 7.2.2": 5, "Pega 7.3": 5, "Pega 7.4": 5, "Pega 8": 5, "Unit testing": 6, "Harness": 5, "Sections": 5, "Flow Actions": 5, "List-View": 5, "Summary-View Reports": 5, "Report Definitions": 5, "Clipboard": 5, "Tracer": 5, "PLA": 5, "Product locking": 2, "Package locking": 2, "Ruleset locking": 2, "SDLC": 8, "E-Commerce": 5, "Insurance": 5, "Agents": 4, "Queue Processors": 4, "Decision Rules": 4, "Declarative Rules": 4, "Application Design": 5, "Case Management": 5, "Process Flows": 5, "Screen Flows": 5, "Data Transforms": 5, "Activities": 5, "Rule Resolution": 5, "Enterprise Class Structure": 4, "Dev Studio": 5, "App Studio": 5, "Admin Studio": 5, "CDH": 5, "Document review": 5, "Pega Marketing Consultant": 1, "Senior System Architect": 1, "System Architect": 1, "Postman": 16, "Selenium IDE": 6, "Selenium RC": 6, "Selenium WebDriver": 18, "Selenium Grid": 6, "TestNG": 18, "QTP": 6, "Gherkin": 6, "Ruby": 11, "Tortoise SVN": 4, "HP Quality Center": 6, "SeeTest (Experitest)": 2, "ACCELQ": 6, "JBehave": 6, "HP ALM": 11, "BrowserStack": 6, "LambdaTest": 6, "Functional Testing": 12, "Smoke Testing": 6, "System Testing": 12, "Integration Testing": 14, "Regression Testing": 12, "User Acceptance Testing (UAT)": 6, "UI Testing": 11, "Mobile Testing": 12, "Automation Testing": 7, "Web Testing": 6, "Compatibility Testing": 6, "Sanity Testing": 6, "Ad hoc Testing": 5, "Test Case Design": 5, "Test Plan Creation": 9, "Test Scripting": 1, "Test Execution": 6, "Defect Tracking": 5, "Bug Reporting": 3, "Test Management": 7, "AI-powered Automation": 4, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 5, "Android Testing": 5, "SSAS": 6, "Power BI Desktop": 6, "Power BI Service": 6, "M Language": 6, "Dimensional Modeling": 6, "Microsoft BI Stack": 6, "Power Pivot": 6, "Data Gateway": 6, "Row-Level Security (RLS)": 5, "Data Flows": 5, "DataMart": 5, "Power Automate": 6, "Visual Studio Code": 6, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 6, "ASAP Methodology": 6, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 6, "Business Process Mapping": 6, "FIT-GAP Analysis": 6, "Financial Reporting": 6, "SAP Solution Design": 6, "SAP Warehouse Management": 6, "Material Master Data Management": 5, "Procurement Processes": 5, "Order-to-Delivery Process": 3, "Demand Forecasting": 6, "Cash Pooling": 6, "Bank Reconciliation": 5, "F110 Automatic Payment Program": 3, "Real-time Cash Visibility System": 6, "Inhouse Cash Management": 6, "SAP Best Practices": 5, "Generally Accepted Accounting Principles (GAAP)": 4, "International Financial Reporting Standards (IFRS)": 4, "Financial Analysis": 6, "Automated Data Entry": 2, "AR Processing & Reporting": 2, "Customer Accounting": 1, "Vendor/Customer Open Items": 1, "SAP Integration": 1, "SAP S/4HANA": 5, "ABAP": 5, "OData": 5, "SAP UI5": 5, "Fiori": 5, "Fiori Elements": 5, "PI/PO": 5, "AIF": 5, "BRF+": 5, "Business Workflow": 5, "CRM": 5, "Web Dynpro ABAP": 5, "RAP": 5, "BTP": 5, "CAPM": 5, "Procure to Pay (PTP)": 5, "Order to Cash Management (OTC)": 5, "Production Planning (PP)": 5, "Quality Management (QM)": 5, "FI-AP": 5, "FI-AR": 5, "FI-GL (FICO)": 5, "RTR": 5, "SCM": 6, "Product Life Cycle Management (PLM)": 5, "Advanced Planner Optimizer (APO)": 5, "Extended Warehouse Management (EWM)": 5, "Data Dictionary (DDIC)": 5, "Module Pool Programming": 5, "Object-Oriented ABAP (OOABAP)": 5, "RFCs": 5, "BADIs": 5, "BDC": 5, "BAPI": 5, "BP Integrations": 5, "Enhancement Points": 5, "User Exits": 5, "Customer Exits": 5, "ALE IDOCs": 5, "Inbound/Outbound Proxy": 1, "SAP NetWeaver Gateway": 5, "Service Registration": 5, "Service Extension": 5, "CDS Views": 5, "AMDP": 5, "SAP Fiori List Report Application": 4, "Web IDE": 5, "BSP": 5, "SAP Fiori Launchpad": 5, "SAP UI5 Framework": 1, "Business Objects (BO)": 5, "ATC": 5, "SPDD": 5, "SPAU": 5, "SAP Security": 5, "PFTC": 4, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 5, "SAP Certified Development Associate - SAP Fiori Application Developer": 5, "Next.js": 5, "REST APIs": 16, "GraphQL APIs": 5, "AWS SAM": 5, "Apache ECharts": 5, "Cognito": 5, "OIDC": 5, "Mantine UI": 5, "Vite": 5, "MySQL Aurora": 5, "AWS API Gateway": 1, "Styled Components": 5, "Sanity": 5, "Amplify": 5, "ShadCN UI": 5, "Salesforce": 5, "CDL": 5, "Cisco Catalyst 9800 Wireless Controller": 5, "Talwar controller": 5, "AireOS controller": 5, "Cisco Access Points": 5, "Talwar Simulator": 5, "WiFi": 5, "802.11": 5, "WLAN": 5, "Ethernet": 5, "IP": 5, "TCP": 5, "UDP": 5, "CAPWAP": 5, "NETCONF": 5, "YANG": 5, "Swift": 5, "ClearCase": 5, "Cisco catalyst 3750 Switch": 5, "ios-xe asr 1K router": 5, "OpenWRT": 5, "Linux": 13, "QMI": 5, "AT interfaces": 5, "Ubus": 4, "Qualcomm SDX hardware": 5, "AT&T Echo controller": 4, "POLARIS": 5, "GDB": 5, "Gre": 5, "RFID": 5, "AeroScout tags": 5, "Cisco Aironet outdoor mesh access points": 4, "Cisco Prime Infrastructure": 5, "Mac filtering": 1, "Bash": 6, "Android App Development": 6, "Flask": 6, "Django": 6, "GraphQL": 6, "Amazon Web Services": 7, "macOS": 6, "Kali Linux": 6, "OAuth": 11, "AWS Certified Solutions Architect - Associate": 7, "Amazon SQS": 1, "Amazon Athena": 3, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 7, "Test Driven Development (TDD)": 5, "Mockito": 11, "Spark SQL": 5, "Server-Side Encryption": 5, "IAM Role Management": 3, "EKS": 4, "BottleRocket": 5, "React.js": 6, "Firebase Cloud Services": 6, "Cassandra": 6, "Android Studio": 6, "Bluetooth": 6, "Java Development": 5, "Advanced Java Development": 5, "Salesforce Platform Administrator": 5, "Salesforce Platform Developer": 5, "Appium": 6, "Perfecto": 6, "SeeTest": 6, "REST Assured": 6, "Karate Framework": 6, "UFT": 6, "LeanFT": 6, "Zephyr": 5, "Quality Center": 5, "Informatica 10.2": 6, "MicroStrategy": 6, "CICS": 6, "JCL": 6, "VSAM": 6, "Sufi": 6, "File-Aid": 6, "CA DevTest": 6, "ATOM": 3, "GCP": 5, "SSO": 5, "Test Strategy": 1, "Test Design": 4, "Test effort estimation": 2, "Requirements mapping": 4, "Risk-based testing": 6, "End-to-End testing": 6, "User Acceptance testing": 6, "Database testing": 6, "API testing": 6, "Web services testing": 6, "Microservices testing": 5, "Browser compatibility testing": 6, "Exploratory testing": 6, "ETL testing": 5, "Data Warehouse testing": 6, "Interactive Voice Response (IVR) testing": 4, "Customer Telephony Integration (CTI) testing": 4, "Mainframes testing": 3, "Service Virtualization": 5, "Continuous Integration and Continuous Deployment (CI/CD)": 2, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 4, "Oracle Financials Cloud Accounts Payable": 4, "Accounts Payable": 4, "Accounts Receivable": 4, "General Ledger": 4, "Fixed Assets": 4, "Cash Management": 4, "I-Expenses": 4, "I-Receivables": 4, "Order Management": 4, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 3, "Order to Cash (O2C)": 3, "Record to Report (R2R)": 3, "Oracle Allocations": 3, "Oracle Cloud": 1, "Intercompany": 4, "SeeTest/Experitest": 4, "Test Case Development": 1, "Test Schedule Creation": 2, "SAP Financial Accounting (FI)": 5, "SAP Controlling (CO)": 5, "SAP Sales and Distribution (SD)": 5, "SAP Materials Management (MM)": 5, "Hyperion Financial Management (HFM)": 5, "Financial Data Management (FDMEE)": 5, "Profit Center Accounting (PCA)": 1, "SAP Accounts Receivable (AR)": 2, "SAP Accounts Payable (AP)": 2, "General Ledger (GL)": 3, "Purchase Order (PO) Management": 1, "Inventory Planning": 4, "Automatic Payment Program (F110)": 3, "Network Security": 2, "Object Oriented Programming": 2, "Operating Systems": 2, "Design and Analysis of Algorithms": 2, "DBMS": 2, "Mainframe Testing": 3, "Performance Testing": 2, "User Interface Testing": 5, "Manual Testing": 1, "Mobile Web Testing": 4, "Desktop Application Testing": 1, "Web Application Testing": 1, "Data Analytics": 4, "Real-time Data Analytics": 1, "NoSQL Databases": 1, "Blueprints": 2, "Test Case Execution": 2, "Custom Visuals (Power BI)": 3, "Drill Down": 3, "Drill Through": 3, "Parameters": 3, "Cascading Filters": 3, "Interactive Dashboards": 1, "Reports": 1, "SAP Profit Center Accounting (PCA)": 1, "Automated Bank Reconciliation": 1, "Pricing Strategies": 2, "SAP MM Functionalities": 2, "Business Process Optimization": 1, "IVR Testing": 1, "CTI Testing": 1, "Continuous Integration": 3, "Continuous Deployment": 2, "High-Performance Architecture Design": 1, "Container-based Architecture Design": 1, "High Throughput System Architecture Design": 1, "Real-Time Data Analytics Solution Architecture Design": 1, "E-commerce Architecture Design": 1, "Microsoft technologies": 3, "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": 1, "Coded UI": 1, "MS SharePoint Server": 2, ".NET Core 6.0": 2, ".NET Core 8.0": 2, "XML Web Services": 3, ".NET Core Apps": 1, "Domain Driven Design (DDD)": 2, "Web Jobs": 4, "Model-View-Controller (MVC)": 1, "Tridion CMS": 3, "Internet of Things (IoT)": 2, "Azure SQL": 4, "Azure Pipelines": 4, "Rally": 3, "Multi-AZ": 1, "High Availability": 2, "Disaster Recovery": 1, "AWS-Kops (EKS)": 2, "HTTP": 4, "TLS": 4, "Windows Application Migration": 1, "OTT": 1, "RACI Matrix": 1, "S3": 5, "Performance Tuning": 2, "Query Optimization": 2, "Informatica Intelligent Cloud Services (IICS)": 4, "Informatica Data Management Center (IDMC)": 2, "Amazon Lake Formation": 2, "Cloud Migration": 4, "Nebula": 4, "Advanced Analytics": 3, "Data Compliance": 2, "Key Performance Indicators (KPIs)": 1, "Service Level Agreement (SLAs)": 1, "Data Flow Architectures": 2, "Data Collection": 2, "Data Storage Strategies": 2, "Agile Transformation": 1, "ISO27001 Compliance": 1, "Mechanical Design": 1, "Service Lifting Tools Design": 1, "2D Drawings Review": 2, "Unix Shell Scripting": 3, "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": 5, "Technical Design": 3, "Technical Architecture": 3, "Big Data": 4, "Real-time Data Integration": 2, "Amazon Web Services (S3, EC2, Lambda)": 2, "Silverlight": 1, "ITIL Foundation": 5, "AWS Certified Developer - Associate": 1, "AWS Certified SysOps Administrator - Associate": 1, "Oracle 19c Data Guard": 2, "Oracle 19c RAC": 2, "Red Hat Linux Enterprise 8": 2, "Red Hat Linux Enterprise 7": 2, "Oracle Enterprise Manager 12c": 3, "Oracle Enterprise Manager Grid Control 11g": 1, "Oracle Export/Import": 1, "Transportable Tablespaces": 1, "SQLTrace": 3, "Oracle Real Application Cluster": 1, "Windows Server 2016": 4, "Fault Tolerance": 1, "Scalability": 1, "Virtualization": 1, "Oracle Autonomous Data Warehouse (ADW)": 1, "Oracle Autonomous Transaction Processing (ATP)": 1, "VCN": 1, "Object Storage": 1, "Load Balancing": 1, "Auto Scaling": 1, "CDN": 1, "WAF": 1, "Exadata X9M-2": 4, "HP": 1, "IBM Power E980": 1, "IBM Power E850": 1, "OCI-Oracle Cloud Infrastructure Foundations Associate": 4, "OCA-Oracle Database Administrator Certified": 2, "Oracle 19c Database Administrator": 1, "ISO/IEC 27001": 2, "ISO 20000": 2, "ISO 27000": 1, "Pega Marketing Consultant (Certification)": 4, "Senior System Architect (Certification)": 4, "System Architect (Certification)": 4, "RICEF": 3, "SAP Script": 3, "Smart Forms": 4, "Adobe Forms": 4, "ALV Reports": 4, "Mac filter configuration": 1, "Athena": 2, "JDK 8": 2, "JDK 17": 2, "Java Development (Certification)": 1, "Advanced Java Development (Certification)": 1, "Salesforce Platform Administrator (Certification - In process)": 1, "Salesforce Platform Developer (Certification - In process)": 1, "C# Programming Certified Professional": 1, "Strapi": 1, "Wix": 1, "AG Grid": 1, "Domain-Driven Design (DDD)": 1, "Pub/Sub": 2, "HPSM": 2, "Subversion": 2, "Saga": 2, "Choreography": 2, "Circuit Breaker": 2, "AKS": 2, "Repository Design Pattern": 1, "Factory Design Pattern": 1, "Abstract Factory Design Pattern": 1, "SEO": 1, "Object-Oriented Programming": 2, "IoT": 1, "Microsoft .NET": 1, "AWS S3": 4, "Amazon Elastic Container Registry (ECR)": 3, "ADFS": 3, "Maven POM": 1, "Build.xml": 1, "AWS Storage Services": 1, "Security Groups": 1, "Multi-AZ VPC": 1, "Amazon CloudFormation": 2, "Amazon Auto Scaling": 2, "Microsoft Project (MPP)": 1, "Strategic Planning": 2, "EZTrieve": 1, "Peoplesoft Financials": 1, "Peoplesoft Supply Chain Management": 1, "Account Payables": 2, "Account Receivables": 2, "GL": 1, "Billing": 2, "Dimension Modeling": 3, "Fact Tables": 6, "Relational Databases": 2, "Data Mining": 1, "DWH": 2, "DM": 2, "Dimension Tables": 2, "Dashboards": 2, "Data Security and Compliance": 1, "Product Validation": 1, "API Development (ICS, ICRT)": 2, "Production Support (L3)": 2, "Test Plan Development": 1, "Test Script Development": 1, "IntelliJ IDEA": 1, "Spiral Model": 1, "Prototype Model": 1, "Oracle Database Administration": 1, "Oracle Cloud Infrastructure (OCI)": 1, "Oracle GoldenGate (implied)": 1, "Java JDK": 2, "Oracle Cloud I-Receivables": 1, "SharePoint": 1, "Microsoft Office": 2, "Ad-hoc Testing": 1, "Test Planning": 1, "SSMS": 2, "SQL Server Data Tools": 2, "SAP Profit Center Accounting (PCBS)": 2, "SAP AR Processing & Reporting": 1, "Cash Discount Management": 2, "Dispute and Deduction Management": 2, "SAP FI-GL Transactions": 1, "SAP AP/AR Transactions": 1, "Vendor/Customer Open Item Management": 2, "BPP Documentation": 1, "Order-to-Delivery Process Optimization": 2, "Certified SAP Functional Consultant": 1, "PFTC Roles": 1, "SAP ECC": 1, "SAP Scripts": 1, "Device Drivers": 2, "LED Manager": 2, "Mesh Networking": 2, "Cisco Aironet": 1, "ATOM (Mainframes/AS400 automation tool)": 1, "Test Automation": 1, "Test Strategy Creation": 3, "Test Coverage": 3, "Requirements Prioritization": 1, "ASP": 1, "N-tier applications": 2, "Client-server applications": 2, "Auto-scaling": 1, "Artifactory": 1, "Physical Database Design": 1, "Database Tuning": 1, "Snowpark API": 1, "PII": 2, "PCI": 2, "Trifacta": 1, "Oracle 8i": 1, "Oracle 11i": 1, "DB2 8.1": 2, "Python Worksheet": 1, "Certified Professional Data Engineer": 1, "ETL Design": 2, "ETL Development": 2, "Python Scripting": 1, "Informatica Data Management Cloud (IDMC)": 2, "Data Mart": 1, "Requirement Gathering": 1, "Solution Architecture": 2, "Dojo": 1, "Spring": 1, "Oracle Grid Control 11g": 2, "SQL*Plus": 2, "OCI AutoScaling": 1, "HP/IBM Power E980": 1, "HP/IBM Power E850": 1, "Exadata CS DBX7": 1, "Defect Management": 1, "Speedometer Charts": 1, "Sankey Diagrams": 1, "Pareto Charts": 1, "Waterfall Charts": 1, "SAP Material Master Data Management": 1, "SAP Procurement Processes": 1, "GAAP (Generally Accepted Accounting Principles)": 2, "IFRS (International Financial Reporting Standards)": 2, "SAP Treasury Modules": 2, "LTE": 1, "5G": 1, "Firehose": 1, "Test Estimation": 2, "Cloud Testing (AWS, GCP, Azure, Microsoft)": 1, "OAuth Testing": 1, "SSO Testing": 1, "KPI Development": 1, "SLA Development": 1, "Microsoft Excel Spreadsheets": 1, "Oracle 8i/11i": 1, "Snowpipe": 1, "Domain Driven Development (DDD)": 1, "Indexing": 1, "Database Performance Tuning": 1, "Database Partitioning": 1, "Error Handling": 1, "Oracle Database (11g, 10g, 9i, 8x)": 1, "Business 360": 1, "Data Pipelines": 2, "Bug Fixing": 1, "Pega 8.4": 1, "Pega 8.4.1": 1, "PCBS (Profit Center Budgeting System)": 1, "FI-GL Transactions": 1, "AP/AR Transactions": 1, "BPPs (Business Process Procedures) Documentation": 1, "Product Assortment Management": 1, "Event-Driven Architecture": 1, "Backend for Frontend (BFF)": 1, "Active Directory": 1, "Continuous Integration/Continuous Deployment (CI/CD)": 1, "Regression Analysis": 1, "Component-Based Architecture": 1, "Multi-tier Distributed Applications": 1, "Oracle Financials Cloud Receivables": 1, "Business Intelligence Publisher (BIP)": 1, "AI": 1, "Chatbot": 1, "AWS Aurora": 1, "Oracle 19c Database Administrator Training from Koenig Database Administration": 1, "Dimensions": 1, "Cloud Data Integration": 1, "Cloud Application Integration": 1, "Materialized Views": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints", "High-Performance Architecture Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "C# Programming Certified Professional"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM", "Coded UI", "Oracle PL/SQL", "MS SharePoint Server", "MySQL", "Azure", "SSRS", "<PERSON><PERSON><PERSON>", "Wix", "Git", "Oracle", "PL/SQL"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C", "Pivotal Cloud Foundry (PCF)", "MVC", "Single Page Application (SPA)", "OAuth 2.0", "Saga Pattern", "API Gateway", "Circuit Breaker <PERSON>", "Event-Driven Architecture", "CQRS", "Backend for Frontend (BFF)", "Object-Oriented Programming (OOP)"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Angular", "React", "XML Web Services", ".NET Core Apps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "T-SQL", "MS SQL Server", "Node.js", "AG Grid", "Domain-Driven Design (DDD)", "Pub/Sub", "ASP", "N-tier applications", "Client-server applications", ".NET", "Auto-scaling", "SQL Server 2016", "Domain Driven Development (DDD)"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems", "ASP.NET Core", "C#", "Angular", ".NET Framework", "Web Jobs", "Visual Studio", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Tridion CMS", "SQL Server", "PMP", "Internet of Things (IoT)", "HPSM", "SOA", "Subversion", "Saga", "Choreography", "Circuit Breaker", "AKS", "Repository Design Pattern", "Factory Design Pattern", "Abstract Factory Design Pattern", "Azure Cloud", "SEO", "Object-Oriented Programming", "Agile", "SCRUM", "IoT"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder", "Azure SQL", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "<PERSON><PERSON>", "Rally", "Microsoft .NET", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "Azure App Services", "AWS S3", "Amazon Elastic Container Registry (ECR)", "ADFS", "Amazon S3"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>", "Linux", "Multi-AZ", "High Availability", "Disaster Recovery", "Maven <PERSON>", "Build.xml", "AWS Storage Services", "Security Groups", "Multi-AZ VPC", "Artifactory", "Active Directory"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption", "AWS-Kops (EKS)", "HTTP", "TLS", "Amazon CloudFormation", "Amazon Auto Scaling", "Amazon Lambda", "AWS", "Azure DevOps", "Pivotal Cloud Foundry", "SCM", "Continuous Integration/Continuous Deployment (CI/CD)"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM", "Windows Application Migration", "OTT", "RACI Matrix", "Microsoft Project (MPP)"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001", "Strategic Planning", "EZTrieve"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization", "S3", "Git", "Informatica PowerCenter", "Business Intelligence", "Data Migration", "Performance Tuning", "Query Optimization", "Peoplesoft Financials", "Peoplesoft Supply Chain Management", "Account Payables", "Account Receivables", "GL", "Billing", "Data Validation", "Dimension Modeling", "Fact Tables", "Physical Database Design", "Database Tuning", "Snowpark API", "PII", "PCI", "Trifacta", "SQL Server", "Oracle 8i", "Oracle 11i", "DB2 8.1", "<PERSON> (GL)", "Python Worksheet", "Certified Professional Data Engineer", "Oracle 8i/11i", "Snowpipe"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "SSIS (SQL Server Integration Services)", "Relational Databases", "Regression Analysis"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "Amazon Web Services (AWS)", "Amazon Athena", "Amazon Lake Formation", "Cloud Migration", "Data Governance", "Neb<PERSON>", "AWS S3", "Data Mining", "DWH", "DM", "Fact Tables", "Dimension Tables", "Dashboards", "Advanced Analytics", "ETL Design", "ETL Development", "Python Scripting", "Informatica Data Management Cloud (IDMC)", "Data Mart", "Project Management", "Requirement Gathering", "Solution Architecture", "Erro<PERSON>", "Oracle Database (11g, 10g, 9i, 8x)"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile", "Business Intelligence", "Advanced Analytics", "Microsoft Excel", "Data Compliance", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Collection", "Data Storage Strategies", "Agile Transformation", "ISO27001 Compliance", "Data Security and Compliance", "Project Management", "KPI Development", "SLA Development", "Microsoft Excel Spreadsheets", "Relational Databases"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering", "Mechanical Design", "Service Lifting Tools Design", "2D Drawings Review", "Product Validation", "Microsoft Office"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review", "Unix Shell Scripting", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Technical Design", "Technical Architecture", "Big Data", "PySpark", "Real-time Data Integration", "API Development (ICS, ICRT)", "Production Support (L3)", "Test Plan Development", "Test Script Development", "Business 360", "Data Pipelines", "Dimensions", "Fact Tables", "Cloud Data Integration", "Cloud Application Integration", "Materialized Views", "Stored Procedures"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)", "Amazon Web Services (S3, EC2, Lambda)", "Silverlight", "Node.js"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres", "JUnit", "Dojo", "PostgreSQL", "AWS Aurora"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines", "ITIL Foundation", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "J2EE", "IntelliJ IDEA", "Spiral Model", "Prototype Model", "Spring", "Component-Based Architecture", "Multi-tier Distributed Applications", "WebLogic", "Oracle", "OOPS"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Oracle 19c Data Guard", "Oracle 19c RAC", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle Export/Import", "Transportable Tablespaces", "SQLTrace", "Oracle Real Application Cluster", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "High Availability", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "IAM", "VCN", "Object Storage", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "Exadata X9M-2", "HP", "IBM Power E980", "IBM Power E850", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "ISO/IEC 27001", "ISO 20000", "ISO 27000", "Oracle Database Administration", "Oracle Cloud Infrastructure (OCI)", "Oracle GoldenGate (implied)", "Java JDK", "Terada<PERSON>", "Oracle Grid Control 11g", "SQL*Plus", "OCI AutoScaling", "HP/IBM Power E980", "HP/IBM Power E850", "Exadata CS DBX7", "Oracle 19c Database Administrator Training from Koenig Database Administration"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany", "Oracle Cloud I-Receivables", "Oracle Financials Cloud Receivables", "Business Intelligence Publisher (BIP)"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)", "Pega 8.4", "Pega 8.4.1"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN", "SharePoint", "Microsoft Office", "Continuous Integration"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation", "Unit testing", "Test Case Execution", "Mobile Web Testing", "SDLC", "Performance Testing", "Ad-hoc Testing", "Test Planning", "Defect Management"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "Excel", "SSMS", "SQL Server Data Tools", "Speedometer Charts", "Sankey Diagrams", "Pareto Charts", "Waterfall Charts", "Stored Procedures", "Functions", "Triggers", "Indexing", "Database Performance Tuning", "Query Optimization", "Database Partitioning"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)", "SAP Profit Center Accounting (PCA)", "Automated Bank Reconciliation", "Pricing Strategies", "SAP MM Functionalities", "Business Process Optimization", "Agile Methodologies", "SAP Profit Center Accounting (PCBS)", "SAP AR Processing & Reporting", "Cash Discount Management", "Dispute and Deduction Management", "SAP FI-GL Transactions", "SAP AP/AR Transactions", "Vendor/Customer Open Item Management", "BPP Documentation", "Order-to-Delivery Process Optimization", "Certified SAP Functional Consultant", "SAP Material Master Data Management", "SAP Procurement Processes", "GAAP (Generally Accepted Accounting Principles)", "IFRS (International Financial Reporting Standards)", "SAP Treasury Modules", "PCBS (Profit Center Budgeting System)", "FI-GL Transactions", "AP/AR Transactions", "BPPs (Business Process Procedures) Documentation", "Product Assortment Management"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "RICEF", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "PFTC Roles", "SAP ECC", "Agile", "Waterfall", "SAP Scripts"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL", "API Gateway", "Amazon Web Services", "MySQL", "Microservices Architecture", "Lambda", "AI", "<PERSON><PERSON><PERSON>"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering", "Mac filter configuration", "Device Drivers", "LED Manager", "Mesh Networking", "Cisco Aironet", "Unit testing", "Integration Testing", "LTE", "5G", "<PERSON><PERSON>xing"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket", "Amazon Web Services (AWS)", "Lambda", "EC2", "SQS", "S3", "SNS", "CI/CD", "CloudWatch", "Step Functions", "AWS Glue", "Athena", "JDK 8", "JDK 17", "AWS Athena", "IAM", "Firehose"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C", "IVR Testing", "CTI Testing", "Continuous Integration", "Continuous Deployment", "ATOM (Mainframes/AS400 automation tool)", "Test Automation", "Test Strategy Creation", "Test Coverage", "Requirements Prioritization", "ETL", "Test Estimation", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "OAuth Testing", "SSO Testing"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Chary"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "PunniyaKodi V updated resume", "Chary"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV", "Vidwaan_vidwan_resume"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Chary", "<PERSON>"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "<PERSON><PERSON>"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "PunniyaKodi V updated resume"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead", "Chandra_Resume"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>_DotNET"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON><PERSON>"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chary"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Lambda": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON>il .Net lead"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Step Functions": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "Chary", "Uday"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary", "PunniyaKodi V updated resume"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure App Services": ["Chary", "<PERSON><PERSON>_DotNET"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Saga Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary", "<PERSON><PERSON>_DotNET"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "PunniyaKodi V updated resume"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET", "Chary"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>il .Net lead"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>", "Chandra_Resume"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet", "Chary"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Chary"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Chandra_Resume"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON>", "DA manager <PERSON><PERSON>"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON>_DotNET"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>", "Chary"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume", "Chary"], "Single Page Application (SPA)": ["Chandra_Resume", "<PERSON><PERSON><PERSON>il .Net lead"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA", "PunniyaKodi V updated resume"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV", "A<PERSON><PERSON>_resume"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday", "<PERSON><PERSON><PERSON>"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)", "Chandra_Resume"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"], "Test Case Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Custom Visuals (Power BI)": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Through": ["<PERSON><PERSON><PERSON><PERSON>"], "Parameters": ["<PERSON><PERSON><PERSON><PERSON>"], "Cascading Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Interactive Dashboards": ["<PERSON><PERSON><PERSON><PERSON>"], "Reports": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "Pricing Strategies": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM Functionalities": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "IVR Testing": ["SivakumarDega_CV"], "CTI Testing": ["SivakumarDega_CV"], "Continuous Integration": ["SivakumarDega_CV", "pavani_resume"], "Continuous Deployment": ["SivakumarDega_CV"], "High-Performance Architecture Design": ["Laxman_Gite"], "Container-based Architecture Design": ["Laxman_Gite"], "High Throughput System Architecture Design": ["Laxman_Gite"], "Real-Time Data Analytics Solution Architecture Design": ["Laxman_Gite"], "E-commerce Architecture Design": ["Laxman_Gite"], "Microsoft technologies": ["Laxman_Gite"], "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": ["Laxman_Gite"], "Coded UI": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SharePoint Server": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], ".NET Core 6.0": ["PunniyaKodi V updated resume"], ".NET Core 8.0": ["PunniyaKodi V updated resume"], "XML Web Services": ["PunniyaKodi V updated resume"], ".NET Core Apps": ["PunniyaKodi V updated resume"], "Domain Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Web Jobs": ["Chary"], "Model-View-Controller (MVC)": ["Chary"], "Tridion CMS": ["Chary"], "Internet of Things (IoT)": ["Chary"], "Azure SQL": ["<PERSON><PERSON>_DotNET"], "Azure Pipelines": ["<PERSON><PERSON>_DotNET"], "Rally": ["<PERSON><PERSON>_DotNET"], "Multi-AZ": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "High Availability": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Disaster Recovery": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS-Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "HTTP": ["<PERSON><PERSON><PERSON>"], "TLS": ["<PERSON><PERSON><PERSON>"], "Windows Application Migration": ["Puneet"], "OTT": ["Puneet"], "RACI Matrix": ["Puneet"], "S3": ["<PERSON>", "Vidwaan_vidwan_resume"], "Performance Tuning": ["<PERSON>"], "Query Optimization": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Informatica Intelligent Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Center (IDMC)": ["<PERSON><PERSON><PERSON>"], "Amazon Lake Formation": ["<PERSON><PERSON><PERSON>"], "Cloud Migration": ["<PERSON><PERSON><PERSON>"], "Nebula": ["<PERSON><PERSON><PERSON>"], "Advanced Analytics": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Data Compliance": ["DA manager <PERSON><PERSON>"], "Key Performance Indicators (KPIs)": ["DA manager <PERSON><PERSON>"], "Service Level Agreement (SLAs)": ["DA manager <PERSON><PERSON>"], "Data Flow Architectures": ["DA manager <PERSON><PERSON>"], "Data Collection": ["DA manager <PERSON><PERSON>"], "Data Storage Strategies": ["DA manager <PERSON><PERSON>"], "Agile Transformation": ["DA manager <PERSON><PERSON>"], "ISO27001 Compliance": ["DA manager <PERSON><PERSON>"], "Mechanical Design": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tools Design": ["<PERSON><PERSON><PERSON>"], "2D Drawings Review": ["<PERSON><PERSON><PERSON>"], "Unix Shell Scripting": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": ["<PERSON><PERSON><PERSON>"], "Technical Design": ["<PERSON><PERSON><PERSON>"], "Technical Architecture": ["<PERSON><PERSON><PERSON>"], "Big Data": ["<PERSON><PERSON><PERSON>"], "Real-time Data Integration": ["<PERSON><PERSON><PERSON>"], "Amazon Web Services (S3, EC2, Lambda)": ["<PERSON><PERSON><PERSON>"], "Silverlight": ["<PERSON><PERSON><PERSON>"], "ITIL Foundation": ["Chandra_Resume"], "AWS Certified Developer - Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator - Associate": ["Chandra_Resume"], "Oracle 19c Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Transportable Tablespaces": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQLTrace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Real Application Cluster": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Fault Tolerance": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Scalability": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Virtualization": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA-Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO/IEC 27001": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 20000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 27000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Pega Marketing Consultant (Certification)": ["<PERSON><PERSON><PERSON>"], "Senior System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "RICEF": ["Uday"], "SAP Script": ["Uday"], "Smart Forms": ["Uday"], "Adobe Forms": ["Uday"], "ALV Reports": ["Uday"], "Mac filter configuration": ["A<PERSON><PERSON>_resume"], "Athena": ["Vidwaan_vidwan_resume"], "JDK 8": ["Vidwaan_vidwan_resume"], "JDK 17": ["Vidwaan_vidwan_resume"], "Java Development (Certification)": ["Soham_Resume_Java"], "Advanced Java Development (Certification)": ["Soham_Resume_Java"], "Salesforce Platform Administrator (Certification - In process)": ["Soham_Resume_Java"], "Salesforce Platform Developer (Certification - In process)": ["Soham_Resume_Java"], "C# Programming Certified Professional": ["Laxman_Gite"], "Strapi": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Wix": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AG Grid": ["PunniyaKodi V updated resume"], "Domain-Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Pub/Sub": ["PunniyaKodi V updated resume"], "HPSM": ["Chary"], "Subversion": ["Chary"], "Saga": ["Chary"], "Choreography": ["Chary"], "Circuit Breaker": ["Chary"], "AKS": ["Chary"], "Repository Design Pattern": ["Chary"], "Factory Design Pattern": ["Chary"], "Abstract Factory Design Pattern": ["Chary"], "SEO": ["Chary"], "Object-Oriented Programming": ["Chary"], "IoT": ["Chary"], "Microsoft .NET": ["<PERSON><PERSON>_DotNET"], "AWS S3": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Amazon Elastic Container Registry (ECR)": ["<PERSON><PERSON>_DotNET"], "ADFS": ["<PERSON><PERSON>_DotNET"], "Maven POM": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Build.xml": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Storage Services": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Security Groups": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Multi-AZ VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon CloudFormation": ["<PERSON><PERSON><PERSON>"], "Amazon Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Microsoft Project (MPP)": ["Puneet"], "Strategic Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "EZTrieve": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Peoplesoft Financials": ["<PERSON>"], "Peoplesoft Supply Chain Management": ["<PERSON>"], "Account Payables": ["<PERSON>"], "Account Receivables": ["<PERSON>"], "GL": ["<PERSON>"], "Billing": ["<PERSON>"], "Dimension Modeling": ["<PERSON>"], "Fact Tables": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Relational Databases": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Data Mining": ["<PERSON><PERSON><PERSON>"], "DWH": ["<PERSON><PERSON><PERSON>"], "DM": ["<PERSON><PERSON><PERSON>"], "Dimension Tables": ["<PERSON><PERSON><PERSON>"], "Dashboards": ["<PERSON><PERSON><PERSON>"], "Data Security and Compliance": ["DA manager <PERSON><PERSON>"], "Product Validation": ["<PERSON><PERSON><PERSON>"], "API Development (ICS, ICRT)": ["<PERSON><PERSON><PERSON>"], "Production Support (L3)": ["<PERSON><PERSON><PERSON>"], "Test Plan Development": ["<PERSON><PERSON><PERSON>"], "Test Script Development": ["<PERSON><PERSON><PERSON>"], "IntelliJ IDEA": ["Chandra_Resume"], "Spiral Model": ["Chandra_Resume"], "Prototype Model": ["Chandra_Resume"], "Oracle Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure (OCI)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle GoldenGate (implied)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Java JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SharePoint": ["pavani_resume"], "Microsoft Office": ["pavani_resume", "<PERSON><PERSON><PERSON>"], "Ad-hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Planning": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SSMS": ["<PERSON><PERSON><PERSON><PERSON>"], "SQL Server Data Tools": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCBS)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Discount Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Dispute and Deduction Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Item Management": ["<PERSON><PERSON><PERSON><PERSON>"], "BPP Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "Certified SAP Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "PFTC Roles": ["Uday"], "SAP ECC": ["Uday"], "SAP Scripts": ["Uday"], "Device Drivers": ["A<PERSON><PERSON>_resume"], "LED Manager": ["A<PERSON><PERSON>_resume"], "Mesh Networking": ["A<PERSON><PERSON>_resume"], "Cisco Aironet": ["A<PERSON><PERSON>_resume"], "ATOM (Mainframes/AS400 automation tool)": ["SivakumarDega_CV"], "Test Automation": ["SivakumarDega_CV"], "Test Strategy Creation": ["SivakumarDega_CV"], "Test Coverage": ["SivakumarDega_CV"], "Requirements Prioritization": ["SivakumarDega_CV"], "ASP": ["PunniyaKodi V updated resume"], "N-tier applications": ["PunniyaKodi V updated resume"], "Client-server applications": ["PunniyaKodi V updated resume"], "Auto-scaling": ["PunniyaKodi V updated resume"], "Artifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Physical Database Design": ["<PERSON>"], "Database Tuning": ["<PERSON>"], "Snowpark API": ["<PERSON>"], "PII": ["<PERSON>"], "PCI": ["<PERSON>"], "Trifacta": ["<PERSON>"], "Oracle 8i": ["<PERSON>"], "Oracle 11i": ["<PERSON>"], "DB2 8.1": ["<PERSON>"], "Python Worksheet": ["<PERSON>"], "Certified Professional Data Engineer": ["<PERSON>"], "ETL Design": ["<PERSON><PERSON><PERSON>"], "ETL Development": ["<PERSON><PERSON><PERSON>"], "Python Scripting": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Data Mart": ["<PERSON><PERSON><PERSON>"], "Requirement Gathering": ["<PERSON><PERSON><PERSON>"], "Solution Architecture": ["<PERSON><PERSON><PERSON>"], "Dojo": ["<PERSON><PERSON>"], "Spring": ["Chandra_Resume"], "Oracle Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Plus": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI AutoScaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS DBX7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Defect Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Speedometer Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Sankey Diagrams": ["<PERSON><PERSON><PERSON><PERSON>"], "Pareto Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Waterfall Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "GAAP (Generally Accepted Accounting Principles)": ["<PERSON><PERSON><PERSON><PERSON>"], "IFRS (International Financial Reporting Standards)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Treasury Modules": ["<PERSON><PERSON><PERSON><PERSON>"], "LTE": ["A<PERSON><PERSON>_resume"], "5G": ["A<PERSON><PERSON>_resume"], "Firehose": ["Vidwaan_vidwan_resume"], "Test Estimation": ["SivakumarDega_CV"], "Cloud Testing (AWS, GCP, Azure, Microsoft)": ["SivakumarDega_CV"], "OAuth Testing": ["SivakumarDega_CV"], "SSO Testing": ["SivakumarDega_CV"], "KPI Development": ["DA manager <PERSON><PERSON>"], "SLA Development": ["DA manager <PERSON><PERSON>"], "Microsoft Excel Spreadsheets": ["DA manager <PERSON><PERSON>"], "Oracle 8i/11i": ["<PERSON>"], "Snowpipe": ["<PERSON>"], "Domain Driven Development (DDD)": ["PunniyaKodi V updated resume"], "Indexing": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Performance Tuning": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Partitioning": ["<PERSON><PERSON><PERSON><PERSON>"], "Error Handling": ["<PERSON><PERSON><PERSON>"], "Oracle Database (11g, 10g, 9i, 8x)": ["<PERSON><PERSON><PERSON>"], "Business 360": ["<PERSON><PERSON><PERSON>"], "Data Pipelines": ["<PERSON><PERSON><PERSON>"], "Bug Fixing": ["A<PERSON><PERSON>_resume"], "Pega 8.4": ["<PERSON><PERSON><PERSON>"], "Pega 8.4.1": ["<PERSON><PERSON><PERSON>"], "PCBS (Profit Center Budgeting System)": ["<PERSON><PERSON><PERSON><PERSON>"], "FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "BPPs (Business Process Procedures) Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Product Assortment Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Event-Driven Architecture": ["<PERSON><PERSON><PERSON>il .Net lead"], "Backend for Frontend (BFF)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Active Directory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Continuous Integration/Continuous Deployment (CI/CD)": ["<PERSON><PERSON><PERSON>"], "Regression Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Component-Based Architecture": ["Chandra_Resume"], "Multi-tier Distributed Applications": ["Chandra_Resume"], "Oracle Financials Cloud Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Intelligence Publisher (BIP)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Chatbot": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS Aurora": ["<PERSON><PERSON>"], "Oracle 19c Database Administrator Training from Koenig Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Dimensions": ["<PERSON><PERSON><PERSON>"], "Cloud Data Integration": ["<PERSON><PERSON><PERSON>"], "Cloud Application Integration": ["<PERSON><PERSON><PERSON>"], "Materialized Views": ["<PERSON><PERSON><PERSON>"]}, "skill_categories": {}, "skill_metadata": {}}