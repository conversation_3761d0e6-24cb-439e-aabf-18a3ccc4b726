{"all_skills": ["GraphQL APIs", "Dimension Modeling", "GraphQL", "Peoplesoft Financials", "Visual Studio", "Network Security", "Statistical Analysis", "AWS S3", "Omniture", "ETL Development", "Pega 7.4", "SQL Server 2014", "GCP", "Event-Driven Architecture", "Senior System Architect", "Terraform", "IP", "Getting Started with Power BI", "JUnits", "CRM", "Azure Container Registry (ACR)", "Risk Management", "Snow Pipe", "Windows Server", "Automated Data Entry", "Smart View", "Azure Container Storage", "Senior System Architect (Certification)", "Performance Tuning", "Business Objects (BO)", "Azure Cloud Architectures", "Speedometer Charts", "Visual Studio 2022", "<PERSON><PERSON><PERSON>", "React.js", "Oracle Cloud General Ledger", "Microsoft SQL Server", "Amazon CloudFront", "Technical Design", "SQL*Trace", "<PERSON><PERSON><PERSON>", "Oracle 19c Data Guard", "ABAP", "Gulp", "Material Design", "Configuration Management", "AWS Glue", "Service Lifting Tools Design", "Excel", "Microsoft Project (MPP)", "Machined Parts Design", "ADFS", "HTML", "ER Studio", "Intercompany", "REST Assured", "Azure Key Vault", "LED Manager", "<PERSON><PERSON>", "Amazon Redshift", "ADO.NET", "Microsoft .NET", "Business 360", "Interactive Dashboards", "Tailwind CSS", "InstallShield", "Test Schedule Creation", "Reports", "Azure Data Factory (ADF)", "Application Insights", "Observable", "Salesforce Platform Administrator (Certification - In process)", "Warehouse Management", "Message Queue", "Avro", "SAP UI5 Framework", "Oracle TDE", "Smoke Testing", "JSF", "Service Registration", "Active Reports", "Oracle 19c Database Administrator Training", "ELT", "Windows Application", "Oracle Cloud Infrastructure (OCI)", "ElasticSearch", "CloudFormation", "MS SharePoint Server", "C# Programming", "Real-Time Data Analytics Solution Architecture Design", "GlassFish", "AWS Storage", "AZ-104", "Cash Management", "Pivotal Cloud Foundry (PCF)", "TFS", "Pega Marketing Consultant", "IAM", "LINQ to SQL", "Quality Management (QM)", "Choreography Pattern", "DB2 8.1", "SAP AR Processing & Reporting", "Open API", "Service Virtualization", "IVR Testing", "OCI IAM", "Test Case Development", "Azure Blobs", "ACCELQ", "Web Jobs", "GitLab Pipelines", "Test Coverage", "Cisco Aironet", "ios-xe asr 1K router", "Styled Components", "Cloud Data Catalog", "JMS", "Azure Cloud", "SeeTest/Experitest", "FRS", "CosmosDB", "CDL", "<PERSON><PERSON>", "Technical Design Documentation", "High-Performance Architecture", "Process Improvement", "Pega Rules Process Engine", "AML Compliance", "Module Pool Programming", "Activities", "ASP.NET Core", "Design FMEA", "Spring JPA", "Abstract Factory Pattern", "AWS Step Functions", "OCA - Oracle Database Administrator Certified", "Cloud Data Governance", "ISO 27000", "High Availability", "Oracle Enterprise Manager (OEM)", "BSP", "Backend for Frontend (BFF)", "Tortoise SVN", "Inventory Planning", "Winforms", "Operating Systems", "MS Access", "YAML Pipelines", "Accounts Receivable (AR) Processing", "Athena", "AWS Athena", "Sub Ledger Accounting (SLA)", "CSS3", "ISTQB Certified Tester Foundation Level", "AWS Certified Developer Associate", "Machine Learning", "Code Review", "ASPX", "Azure SQL Server", "Cognito", "Index Design", "Mobile Web Testing", "Application Load Balancer", "VPC", "Oracle Cloud Budgetary Control", "Micro-services Architecture Design", "Snowflake", "HP/IBM Power E850", "CDN", "JPA", "Azure Storage", "JMeter", "Custom Visuals (Power BI)", "Amazon Elastic Kubernetes Service (EKS)", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "Automated Bank Reconciliation", "Circuit Breaker", "Datadog", "WebJobs", "Database Tuning", "Dispute and Deduction Management", "Amazon ECR", "SeeTest", "Elastic Load Balancers", "Analytical Applications", "Data Collection and Storage", "Ping Identity", "Trifacta", "Test Strategy", "Azure Developer", "Data Compliance", "Oracle Cloud Financials", "Tomcat", "Advanced Analytics", "Dimensional Modeling", "Virtual Network", "WebSphere", "Data Encryption", "Oracle Cloud Infrastructure", "ICS", "Oracle 12c", "AWS", "OCA-Oracle Database Administrator Certified", "Data Warehouse testing", "Hibernate", "SQL Server Integration Services (SSIS)", "GD&T", "Windows Service", "OAuth 2.0", "MuleSoft", "IBM LTO 8", "Sections", "IoT Systems", "SAP Script", "Production Support", "Telerik", "ETL Design and Development", "Power BI Desktop", "Object-Oriented Programming", "MS SQL Server 2019", "Maven POM.xml", "Browser compatibility testing", "ATOM", "SAP Accounts Receivable (AR)", "Auto-scaling", "Apache Airflow", "Python Scripting", "Test Design", "AWS Lambda", "SQL", "SAP ECC", "Amazon RDS", "Erro<PERSON>", "SAS Data Integration Studio", "Mobile Testing", "Software Development Life Cycle (SDLC)", "Dojo", "ITIL Foundation", "Waterfall", "Customer Telephony Integration (CTI) testing", "IntelliJ", "Fault Tolerance", "Moq", "T-SQL", "GL", "Windows Server 2016", "FI-GL (FICO)", "Elasticache", "Backup and Recovery", "JRockit Mission Control", "ADFS (Active Directory Federation Services)", "QMI", "LINQ", "Azure AD", "High Throughput System Architecture Design", "SQLite", "Lambda Expressions", "AWS-Kops (EKS)", "Spring", ".NET Framework 3.5", "Data Flow Architectures", "EN-13155", "BP Integrations", "Accounts Payable", "Oracle Fusion Financials", "Web Services", "Android", "VB.NET", "Data Automation", "Fact Tables", "Azure Storage Services", "Agile Methodologies", "Organizational Change Management (OCM)", "FIT-GAP Analysis", "Key Management", "Real-time Cash Visibility System", "MS-SQL", "User Exits", "Performance Testing", "<PERSON><PERSON><PERSON><PERSON>", "Red Hat Linux 8.x", "<PERSON><PERSON><PERSON>", "Data Decryption", "Requirements Prioritization", "WLAN", "Problem Solving", "Domain-Driven Design (DDD)", "IBM MQ", "SAML", "Rule Resolution", "WebLogic 12c", "Active Directory", "<PERSON> Data Modeler", "Azure Table Storage", "SQL Server Reporting Services (SSRS)", "PCBS (Profit Center Budgeting System)", "General Accepted Accounting Principles (GAAP)", "Ezetrieves", "Fixed Assets", "Oracle Autonomous Data Warehouse (ADW)", "Tridion CMS 2009", "Server-Side Encryption", "Adobe Forms", "AWS CLI", "Microsoft Project", "Oracle 11g/12c", "SAP NetWeaver Gateway", "Oracle 19c Database Administrator Training (Koenig)", "LDAP", "Gre", "Agile Methodology", "Business Process Optimization", "Spring MVC", "SAP Fiori List Report Application", "Mechanical Product Design", "Java JDK", "Fivetran", "Sanity Testing", "Agile Transformation", "VMware", "KPI Development", "EN ISO 50308", "Confluence", "Oracle Financials Cloud General Ledger", "CDH", "Onshore Rigging Calculations", "Unix Shell Scripting", "Cascading Filters", "API Gateway", "Customer Accounting", "Slowly Changing Dimensions (SCD)", "SDLC", "Azure Container Registry", "<PERSON>aud Detection", "Angular 10", "PI/PO", "Perfecto", ".NET Core Web API", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "Pub/Sub", "Functions (Database)", "Informatica Power Center", "CDS Annotations", "SCRUM", "Angular 9", "Android App Development", "AngularJS", "NetBeans", "RESTful", "Account Receivables", "ALE IDOCs", "Oracle Database Administrator Certified", "Data Enrichment", "SOA", "Informatica PowerCenter", "Google Data Analytics Professional Certificate", "OpenWRT", "CAPM", ".NET", "Bitbucket", "Design Calculations", ".NET Core 6.0", "JAX-WS", "DynamoDB", "Amazon Firehose", "VPC Design", "Mesh Networking", "Snow SQL", "Resin", "Gateway Aggregation", "Predictive Modeling", "Web IDE", "Mechanical Design", "MongoDB", "Web Application Automation", "BRF+", "Oracle Database (11g, 10g, 9i, 8x)", "Software Architecture Planning & Design", "Snowpark API", "Pega 8.4", "Microsoft Azure", "Team Foundation Server (TFS)", "Business Workflow", "CATIA", "Advanced Java Development", "Dev Studio", "Cosmos DB", "Oracle Data Guard", "SNS Event Producers", "Physical Database Design", "EF Core", "Salesforce APIs", "Data Flows", "Delivery Management", "Product Validation", "txText Control", "RDS", "NuGet", "Program Management", "PLA", "El<PERSON>", "Salesforce", "Firebase Cloud Services", "Multi-AZ", "RICEF", "ASP", "N-tier applications", "Enterprise Class Structure", "Spring IOC", "Procurement Processes", "Build.xml", "Azure Tables", "Time Series Forecasting", "PHP", "Azure App Services", "MCA", "<PERSON><PERSON><PERSON><PERSON> (implied)", "Inbound/Outbound Proxies", "Oracle GoldenGate (implied)", "Data Guard Snapshot Standby", "Oracle (PL/SQL)", "LeanFT", "Azure Blob Storage", "C", "Redis", "Record to Report (R2R)", "Amazon EC2", "Sitecore", "DFA", "Virtualization", "Swift", "Oracle Cloud Accounts Receivable", "REST APIs", "A<PERSON> (EKS)", "UG-NX", "MathCAD", "GitHub", "WPF", "App Studio", "802.11", "Maven <PERSON>", "EN ISO 14122", "Spring Framework", "Amazon S3", "Azure Active Directory", "SPDD", "AWS Lambdas", "Bug Reporting", "Angular 8", "Power Pivot", "SAP MM Functionalities", "Amazon SQS", "Normalization", "Manual Testing", "Stack Up Analysis", "ITIL V3 Foundation", "Selenium Grid", "SmartView", "Orchestration", "IICS", "Razor View Engine", "Document review", "Data Governance", "Amazon VPC", "Java", "Windows", "JDeveloper", "Spring Boot", "SAP Procurement Processes", "Object-Oriented ABAP (OOABAP)", "TDD", "Oracle 19c Database Administrator", "Repository Design Pattern", "Angular Reactive Forms", "Silverlight", "<PERSON><PERSON>", "Microsoft Power BI", "AWS Certified Solutions Architect - Associate", "Pivotal Cloud Foundry", "TortoiseSVN", "Azure Logic Apps", "SAP Certified Development Associate - SAP Fiori Application Developer", "OpenTelemetry", "HFM", "Amazon ECR (Elastic Container Registry)", "Database Performance Tuning", "AZ-900: Microsoft Azure Fundamentals", "<PERSON><PERSON><PERSON><PERSON>", "SVN Subversion", "TKPROF", "Domain Driven Design (DDD)", "Git", "Azure APIM", "Tracer", "Regression Analysis", "Clipboard", "Microsoft Azure Certified Professional", "Hive", "V<PERSON>am Backup and Recovery", "Power BI", "Scrum Master", "Visual Studio 2013", "FI-AP", ".NET Core 5/6/8", "JR<PERSON>el", "BADIs", "SAS Visual Investigator", "RTR", "Real-time Data Integration", "Lambda", "Star Schema", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "Decision Rules", "Summary-View", "Postman", "SAP UI5", "MS SQL Server", "FI-AR", "Selenium IDE", "Case Management", "Business Intelligence", "Visio", "DBT", "Test Strategy Creation", "SoapUI", "Functions", "Transportable Tablespaces", "IFRS (International Financial Reporting Standards)", "OCI - Oracle Cloud Infrastructure Foundations Associate", "Karate Framework", "WebLogic", "REST", "Subversion", "SAP Solution Design", "SolidWorks", "DAST", ".NET Framework 4.7", "FDMEE", "CI/CD", "OOPS", "Smart Forms", "Business Intelligence Publisher (BIP)", "PFTC", "SAP Certified Functional Consultant", "Domain Driven Design", "complex join queries", "DDD", "Enterprise Systems Integration", "Netezza", "AWS Lake Formation", "RMAN", "Java Mail", "MVC Design Pattern", "AI", ".NET Framework 4.0", ".NET Core", "IntelliJ IDEA", "Node.js", "iText", "Oracle Real Application Cluster", "Multi-tier Distributed Applications", "High Throughput System Architecture", "Blue-Green Deployment", "Hybrid Solution Architecture", "Visual Studio 2019", "Deep Learning", "Production Support (L3)", "Auto Scaling", "Micro-services", "Angular 7", "Groovy", "AG Grid", "HP", "UFT", "Putty", "Data Architecture", "Oracle", "Exploratory testing", "NoSQL", "Redux Toolkit", "Design Standardization", "Amazon Athena", "Scalability", "SWR", "JBehave", "Snyk", "Test Plan Development", "React Router", "Azure Queues", "Client-server applications", "RFCs", "Data Security", "PMO", "Data Processing", "Crontab", "Cypress", "RBAC", "XML Web Services", "<PERSON>", "AWS Certified SysOps Administrator Associate", "Load Strategizing", "SeeTest (Experitest)", "<PERSON><PERSON>", "AIF", "Containerization", "AZ-204", "Power Automate", "Resource Management", "Data Warehouse (DWH)", "CICS", "Insurance", "AKS (Azure Kubernetes Service)", "YAML Pipeline", "OAuth", "Service Bus", "EC2", "API", "Oracle Enterprise Manager Grid Control 11g", "Informatica Data Management Center (IDMC)", "Tridion CMS 2013", "Report Development", "Test Script Development", "JSP", "<PERSON><PERSON>", "Data Loader", "OLAP", "Sufi", "SQL Server 2000", "Blob Storage", "AGGrid", "HTML5", "JDK", "Streamset", "SQL Server 2005", "Kubernetes", "Mainframe Testing", "<PERSON><PERSON><PERSON>", "Amazon Elastic Beanstalk", "Terraform (IaC)", "Mobile Testing (iOS, Android)", "Cash Pooling", "STATSPACK", "Mechanical Component Design", "Team Foundation Server", "Drill Down", "Oracle Export/Import", "Toad", "Single Page Application (SPA)", "DevExpress", "Amazon ELB", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Disaster Recovery", "Component Localization", "Oracle Financial Accounting Hub", "SQLTrace", "Ad hoc Testing", "Waterfall Charts", "OCI Auto Scaling", "Oracle Financials Cloud", "OpenID Connect", "DataMart", "SAP Profit Center Accounting (PCBS)", "ISO/IEC 27001", "Bootstrap", "Confluent <PERSON><PERSON><PERSON>", "Oracle Cloud Cash Management", "Dashboarding", "Accounts Receivable", "SAP FI-GL Transactions", "Microsoft BI Stack", "SQL Server 2016", "YANG", "AWS Storage Services", "RICEF objects", "Azure Blueprints", "Amazon Web Services (S3, EC2, Lambda)", "Import/Export", "UI Testing", "Pa<PERSON><PERSON>", "Sankey Diagrams", "MicroStrategy", "Order to Cash (O2C)", ".NET Core 8.0", "HTTP", "Cobol", "SAP Sales and Distribution (SD)", "Oracle PL/SQL", "Snowpark", "Regulatory Reporting", "Hangfire", "Swagger", "Agile (SCRUM)", "Relational Databases", "Artificial Intelligence (AI)", "<PERSON><PERSON>", "Logic Apps", "BAPI", "Peoplesoft HCM", "Product Assortment Management", "Federated Database Design", "Quality Center", "Docker Registries", "Azure API Management", "SAP Material Master Data Management", "AKS", "Screen Flows", "Saga Pattern", "<PERSON><PERSON><PERSON>ber", "Financial Analysis", "Data Visualization", "Appium", "Abstract Factory Design Pattern", "File-Aid", "JDBC", "C# Programming Certified Professional", "Apache", "API Development (ICS, ICRT)", "<PERSON><PERSON>", ".NET MAUI", "SAP Profit Center Accounting (PCA)", "SOAP", "HTTP (TLS)", "GitHub Copilot", "Queue Processors", "SAP", "Peoplesoft Supply Chain Management", "Serverless Architecture", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "MS Azure", "Cisco Access Points", "MVVM", "AWS Certified Developer - Associate", "Key Performance Indicators (KPIs)", "Google Analytics", "Object Oriented Programming", "Web API", "JUnit", "Shell Scripting", "Visual Studio 2012", ".NET Framework 4.5", "SQL*Plus", "Visual Studio 2015", "Sheet Metal Design", "Build/Release Management", "PowerShell", "IBM BMP", "FinCrime", "Pega 8", "BPPs (Business Process Procedures) Documentation", "System Integration Testing", "IFRS", "Sanity", "OBIEE", "Classification", "JavaScript", "Vendor/Customer Open Items", "Amazon SNS", "Salesforce Platform Administrator", "J#", "TestNG", "Test effort estimation", "OUM Methodology", "Intelligent Data Management Cloud (IDMC)", "Order to Cash Management (OTC)", "<PERSON><PERSON>", "Web Testing", "Talwar Simulator", "ATOM (Mainframes/AS400 automation tool)", "Brainbench C# 5.0", "SOLID principles", "2D Drawing Review", "HP Service Manager (HPSM)", "App Services", "Test Case Execution", "Google Cloud Platform (GCP)", "MySQL", "Cash Management Integration", ".NET Framework", "GitLab", "Spark SQL", "Hyperion Financial Management (HFM)", "SAP Fiori Launchpad", "Angular 12", "E-commerce Architecture Design", "AIM Methodology", "HttpClient", "Design Patterns", "Accounts Payable (AP) Processing", "IBM Power E850", "Declarative Rules", "Data Validation", "OData", "POLARIS", "SEO", "Amazon IAM", "Prototype Model", "OIDC", "Data Guard Physical Standby", "I-Receivables", "<PERSON><PERSON><PERSON>", "Caliburn.Micro", "Peoplesoft FSCM", "Entity Framework 7.0", "API Management", "Step Functions", "Microsoft Certified Professional", "Repository Pattern", "Data Security and Compliance", "Export/Import", "EJB", "Servlets", "Kafka", "SAP Controlling (CO)", "AWS API Gateway", "Container Storage", "Ansible", "I-Expenses", "ITIL Foundation 2011", "Azure Log Analytics", "<PERSON>er", "CMMI Level 5", "SAP Accounts Payable (AP)", "OLTP", "Ruleset locking mechanisms", "Mantine UI", "Stored Procedures", "Data Capture (CDC)", "HPSM", "Domain Driven Development (DDD)", "Azure SQL", "International Financial Reporting Standards (IFRS)", "SAP Integration", "<PERSON><PERSON>", "Financial Reporting", "Pega 7.2.2", "ASP.NET", "Data Pipelines", "Pig", "Oracle Cloud I-Receivables", "Cisco Catalyst 9800 Wireless Controller", "LINQ to Objects", "Microsoft Excel Spreadsheets", "Data Migration", "Product locking", "Amazon Elastic Container Registry (ECR)", "Data Storage Strategies", "SSMS", "Security Groups", "SAST", "TCP", "Test Automation", "Ad-hoc Testing", "CloudFront", "Data Profiling", "Advanced Java Development (Certification)", "SAP S/4HANA", "WiFi", "OAuth Testing", "OTT", "Material Master Data Management", "Prince2", "SSRS", "CloudWatch", "Apache Tomcat", "RAP", "Flask", "Container-based Architecture Design", "Coded UI Testing", "ISO 27001", "Bamboo", "Windows Application Migration", "MyEclipse", "Sankey Charts", "Model-View-Controller (MVC)", "BottleRocket", "PCI", "AWS Aurora Postgres", "ISO27001 Compliance", "Logic App", "Pareto Charts", "Waterfall Methodology", "Oracle Cloud I-Expenses", "CentOS", "Subversion (SVN)", "Row-Level Security (RLS)", "Order Management", "Azure Pipelines", "Log4j", "Agents", "Project Management", "Project Planning", "F110 Automatic Payment Program", "Wix", "Choreography", "Azure SQL Database", "Selenium WebDriver", "AZ-304", "Indexing", "Amplify", "Oracle Enterprise Manager", "ALV Reports", "BTP", "j<PERSON><PERSON><PERSON>", "Spark", "SQS", "CSS", "Log Analytics", "EZTrieve", "Azure Data Factory", "SSO Testing", "iTech Sharp", "LambdaTest", "Cross-functional Collaboration", "Parameters", "Vendor/Customer Open Item Management", "Compatibility Testing", "Solution Architecture", "Package locking", "Apache ECharts", "Teradata Certified Administrator (V2R5)", "Continuous Integration/Continuous Deployment (CI/CD)", "Software Architecture", "Java Development (Certification)", "Test Plan Creation", "The Complete Python Developer", "Snowpipe", "JAX-RS", "Dimensions", "ECS", "SAP FI", "NoSQL Databases", "VPCs", "Drill Through", "Ruleset locking", "Rally", "Client Management", "BIP", "Salesforce Platform Developer (Certification - In process)", "JMS Hermes", "Windows Services", "NumPy", "Amazon CloudFormation", "@task", "Eclipse", "DNS Delegation", "Release Management", "Vite", "OCI AutoScaling", "Tibco", "JCL", "Elastic Beanstalk", "Unit testing", "Windows 2007/2008/2010", "Defect Tracking", "Oracle Enterprise Manager 12c", "Azure Data Lake", "5G", "Predictive Analysis", "Google Tag Manager", "SAP MM", "Purchase Order (PO) Management", "Data Transforms", "Ubus", "Technical Architecture", "AT&T Echo controller", "Financial Reporting Studio (FRS)", "OCI-Oracle Cloud Infrastructure Foundations Associate", "Red Hat Linux 8", "Continuous Integration", "Customer Exits", "AWS Aurora", "Object-Oriented Programming (OOP)", "Data Integration", "Oracle Financials Cloud Accounts Payable", "Cisco Aironet outdoor mesh access points", "Blazor", "Machinery Directive 2006/42/EC", "IBM LTO 9", "Regression", "MVC", "Visual Studio 2010", "TLS", "Materialized Views", "Oracle 8i/11i", "<PERSON>adata", "Account Payables", "SLA Development", "Pega Group Benefits Insurance Framework", "Encryption", "Visual Studio 2017", "Selenium", "<PERSON>", "CTI Testing", "Sparx Enterprise Architect", "AWS CloudWatch", "Nx Monorepo", "WAF", "VS Code", "Service Extension", "Flat Files", "ActiveMQ", "SAP SD", "Container-based Architecture", "AWS CloudFormation", "Android Testing", "Data Collection", "OCI CDN", "Cash Discount Management", "Mobile Application Automation", "Automatic Payment Program (F110)", "PySpark", "<PERSON><PERSON><PERSON>", "BrowserStack", "NUnit", "Business Process Documentation", "C#", "System Testing", "<PERSON>", "Elastic APM", "Microsoft Excel", "ADDM", "<PERSON>", "HP/IBM Power E980", "Database testing", "Amazon Lambda", "XML Parser", "<PERSON><PERSON>", "NodeJS", "Dimension Tables", "Red Hat Linux 7.x", "C++", "SAFe", "EKS", "YAML", "AireOS controller", "Amazon ECS", "Red Hat Linux", "Profit Center Accounting (PCBS)", "CVS", "App Insights", "Oracle 10g", "Advanced Planner Optimizer (APO)", "OAuth2", "Microservices", "Flow Actions", "AJAX", "Kali Linux", "SQL Server Data Tools", "React", "Visual Studio 2008", "Dependency Injection", "Struts 2", "ETL Processes", "Redux", "Power Query", "Agile Project Management", "JDK 8", "Certified SAP Functional Consultant", "Data Management", "System Architect", "AT interfaces", "Software Development Lifecycle (SDLC)", "ServiceNow", "Spring REST", "Blueprints", "Autonomous Data Warehouse (ADW)", "Azure Active Directory (Azure AD)", "GAAP", "ETL Design", "Informatica Cloud Services (IICS)", "PMP", "Amazon Web Services (AWS)", "Test Estimation", "RabbitMQ", "Oracle Database Administration", "SonarQube", "CE Marking", "Continuous Deployment", "Power BI Service", "PivotTables", "High-Throughput System Architecture", "Process Management", "Azure Kubernetes Service (AKS)", "Sass", "ETL testing", "PostgreSQL", "Inbound/Outbound Proxy", "Data Analysis", "Microservices testing", "Pega Product Builder", "<PERSON><PERSON><PERSON>", "JWT", "Summary-View Reports", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Harvest", "RDBMS", "C# 10.0", "SSRS (SQL Server Reporting Services)", "ASP.NET Core 8.0", "Database Migration", "Apache Kafka", "Next.js", "Bank Reconciliation", "Azure Redis <PERSON>ache", "Autonomous Transaction Processing (ATP)", "ICRT", "Stimulsoft", "Direct Connect", "Data Warehousing", "Amazon Web Services", "Data Transformation", "Azure Virtual Network", "Interactive Voice Response (IVR) testing", "WCF RESTful", "ClearCase", "Production Planning (PP)", "MySQL Aurora", "Mac filter configuration", "Enhancement Points", "SSIS", "<PERSON><PERSON>", "Billing", "SharePoint", "Azure DevOps", "Data Marts", "Waterfall Methodologies", "R", "Device Drivers", "DevOps", "SAP Cash Management (CM)", "Scalable architecture design", "FullStory", "E-commerce Architecture", "MudBlazor", "Generally Accepted Accounting Principles (GAAP)", "Django", "SQL Server 2012", "System Integration", "Data Wrangling", ".NET Framework 2.0", "Web Sites Design and Development", "Terada<PERSON>", "Linux Shell Scripting", "Salesforce Platform Developer", "Test Planning", "Red Hat Linux 7", "Exadata CS X9M-2", "High-Performance Architecture Design", "Data Dictionary (DDIC)", "SAP Security", "PL/SQL", "Oracle 11g", "Business Process Management (BPM)", "AR Processing & Reporting", "Oracle OCI", "Saga", "CQRS", "Supply Chain Management (SCM)", "Neb<PERSON>", "Predictive Forecasting", "JDK 17", "Oracle Fusion Applications", "Artificial Intelligence", "Oracle Autonomous Transaction Processing (ATP)", "Python Worksheet", "Azure Service Bus", "Web Dynpro ABAP", "Sigma", "Lake Formation", "gRPC", "Apache POI", "WAR file deployment", ".NET 6", "Microsoft technologies", "Data Masking", "Continuous Integration and Continuous Deployment (CI/CD)", "Server-Side Encryption (S3)", "Exadata CS DB X7", "AWS Certified SysOps Administrator - Associate", "Talwar controller", "Query Performance Optimization", "Data Modernization", "Selenium RC", "SSO", "Real-time Data Ingestion", "AMDP", "Cisco catalyst 3750 Switch", "Android Studio", "BPP Documentation", "Pega 7.3", "Oracle Cloud Fixed Assets", "Automation Testing", "Tridion CMS 8.5", "Oracle RAC", "Oracle Grid Control 11g", "Profit Center Accounting (PCA)", "Change Management", "Pega 8.4.1", "Exadata X9M-2", "Talend", "RESTful APIs", "User Interface Testing", "SAP Financial Accounting (FI)", "EXPLAIN PLAN", "XAML", "Test Scripting", "DFM", "Angular 8/10/12", "DBMS", "VPN", "Stakeholder Management", "HP ALM", "Mainframes testing", "Data Cleaning", "Amazon EKS", "Inhouse Cash Management", "Internet of Things (IoT)", "WebLogic 11g", "AWS SAM", "SAP Treasury Modules", "Oracle Transactional Business Intelligence (OTBI)", "DM", "Triggers", "Infrastructure as Code (IaC)", "Reverse Engineering", "ES6", "Pricing Strategies", "Requirements mapping", "macOS", "Red Hat Linux Enterprise 7", "MS Excel", "Component-Based Architecture", "DAX", "Query Optimization", "Factory Pattern", "Supply Chain Management", "Amazon Glue", "Hyperion FRS", "NETCONF", "OOAD", "QTP", "<PERSON><PERSON>", "Tridion CMS", "Service Lifting Tool Design", "Amazon CloudWatch", "Material UI", "IAM Role Management", "API testing", "Integration Testing", "AI-powered Automation", "iOS", "Cloud Design Patterns", "S3 (Amazon S3)", "Data Mining", "UML", "Real-time Data Analytics", "CSV", "Data Modeling (<PERSON> Schema, <PERSON><PERSON><PERSON>)", "Oracle 8i", "Data Modeling", "Service-Oriented Architecture (SOA)", "Snowf<PERSON>a", "RFID", "Observables", "Desktop Application Testing", "Big Data", "Oracle 9i", "WRICEF Documentation", "Oracle Data Guard (Active/Passive)", "OTBI", "Pega Marketing Consultant (Certification)", "End-to-End testing", "Regression Testing", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "SSAS", "<PERSON><PERSON>", "ITSM (Service Desk)", "Red Hat Linux Enterprise 8", "Amazon Route 53", "DNVGL", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "Test-Driven Development (TDD)", "VCN", "CAPWAP", "WCF", "Microservices Architecture", "<PERSON><PERSON><PERSON>", "Spiral Model", "Application Design", "<PERSON><PERSON>xing", "SQL Server 2017", "Fiori Elements", "ISO 20000", "2D Drawings Review", "MIS Management", "Azure Functions", "SAP Warehouse Management", "Azure Storage (Tables, Queues, Blobs)", "XML", "Dynatrace", "Azure Monitor", "Factory Design Pattern", "Dependency Inversion Principle", "Virtual Machines (VMs)", "Code Optimization", "SCM", "Azure API Management (APIM)", "Oracle 19c RAC", "AutoCAD", "<PERSON><PERSON>", "Tridion CMS 2011", "J2EE", "Prism", "WebIDE", "Hypothesis Testing", "IDoc", "Azure Application Insights", "Databricks", "Test Driven Development", "SQL Server", "Java Development", "Agile", "AWS (Amazon Web Services)", "LTE", "Test Scheduling", "Financial Data Management (FDMEE)", "BDC", "Oracle Financials Cloud Receivables", "Cloud Data Integration", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Circuit Breaker <PERSON>", "SAP CO", "Order-to-Delivery Process", "Python", "Column <PERSON>", "Procure to Pay (PTP)", "SAP AP/AR Transactions", "Microsoft Office", "Database Partitioning", "Scenario Assessment", "Data Structures", "Visual Studio Code", "IBM Infosphere DataStage", "Amazon Auto Scaling", "Spring Data", "CDS Views", "PSM", "Data Pump", "MS Unit Test", "Amazon EBS", "S3", "SQR 6.0", "ANT", "Artifactory", "IBM Power E980", "RESTful Web Services", "AWS Certified Solutions Architect Associate", "Test Driven Development (TDD)", "Data Gateway", "ANSYS", "Informatica 10.2", "VSAM", "ATC", "ShadCN UI", ".NET Core Apps", "IBM DB2", "Risk-based testing", "Oracle 21c", "SNS", "Visual Studio 2005", "Azure Service Fabric", "OCI VCN", "Data Mart", "GDB", "UDP", "Requirement Gathering", "IIS", "Bluetooth", "Crystal Reports", "Quartz", "C# 8.0", "<PERSON> (GL)", "Kendo UI", "UAT (User Acceptance Testing)", "DB2", "SQL Server 2008", "Source Code Management (SCM)", "Prototype", "Table Storage", "RACI Matrix", "System Architect (Certification)", "Dashboards", "Data Cleansing", "General <PERSON><PERSON>", "Object Storage", "Informatica Intelligent Cloud Services (IICS)", "Certified Professional Data Engineer", "Load Balancers", "Product Life Cycle Management (PLM)", "ASME Y14.5", "Enterprise Reporting", "Antifactory", "SiteMinder", "Oracle E-Business Suite R12", "OCI WAF", "Jersey REST", "Design and Analysis of Algorithms", "Data Intelligence", "PCBS", "Slowly Changing Dimensions (SCD Type 1, 2, 3)", "Entity Framework", "Snowsight", "List-View", "OCI Load Balancing", "SAP Materials Management (MM)", "Web services testing", "Process Flows", "Test Case Design", "User Acceptance testing", "Event Grid", "AWS Firehose", "SPAU", "TypeScript", "CA DevTest", "OpenSearch", "Ka<PERSON><PERSON>", "ASP.NET MVC", "SAP Best Practices", "JSON", "Cost Optimization", "IDMC", "JSTL", "AWR", "Demand Forecasting", "Query Plan Optimization", "Defect Management", "Procure to Pay (P2P)", "WebLogic 14c", "Database Migration Service", "E-Commerce", "GAAP (Generally Accepted Accounting Principles)", "<PERSON><PERSON>", "<PERSON><PERSON> (Project Management Professional)", "Test Management", "A<PERSON>os", "C# 9.0", "Strategic Planning", "AZ900: Microsoft Azure Fundamentals", "HP Quality Center", "ReactJS", "CI/CD Pipeline", "Multi-AZ VPC", "Blue Yonder", "Extended Warehouse Management (EWM)", "AWS Active Directory", "Alert Management", "Exadata CS DBX7", "Logility", "SSIS (SQL Server Integration Services)", "Tibco Messaging", "Oracle Allocations", "<PERSON><PERSON><PERSON>", "Real-time Data Analytics Solution Architecture", "Oracle DBA", "AWS Auto Scaling", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "Service Level Agreement (SLAs)", "AP/AR Transactions", "OCI Object Storage", "Mac filtering", "Oracle Cloud Accounts Payable", "M Language", "AWS Elastic Kubernetes Service (EKS)", "Coded UI", "OKTA", "Event Hub", "Linux", "User Acceptance Testing (UAT)", "Qualcomm SDX hardware", "Cloud Migration", "Informatica Data Management Cloud (IDMC)", "ASAP Methodology", "Single-page application (SPA)", "Amazon DynamoDB", "PFTC Roles", "Business Process Mapping", "AeroScout tags", "Cisco Prime Infrastructure", "Azure", "Data Quality", "Data Pipelining", "Data Analytics", "WinSCP", "Ethernet", "Strapi CMS", "Cloud Application Integration", "Functional Testing", "Amazon Lake Formation", "Unix", "ASP.NET Core 6.0", "Data & Analytics Architecture Modernization", "Webpack", "SEO Optimization", "Azure App Service", "Technical Architecture Documentation", "SmartSheet", "SAS Visual Analytics", "NestJS", "SNS Event Consumers", "Oracle 19c Database Administrator Training from Koenig Database Administration", "Oracle 19c", "Firehose", "Oracle 11i", "Azure Entra ID", "FI-GL Transactions", "IOS Testing", "Cost Analysis", "Test Execution", "Admin Studio", "Oracle Cloud", "Business 360 Console", "DWH", "SVN", "Web Application Testing", "Visual Studio 2003", "SAP Scripts", "ETL", "Order-to-Delivery Process Optimization", "<PERSON><PERSON><PERSON>", "Oracle 8x", "PII", "Angular", "Report Definitions", "IoT", "AWS CDK"], "skill_frequency": {"Python": 125, "JavaScript": 96, "Java": 106, "C#": 62, "SQL": 120, ".NET 6": 12, "ASP.NET Core": 35, "ASP.NET MVC": 32, "Angular": 91, "Web API": 50, "Azure": 66, "Azure Functions": 49, "Azure Developer": 12, "Azure Logic Apps": 12, "Azure Service Bus": 19, "Azure API Management": 1, "Azure Storage": 23, "Cosmos DB": 20, "Redis Cache": 12, "Azure Active Directory (Azure AD)": 4, "Azure Virtual Network": 1, "Azure Application Insights": 2, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 22, "Azure Container Registry": 12, "Azure Service Fabric": 12, "Azure Data Lake": 20, "YAML Pipelines": 7, "Docker": 74, "Kubernetes": 66, "CI/CD": 79, "Microservices": 49, "Serverless Architecture": 20, "HTML": 83, "CSS": 56, "jQuery": 69, "Event Grid": 12, "Event Hub": 12, "SQL Server": 75, "MySQL": 84, "Snowflake": 33, "T-SQL": 32, "PL/SQL": 41, "Stored Procedures": 41, "Triggers": 19, "Functions (Database)": 1, "Amazon Web Services (AWS)": 77, "Microsoft Azure": 33, "Agile Methodology": 17, "Design Patterns": 30, "Microservices Architecture": 13, "Federated Database Design": 9, "Container-based Architecture": 6, "High-Throughput System Architecture": 3, "Real-time Data Analytics Solution Architecture": 6, "E-commerce Architecture": 8, "Hybrid Solution Architecture": 9, "VPC Design": 5, "Direct Connect": 9, "VPN": 9, "Query Performance Optimization": 10, "Data Modeling": 62, "Microsoft Certified Professional": 2, "WPF": 9, "MVC": 12, "MS Azure": 6, "WCF": 39, "Blob Storage": 8, "Table Storage": 8, "App Services": 8, "Redis": 18, "App Insights": 8, "Azure APIM": 8, "Logic Apps": 16, "AZ900: Microsoft Azure Fundamentals": 8, "Caliburn.Micro": 9, "Prism": 9, "Entity Framework 7.0": 8, "XML Parser": 9, "LINQ": 26, "Stimulsoft": 9, "Angular Reactive Forms": 9, "HttpClient": 8, "NUnit": 19, "Coded UI Testing": 8, "ADO.NET": 38, "SQL Server Reporting Services (SSRS)": 12, "Strapi CMS": 8, "Windows Services": 15, "WCF RESTful": 9, "MS SQL Server 2019": 4, "PostgreSQL": 70, "SQLite": 9, "Oracle (PL/SQL)": 4, "MS Access": 18, "InstallShield": 9, "GitHub": 63, "TFS": 28, "SVN": 52, "IIS": 9, "Apache Tomcat": 9, "DevExpress": 9, "Brainbench C# 5.0": 8, "MVVM": 3, "ASP.NET": 33, ".NET Framework": 19, "Entity Framework": 29, "EF Core": 10, "Razor View Engine": 10, "Bootstrap": 56, "CosmosDB": 10, "ElasticSearch": 29, "Apache Kafka": 36, "ActiveMQ": 10, "Pivotal Cloud Foundry": 10, "Azure App Service": 17, "Node.js": 60, "React": 42, "OAuth2": 7, "Swagger": 19, "OOPS": 10, "SOLID principles": 20, "Team Foundation Server (TFS)": 11, "Git": 83, "Jira": 106, "Azure DevOps": 69, "Moq": 10, "Agile Methodologies": 14, "SCRUM": 42, "Waterfall Methodologies": 9, "Test-Driven Development (TDD)": 16, "C++": 29, "Service Bus": 11, "API Management": 11, "YAML Pipeline": 5, "Azure AD": 10, "Virtual Network": 12, "Application Insights": 11, "Log Analytics": 11, "Key Vault": 11, "Functions": 16, "Software Architecture": 4, "Micro-services": 3, "High Throughput System Architecture": 4, "Microsoft Azure Certified Professional": 4, "MCA": 2, "Open API": 9, "C": 26, ".NET Core": 24, "AJAX": 28, "AngularJS": 10, "ReactJS": 30, "XML": 43, "HTML5": 34, "Sass": 10, "TypeScript": 61, "JSON": 63, "DynamoDB": 30, "OpenSearch": 10, "EC2": 31, "CloudFront": 10, "IAM": 29, "ECS": 10, "SQS": 24, "SNS": 25, "Lambda": 15, "API Gateway": 31, "RDS": 10, "CloudWatch": 31, "Step Functions": 16, "Elastic Cache": 9, "NodeJS": 10, "AGGrid": 7, "txText Control": 10, "ASPX": 10, "SOAP": 62, "RESTful APIs": 6, "Crystal Reports": 19, "Active Reports": 10, "SSRS": 31, "SSIS": 30, "YAML": 8, "Terraform": 29, "DDD": 1, "TDD": 1, "Agile": 95, "NuGet": 8, "Object-Oriented Programming (OOP)": 13, "VB.NET": 9, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 9, "OpenTelemetry": 10, "FullStory": 10, "Google Analytics": 20, "ASP.NET Core 6.0": 6, "ASP.NET Core 8.0": 6, ".NET MAUI": 10, "XAML": 10, "C# 8.0": 6, "C# 9.0": 6, "C# 10.0": 6, "Web Services": 10, "REST": 35, "Angular 7": 6, "Angular 8": 6, "Angular 9": 6, "Angular 10": 6, "Angular 12": 6, "Material Design": 10, ".NET Framework 2.0": 6, ".NET Framework 3.5": 6, ".NET Framework 4.0": 6, ".NET Framework 4.5": 6, ".NET Framework 4.7": 11, "CI/CD Pipeline": 10, "Splunk": 28, "RabbitMQ": 10, "Amazon DynamoDB": 19, "Kendo UI": 18, "Amazon EC2": 40, "AWS Lambda": 32, "Azure App Services": 12, "WebJobs": 1, "Azure Active Directory": 8, "ServiceNow": 23, "HP Service Manager (HPSM)": 6, "Service-Oriented Architecture (SOA)": 6, "OAuth 2.0": 13, "OKTA": 18, "Azure Entra ID": 10, "Bitbucket": 35, "Team Foundation Server": 9, "Subversion (SVN)": 14, "TortoiseSVN": 13, "Visual Studio 2003": 6, "Visual Studio 2005": 6, "Visual Studio 2008": 6, "Visual Studio 2010": 6, "Visual Studio 2012": 6, "Visual Studio 2013": 6, "Visual Studio 2015": 6, "Visual Studio 2017": 6, "Visual Studio 2019": 6, "Visual Studio 2022": 6, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 11, "OpenID Connect": 2, "Ping Identity": 2, "Salesforce APIs": 2, "CQRS": 13, "Saga Pattern": 9, "Choreography Pattern": 6, "Gateway Aggregation": 5, "Circuit Breaker Pattern": 9, "Message Queue": 2, "MuleSoft": 10, "Kafka": 24, "Tibco": 9, "AKS (Azure Kubernetes Service)": 6, "MVC Design Pattern": 7, "Repository Pattern": 7, "Dependency Inversion Principle": 9, "Dependency Injection": 9, "Factory Pattern": 7, "Abstract Factory Pattern": 7, "Tridion CMS 2009": 6, "Tridion CMS 2011": 6, "Tridion CMS 2013": 6, "Tridion CMS 8.5": 6, "Sitecore": 10, "SEO Optimization": 9, "Omniture": 10, "Google Tag Manager": 10, "SQL Server 2000": 6, "SQL Server 2005": 6, "SQL Server 2008": 6, "SQL Server 2012": 6, "SQL Server 2014": 6, "SQL Server 2017": 6, "Azure SQL Server": 4, "Oracle PL/SQL": 21, "Selenium": 10, "Azure Data Factory": 10, "PMP (Project Management Professional)": 7, "Agile (SCRUM)": 8, "Kanban": 18, "AZ-104": 9, "AZ-204": 9, "AZ-304": 9, "Machine Learning": 21, "Deep Learning": 9, "Predictive Analysis": 9, "Artificial Intelligence": 8, "IoT Systems": 1, ".NET": 18, "gRPC": 8, "SSIS (SQL Server Integration Services)": 8, "SSRS (SQL Server Reporting Services)": 3, "LINQ to SQL": 7, "LINQ to Objects": 7, "Lambda Expressions": 9, "S3 (Amazon S3)": 2, "Amazon Elastic Kubernetes Service (EKS)": 5, "Amazon ECR (Elastic Container Registry)": 2, "Elastic Beanstalk": 16, "Application Load Balancer": 6, "NoSQL": 9, "Datadog": 8, "Azure Container Registry (ACR)": 8, "Azure Kubernetes Service (AKS)": 7, "Azure Blob Storage": 18, "Blazor": 8, "MudBlazor": 8, "Telerik": 8, "Redux": 8, "Hangfire": 8, "ADFS (Active Directory Federation Services)": 3, "Tableau": 56, "DB2": 29, "SAP": 17, "IDoc": 8, "Logility": 6, "Blue Yonder": 6, "CloudFormation": 10, "VPC": 10, "Jenkins": 77, "SonarQube": 18, "Antifactory": 8, "AWS Elastic Kubernetes Service (EKS)": 9, "ANT": 34, "Maven": 71, "Shell Scripting": 22, "Ansible": 18, "PowerShell": 19, "Tomcat": 42, "JBoss": 25, "WebLogic": 27, "WebSphere": 24, "Windows Server": 9, "Red Hat Linux": 6, "Unix": 22, "CentOS": 6, "VMware": 8, "Elastic Load Balancers": 7, "Waterfall": 45, "Batch Scripting": 7, "Amazon ECS": 9, "Amazon S3": 36, "Amazon EBS": 9, "Amazon VPC": 9, "Amazon ELB": 9, "Amazon SNS": 11, "Amazon RDS": 9, "Amazon IAM": 9, "Amazon Route 53": 9, "AWS CloudFormation": 13, "AWS Auto Scaling": 4, "Amazon CloudFront": 9, "Amazon CloudWatch": 10, "AWS CLI": 8, "Vault": 9, "Docker Hub": 9, "Docker Registries": 8, "AWS Kops (EKS)": 6, "Groovy": 9, "GitLab": 27, "Apache": 3, "Grafana": 9, "Pivotal Cloud Foundry (PCF)": 9, "Infrastructure as Code (IaC)": 9, "Configuration Management": 17, "Containerization": 9, "Orchestration": 9, "Build/Release Management": 9, "Source Code Management (SCM)": 5, "HTTP (TLS)": 2, "Key Management": 1, "Encryption": 1, "J2EE": 20, "SAFe": 9, "Confluence": 23, "Microsoft Project": 8, "SmartSheet": 9, "DevOps": 9, "Warehouse Management": 9, "CMMI Level 5": 16, "PMP": 10, "PSM": 7, "Agile Project Management": 8, "Scrum Master": 8, "Program Management": 17, "Project Management": 16, "Project Planning": 15, "Risk Management": 17, "Cost Analysis": 16, "Resource Management": 8, "Stakeholder Management": 9, "Delivery Management": 8, "Client Management": 8, "Release Management": 9, "Microsoft Excel": 11, "Azure Cloud": 19, "Cobol": 21, "Ezetrieves": 5, "IBM BMP": 8, "ISO 27001": 7, "DBT": 7, "AWS": 27, "Azure Data Factory (ADF)": 7, "Databricks": 14, "Database Migration Service": 7, "AWS Glue": 22, "Fivetran": 7, "Snow SQL": 7, "Streamset": 7, "Snowpark": 7, "Column Masking": 6, "Data Encryption": 6, "Data Decryption": 6, "Data Masking": 6, "Data Governance": 8, "Hive": 14, "Pig": 7, "Sqoop": 7, "PySpark": 9, "Sigma": 7, "Apache Airflow": 7, "Informatica Power Center": 2, "Talend": 16, "Peoplesoft FSCM": 5, "Peoplesoft HCM": 6, "Oracle": 35, "MS SQL Server": 16, "OLTP": 15, "OLAP": 15, "Data Warehousing": 45, "Data Architecture": 4, "Data Integration": 19, "ELT": 5, "ETL": 35, "Data Quality": 22, "Real-time Data Ingestion": 6, "Snow Pipe": 5, "Confluent Kafka": 6, "Snowsight": 7, "SQR 6.0": 3, "Avro": 5, "Parquet": 5, "CSV": 10, "Index Design": 7, "Query Plan Optimization": 6, "Data Analysis": 15, "Business Intelligence": 18, "Data Management": 7, "ETL Processes": 8, "Excel": 11, "Power BI": 33, "DAX": 17, "Statistical Analysis": 7, "Regression": 7, "Hypothesis Testing": 8, "Predictive Modeling": 8, "Time Series Forecasting": 8, "Classification": 8, "Data Cleaning": 8, "Data Transformation": 19, "Data Automation": 8, "PivotTables": 8, "Power Query": 17, "Pandas": 8, "NumPy": 8, "SQL Server Integration Services (SSIS)": 7, "R": 8, "Google Data Analytics Professional Certificate": 8, "Getting Started with Power BI": 8, "The Complete Python Developer": 8, "ISTQB Certified Tester Foundation Level": 8, "Informatica PowerCenter": 20, "IICS": 2, "IDMC": 2, "IBM Infosphere DataStage": 8, "SAS Data Integration Studio": 8, "Oracle 11g": 14, "Oracle 10g": 14, "Oracle 9i": 6, "Oracle 8x": 5, "Microsoft SQL Server": 8, "Amazon Redshift": 8, "Data Migration": 11, "Data Modernization": 8, "Data Enrichment": 7, "Data Validation": 13, "Data Processing": 7, "Data Pipelining": 8, "Data Visualization": 13, "Enterprise Reporting": 7, "Dashboarding": 3, "AWS Athena": 9, "AWS Lake Formation": 5, "Microsoft Power BI": 8, "OBIEE": 8, "SAS Visual Investigator": 8, "SAS Visual Analytics": 8, "Erwin Data Modeler": 16, "Sparx Enterprise Architect": 8, "RDBMS": 15, "Star Schema": 23, "Snowflake Schema": 23, "Slowly Changing Dimensions (SCD)": 7, "Normalization": 8, "Flat Files": 5, "Predictive Forecasting": 4, "Alert Management": 4, "Regulatory Reporting": 4, "AML Compliance": 2, "Data Intelligence": 6, "Scenario Assessment": 2, "MIS Management": 5, "MS Excel": 6, "Data Security": 7, "Data Wrangling": 8, "Visual Studio": 12, "Mechanical Product Design": 8, "Mechanical Component Design": 2, "System Integration": 8, "Sheet Metal Design": 8, "Machined Parts Design": 8, "Design Standardization": 8, "Component Localization": 8, "Cost Optimization": 8, "Design Calculations": 8, "Cross-functional Collaboration": 7, "Onshore Rigging Calculations": 8, "Service Lifting Tool Design": 6, "Process Management": 8, "UG-NX": 8, "SolidWorks": 8, "CATIA": 8, "AutoCAD": 8, "ANSYS": 8, "Design FMEA": 8, "DFM": 8, "DFA": 8, "GD&T": 8, "Stack Up Analysis": 8, "ASME Y14.5": 8, "2D Drawing Review": 3, "MathCAD": 8, "CE Marking": 8, "DNVGL": 8, "EN-13155": 8, "Machinery Directive 2006/42/EC": 8, "EN ISO 50308": 8, "EN ISO 14122": 8, "Reverse Engineering": 8, "Informatica Cloud Services (IICS)": 7, "Intelligent Data Management Cloud (IDMC)": 7, "Netezza": 7, "Teradata": 9, "Windows": 24, "Spark": 7, "Data Profiling": 7, "Business 360 Console": 6, "Cloud Data Governance": 7, "Cloud Data Catalog": 5, "Data Marts": 3, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 7, "API": 4, "ICS": 4, "ICRT": 4, "Nifi": 7, "Technical Design Documentation": 3, "Technical Architecture Documentation": 3, "Production Support": 11, "Code Review": 11, "Redux Toolkit": 8, "Axios": 8, "SWR": 8, "Formik": 8, "React Router": 8, "CSS3": 16, "ES6": 5, "Material UI": 8, "Tailwind CSS": 8, "PHP": 8, "Amazon Lambda": 9, "Gulp": 8, "Grunt": 8, "Webpack": 8, "GitHub Copilot": 6, "JWT": 8, "RBAC": 7, "Software Development Life Cycle (SDLC)": 2, "Spring Boot": 33, "Struts 2": 7, "Spring IOC": 7, "Spring MVC": 15, "Spring Data": 7, "Spring REST": 7, "Jersey REST": 7, "JSF": 7, "Apache POI": 7, "iText": 7, "Servlets": 16, "JSP": 16, "JDBC": 16, "JAX-WS": 7, "JAX-RS": 7, "Java Mail": 7, "JMS": 16, "JUnits": 5, "IBM MQ": 7, "Amazon EKS": 10, "Cucumber": 16, "Cypress": 16, "Dojo Toolkit": 5, "MongoDB": 24, "Quartz": 7, "Hibernate": 15, "Spring JPA": 7, "Putty": 7, "WinSCP": 7, "Bamboo": 7, "AWS Aurora Postgres": 6, "EJB": 9, "JSTL": 9, "JPA": 9, "Struts": 9, "Spring Framework": 7, "NestJS": 17, "WebLogic 11g": 7, "GlassFish": 17, "Resin": 9, "Oracle 11g/12c": 7, "IBM DB2": 9, "Eclipse": 9, "NetBeans": 9, "JDeveloper": 9, "IntelliJ": 8, "MyEclipse": 9, "VS Code": 9, "Toad": 27, "Visio": 9, "UML": 9, "CVS": 9, "SoapUI": 18, "JMS Hermes": 9, "JUnit": 48, "Log4j": 9, "JRockit Mission Control": 9, "JMeter": 9, "JRebel": 9, "Spiral": 8, "Prototype": 8, "Google Cloud Platform (GCP)": 17, "ITIL Foundation 2011": 2, "AWS Certified Solutions Architect Associate": 7, "AWS Certified Developer Associate": 7, "AWS Certified SysOps Administrator Associate": 7, "Dynatrace": 9, "LDAP": 9, "SiteMinder": 9, "SAML": 9, "Harvest": 9, "Nx Monorepo": 9, "OOAD": 7, "SOA": 11, "Single Page Application (SPA)": 6, "AWS CDK": 19, "@task": 9, "GitLab Pipelines": 4, "Oracle DBA": 5, "Oracle OCI": 5, "Oracle 19c": 8, "Oracle 12c": 8, "Oracle 21c": 8, "Oracle RAC": 5, "Oracle Data Guard": 9, "Oracle Enterprise Manager": 5, "Oracle TDE": 8, "Data Pump": 8, "Oracle Cloud Infrastructure": 4, "RMAN": 8, "Linux Shell Scripting": 8, "Crontab": 8, "AWR": 8, "ADDM": 8, "EXPLAIN PLAN": 8, "SQL*Trace": 4, "TKPROF": 8, "STATSPACK": 8, "WebLogic 14c": 8, "WebLogic 12c": 8, "JDK": 6, "SQL Server 2016": 11, "Veeam Backup and Recovery": 8, "Red Hat Linux 7": 5, "Red Hat Linux 8": 5, "Exadata": 8, "IBM LTO 9": 8, "IBM LTO 8": 8, "OCI IAM": 7, "OCI VCN": 7, "OCI Object Storage": 7, "OCI Load Balancing": 7, "OCI Auto Scaling": 5, "OCI CDN": 7, "OCI WAF": 7, "Autonomous Data Warehouse (ADW)": 7, "Autonomous Transaction Processing (ATP)": 7, "ITIL V3 Foundation": 8, "Prince2": 8, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 6, "Oracle 19c Database Administrator Training": 2, "Teradata Certified Administrator (V2R5)": 8, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 8, "Oracle E-Business Suite R12": 9, "Oracle Cloud Financials": 7, "Oracle Cloud General Ledger": 4, "Oracle Cloud Accounts Payable": 4, "Oracle Cloud Accounts Receivable": 4, "Oracle Cloud Fixed Assets": 4, "Oracle Cloud Cash Management": 4, "Oracle Cloud I-Expenses": 4, "Oracle Cloud Budgetary Control": 9, "Oracle Financial Accounting Hub": 9, "Oracle Transactional Business Intelligence (OTBI)": 8, "Financial Reporting Studio (FRS)": 8, "Smart View": 8, "Data Loader": 9, "Hyperion FRS": 9, "Business Process Management (BPM)": 8, "AIM Methodology": 9, "OUM Methodology": 9, "Sub Ledger Accounting (SLA)": 7, "Windows 2007/2008/2010": 9, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 9, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 9, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 9, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 9, "BIP": 8, "Pega Rules Process Engine": 8, "Pega Group Benefits Insurance Framework": 8, "Pega Product Builder": 5, "Pega 7.2.2": 8, "Pega 7.3": 8, "Pega 7.4": 8, "Pega 8": 8, "Unit testing": 12, "Harness": 8, "Sections": 8, "Flow Actions": 8, "List-View": 8, "Summary-View Reports": 7, "Report Definitions": 8, "Clipboard": 8, "Tracer": 8, "PLA": 8, "Product locking": 4, "Package locking": 4, "Ruleset locking": 4, "SDLC": 12, "E-Commerce": 8, "Insurance": 8, "Agents": 7, "Queue Processors": 7, "Decision Rules": 7, "Declarative Rules": 7, "Application Design": 8, "Case Management": 8, "Process Flows": 8, "Screen Flows": 8, "Data Transforms": 8, "Activities": 8, "Rule Resolution": 8, "Enterprise Class Structure": 7, "Dev Studio": 8, "App Studio": 8, "Admin Studio": 8, "CDH": 8, "Document review": 8, "Pega Marketing Consultant": 2, "Senior System Architect": 2, "System Architect": 2, "Postman": 27, "Selenium IDE": 9, "Selenium RC": 9, "Selenium WebDriver": 27, "Selenium Grid": 9, "TestNG": 27, "QTP": 9, "Gherkin": 9, "Ruby": 17, "Tortoise SVN": 6, "HP Quality Center": 9, "SeeTest (Experitest)": 3, "ACCELQ": 9, "JBehave": 9, "HP ALM": 15, "BrowserStack": 9, "LambdaTest": 9, "Functional Testing": 17, "Smoke Testing": 9, "System Testing": 17, "Integration Testing": 20, "Regression Testing": 17, "User Acceptance Testing (UAT)": 8, "UI Testing": 15, "Mobile Testing": 16, "Automation Testing": 10, "Web Testing": 9, "Compatibility Testing": 9, "Sanity Testing": 9, "Ad hoc Testing": 8, "Test Case Design": 8, "Test Plan Creation": 12, "Test Scripting": 1, "Test Execution": 6, "Defect Tracking": 7, "Bug Reporting": 5, "Test Management": 11, "AI-powered Automation": 7, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 6, "Android Testing": 6, "SSAS": 9, "Power BI Desktop": 9, "Power BI Service": 9, "M Language": 9, "Dimensional Modeling": 9, "Microsoft BI Stack": 9, "Power Pivot": 9, "Data Gateway": 9, "Row-Level Security (RLS)": 8, "Data Flows": 8, "DataMart": 6, "Power Automate": 9, "Visual Studio Code": 9, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 9, "ASAP Methodology": 9, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 9, "Business Process Mapping": 9, "FIT-GAP Analysis": 9, "Financial Reporting": 9, "SAP Solution Design": 9, "SAP Warehouse Management": 9, "Material Master Data Management": 7, "Procurement Processes": 7, "Order-to-Delivery Process": 6, "Demand Forecasting": 9, "Cash Pooling": 9, "Bank Reconciliation": 8, "F110 Automatic Payment Program": 3, "Real-time Cash Visibility System": 9, "Inhouse Cash Management": 9, "SAP Best Practices": 8, "Generally Accepted Accounting Principles (GAAP)": 5, "International Financial Reporting Standards (IFRS)": 6, "Financial Analysis": 8, "Automated Data Entry": 3, "AR Processing & Reporting": 3, "Customer Accounting": 3, "Vendor/Customer Open Items": 2, "SAP Integration": 1, "SAP S/4HANA": 8, "ABAP": 8, "OData": 8, "SAP UI5": 8, "Fiori": 8, "Fiori Elements": 8, "PI/PO": 8, "AIF": 8, "BRF+": 8, "Business Workflow": 8, "CRM": 8, "Web Dynpro ABAP": 8, "RAP": 8, "BTP": 8, "CAPM": 8, "Procure to Pay (PTP)": 8, "Order to Cash Management (OTC)": 8, "Production Planning (PP)": 8, "Quality Management (QM)": 8, "FI-AP": 8, "FI-AR": 8, "FI-GL (FICO)": 8, "RTR": 8, "SCM": 10, "Product Life Cycle Management (PLM)": 8, "Advanced Planner Optimizer (APO)": 8, "Extended Warehouse Management (EWM)": 8, "Data Dictionary (DDIC)": 8, "Module Pool Programming": 8, "Object-Oriented ABAP (OOABAP)": 8, "RFCs": 8, "BADIs": 8, "BDC": 8, "BAPI": 8, "BP Integrations": 8, "Enhancement Points": 8, "User Exits": 8, "Customer Exits": 8, "ALE IDOCs": 8, "Inbound/Outbound Proxy": 2, "SAP NetWeaver Gateway": 8, "Service Registration": 8, "Service Extension": 8, "CDS Views": 8, "AMDP": 8, "SAP Fiori List Report Application": 7, "Web IDE": 6, "BSP": 8, "SAP Fiori Launchpad": 8, "SAP UI5 Framework": 2, "Business Objects (BO)": 8, "ATC": 8, "SPDD": 8, "SPAU": 8, "SAP Security": 8, "PFTC": 6, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 8, "SAP Certified Development Associate - SAP Fiori Application Developer": 8, "Next.js": 8, "REST APIs": 24, "GraphQL APIs": 8, "AWS SAM": 8, "Apache ECharts": 8, "Cognito": 8, "OIDC": 8, "Mantine UI": 8, "Vite": 8, "MySQL Aurora": 8, "AWS API Gateway": 2, "Styled Components": 8, "Sanity": 8, "Amplify": 8, "ShadCN UI": 8, "Salesforce": 8, "CDL": 8, "Cisco Catalyst 9800 Wireless Controller": 9, "Talwar controller": 9, "AireOS controller": 9, "Cisco Access Points": 9, "Talwar Simulator": 9, "WiFi": 9, "802.11": 9, "WLAN": 9, "Ethernet": 9, "IP": 9, "TCP": 9, "UDP": 9, "CAPWAP": 9, "NETCONF": 9, "YANG": 9, "Swift": 9, "ClearCase": 9, "Cisco catalyst 3750 Switch": 9, "ios-xe asr 1K router": 9, "OpenWRT": 9, "Linux": 23, "QMI": 9, "AT interfaces": 9, "Ubus": 8, "Qualcomm SDX hardware": 9, "AT&T Echo controller": 7, "POLARIS": 9, "GDB": 9, "Gre": 9, "RFID": 9, "AeroScout tags": 9, "Cisco Aironet outdoor mesh access points": 7, "Cisco Prime Infrastructure": 8, "Mac filtering": 1, "Bash": 10, "Android App Development": 10, "Flask": 10, "Django": 10, "GraphQL": 10, "Amazon Web Services": 9, "macOS": 10, "Kali Linux": 10, "OAuth": 18, "AWS Certified Solutions Architect - Associate": 11, "Amazon SQS": 2, "Amazon Athena": 3, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 12, "Test Driven Development (TDD)": 11, "Mockito": 16, "Spark SQL": 8, "Server-Side Encryption": 7, "IAM Role Management": 6, "EKS": 7, "BottleRocket": 8, "React.js": 8, "Firebase Cloud Services": 8, "Cassandra": 8, "Android Studio": 8, "Bluetooth": 8, "Java Development": 6, "Advanced Java Development": 6, "Salesforce Platform Administrator": 6, "Salesforce Platform Developer": 6, "Appium": 9, "Perfecto": 9, "SeeTest": 9, "REST Assured": 9, "Karate Framework": 9, "UFT": 9, "LeanFT": 9, "Zephyr": 6, "Quality Center": 6, "Informatica 10.2": 9, "MicroStrategy": 9, "CICS": 9, "JCL": 9, "VSAM": 9, "Sufi": 9, "File-Aid": 9, "CA DevTest": 9, "ATOM": 4, "GCP": 8, "SSO": 8, "Test Strategy": 1, "Test Design": 4, "Test effort estimation": 2, "Requirements mapping": 4, "Risk-based testing": 8, "End-to-End testing": 8, "User Acceptance testing": 9, "Database testing": 9, "API testing": 8, "Web services testing": 8, "Microservices testing": 7, "Browser compatibility testing": 8, "Exploratory testing": 8, "ETL testing": 7, "Data Warehouse testing": 9, "Interactive Voice Response (IVR) testing": 7, "Customer Telephony Integration (CTI) testing": 7, "Mainframes testing": 5, "Service Virtualization": 8, "Continuous Integration and Continuous Deployment (CI/CD)": 3, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 4, "Oracle Financials Cloud Accounts Payable": 4, "Accounts Payable": 5, "Accounts Receivable": 5, "General Ledger": 5, "Fixed Assets": 5, "Cash Management": 6, "I-Expenses": 5, "I-Receivables": 5, "Order Management": 5, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 4, "Order to Cash (O2C)": 4, "Record to Report (R2R)": 4, "Oracle Allocations": 3, "Oracle Cloud": 1, "Intercompany": 6, "SeeTest/Experitest": 6, "Test Case Development": 1, "Test Schedule Creation": 3, "SAP Financial Accounting (FI)": 8, "SAP Controlling (CO)": 8, "SAP Sales and Distribution (SD)": 8, "SAP Materials Management (MM)": 8, "Hyperion Financial Management (HFM)": 8, "Financial Data Management (FDMEE)": 8, "Profit Center Accounting (PCA)": 2, "SAP Accounts Receivable (AR)": 2, "SAP Accounts Payable (AP)": 2, "General Ledger (GL)": 4, "Purchase Order (PO) Management": 1, "Inventory Planning": 6, "Automatic Payment Program (F110)": 6, "Network Security": 4, "Object Oriented Programming": 4, "Operating Systems": 4, "Design and Analysis of Algorithms": 4, "DBMS": 4, "Mainframe Testing": 4, "Performance Testing": 3, "User Interface Testing": 7, "Manual Testing": 1, "Mobile Web Testing": 5, "Desktop Application Testing": 2, "Web Application Testing": 1, "Data Analytics": 7, "Real-time Data Analytics": 2, "NoSQL Databases": 3, "Blueprints": 2, "Test Case Execution": 3, "Custom Visuals (Power BI)": 5, "Drill Down": 5, "Drill Through": 5, "Parameters": 5, "Cascading Filters": 5, "Interactive Dashboards": 2, "Reports": 2, "SAP Profit Center Accounting (PCA)": 1, "Automated Bank Reconciliation": 1, "Pricing Strategies": 4, "SAP MM Functionalities": 4, "Business Process Optimization": 1, "IVR Testing": 1, "CTI Testing": 1, "Continuous Integration": 4, "Continuous Deployment": 3, "High-Performance Architecture Design": 4, "Container-based Architecture Design": 3, "High Throughput System Architecture Design": 2, "Real-Time Data Analytics Solution Architecture Design": 2, "E-commerce Architecture Design": 2, "Microsoft technologies": 6, "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": 1, "Coded UI": 1, "MS SharePoint Server": 4, ".NET Core 6.0": 5, ".NET Core 8.0": 5, "XML Web Services": 7, ".NET Core Apps": 1, "Domain Driven Design (DDD)": 5, "Web Jobs": 9, "Model-View-Controller (MVC)": 1, "Tridion CMS": 4, "Internet of Things (IoT)": 6, "Azure SQL": 5, "Azure Pipelines": 5, "Rally": 5, "Multi-AZ": 2, "High Availability": 5, "Disaster Recovery": 3, "AWS-Kops (EKS)": 3, "HTTP": 8, "TLS": 7, "Windows Application Migration": 1, "OTT": 3, "RACI Matrix": 1, "S3": 7, "Performance Tuning": 4, "Query Optimization": 4, "Informatica Intelligent Cloud Services (IICS)": 6, "Informatica Data Management Center (IDMC)": 3, "Amazon Lake Formation": 2, "Cloud Migration": 6, "Nebula": 7, "Advanced Analytics": 5, "Data Compliance": 2, "Key Performance Indicators (KPIs)": 1, "Service Level Agreement (SLAs)": 1, "Data Flow Architectures": 3, "Data Collection": 2, "Data Storage Strategies": 2, "Agile Transformation": 1, "ISO27001 Compliance": 1, "Mechanical Design": 2, "Service Lifting Tools Design": 2, "2D Drawings Review": 5, "Unix Shell Scripting": 3, "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": 6, "Technical Design": 3, "Technical Architecture": 3, "Big Data": 5, "Real-time Data Integration": 2, "Amazon Web Services (S3, EC2, Lambda)": 3, "Silverlight": 3, "ITIL Foundation": 7, "AWS Certified Developer - Associate": 2, "AWS Certified SysOps Administrator - Associate": 2, "Oracle 19c Data Guard": 3, "Oracle 19c RAC": 3, "Red Hat Linux Enterprise 8": 2, "Red Hat Linux Enterprise 7": 2, "Oracle Enterprise Manager 12c": 6, "Oracle Enterprise Manager Grid Control 11g": 1, "Oracle Export/Import": 1, "Transportable Tablespaces": 3, "SQLTrace": 4, "Oracle Real Application Cluster": 1, "Windows Server 2016": 6, "Fault Tolerance": 2, "Scalability": 2, "Virtualization": 2, "Oracle Autonomous Data Warehouse (ADW)": 1, "Oracle Autonomous Transaction Processing (ATP)": 1, "VCN": 1, "Object Storage": 1, "Load Balancing": 1, "Auto Scaling": 1, "CDN": 1, "WAF": 1, "Exadata X9M-2": 6, "HP": 1, "IBM Power E980": 1, "IBM Power E850": 2, "OCI-Oracle Cloud Infrastructure Foundations Associate": 5, "OCA-Oracle Database Administrator Certified": 2, "Oracle 19c Database Administrator": 1, "ISO/IEC 27001": 3, "ISO 20000": 3, "ISO 27000": 1, "Pega Marketing Consultant (Certification)": 6, "Senior System Architect (Certification)": 6, "System Architect (Certification)": 6, "RICEF": 3, "SAP Script": 3, "Smart Forms": 5, "Adobe Forms": 5, "ALV Reports": 5, "Mac filter configuration": 2, "Athena": 4, "JDK 8": 3, "JDK 17": 3, "Java Development (Certification)": 2, "Advanced Java Development (Certification)": 2, "Salesforce Platform Administrator (Certification - In process)": 2, "Salesforce Platform Developer (Certification - In process)": 2, "C# Programming Certified Professional": 2, "Strapi": 1, "Wix": 2, "AG Grid": 3, "Domain-Driven Design (DDD)": 1, "Pub/Sub": 5, "HPSM": 4, "Subversion": 3, "Saga": 3, "Choreography": 3, "Circuit Breaker": 3, "AKS": 3, "Repository Design Pattern": 2, "Factory Design Pattern": 2, "Abstract Factory Design Pattern": 2, "SEO": 1, "Object-Oriented Programming": 4, "IoT": 3, "Microsoft .NET": 1, "AWS S3": 7, "Amazon Elastic Container Registry (ECR)": 3, "ADFS": 5, "Maven POM": 2, "Build.xml": 3, "AWS Storage Services": 1, "Security Groups": 3, "Multi-AZ VPC": 1, "Amazon CloudFormation": 3, "Amazon Auto Scaling": 5, "Microsoft Project (MPP)": 1, "Strategic Planning": 3, "EZTrieve": 3, "Peoplesoft Financials": 2, "Peoplesoft Supply Chain Management": 2, "Account Payables": 3, "Account Receivables": 3, "GL": 1, "Billing": 3, "Dimension Modeling": 4, "Fact Tables": 8, "Relational Databases": 2, "Data Mining": 1, "DWH": 3, "DM": 3, "Dimension Tables": 3, "Dashboards": 4, "Data Security and Compliance": 1, "Product Validation": 1, "API Development (ICS, ICRT)": 3, "Production Support (L3)": 2, "Test Plan Development": 1, "Test Script Development": 1, "IntelliJ IDEA": 1, "Spiral Model": 1, "Prototype Model": 1, "Oracle Database Administration": 3, "Oracle Cloud Infrastructure (OCI)": 3, "Oracle GoldenGate (implied)": 1, "Java JDK": 2, "Oracle Cloud I-Receivables": 1, "SharePoint": 2, "Microsoft Office": 3, "Ad-hoc Testing": 1, "Test Planning": 1, "SSMS": 3, "SQL Server Data Tools": 3, "SAP Profit Center Accounting (PCBS)": 3, "SAP AR Processing & Reporting": 1, "Cash Discount Management": 2, "Dispute and Deduction Management": 2, "SAP FI-GL Transactions": 1, "SAP AP/AR Transactions": 1, "Vendor/Customer Open Item Management": 2, "BPP Documentation": 1, "Order-to-Delivery Process Optimization": 2, "Certified SAP Functional Consultant": 1, "PFTC Roles": 2, "SAP ECC": 1, "SAP Scripts": 2, "Device Drivers": 2, "LED Manager": 2, "Mesh Networking": 3, "Cisco Aironet": 1, "ATOM (Mainframes/AS400 automation tool)": 1, "Test Automation": 1, "Test Strategy Creation": 3, "Test Coverage": 3, "Requirements Prioritization": 1, "ASP": 1, "N-tier applications": 2, "Client-server applications": 2, "Auto-scaling": 1, "Artifactory": 1, "Physical Database Design": 1, "Database Tuning": 1, "Snowpark API": 1, "PII": 2, "PCI": 2, "Trifacta": 1, "Oracle 8i": 2, "Oracle 11i": 1, "DB2 8.1": 2, "Python Worksheet": 1, "Certified Professional Data Engineer": 2, "ETL Design": 3, "ETL Development": 3, "Python Scripting": 2, "Informatica Data Management Cloud (IDMC)": 3, "Data Mart": 5, "Requirement Gathering": 1, "Solution Architecture": 2, "Dojo": 2, "Spring": 2, "Oracle Grid Control 11g": 5, "SQL*Plus": 2, "OCI AutoScaling": 2, "HP/IBM Power E980": 2, "HP/IBM Power E850": 1, "Exadata CS DBX7": 1, "Defect Management": 2, "Speedometer Charts": 2, "Sankey Diagrams": 1, "Pareto Charts": 2, "Waterfall Charts": 2, "SAP Material Master Data Management": 2, "SAP Procurement Processes": 2, "GAAP (Generally Accepted Accounting Principles)": 2, "IFRS (International Financial Reporting Standards)": 2, "SAP Treasury Modules": 2, "LTE": 3, "5G": 3, "Firehose": 1, "Test Estimation": 2, "Cloud Testing (AWS, GCP, Azure, Microsoft)": 1, "OAuth Testing": 1, "SSO Testing": 1, "KPI Development": 2, "SLA Development": 2, "Microsoft Excel Spreadsheets": 1, "Oracle 8i/11i": 1, "Snowpipe": 1, "Domain Driven Development (DDD)": 3, "Indexing": 3, "Database Performance Tuning": 3, "Database Partitioning": 2, "Error Handling": 3, "Oracle Database (11g, 10g, 9i, 8x)": 2, "Business 360": 1, "Data Pipelines": 2, "Bug Fixing": 1, "Pega 8.4": 1, "Pega 8.4.1": 2, "PCBS (Profit Center Budgeting System)": 1, "FI-GL Transactions": 2, "AP/AR Transactions": 2, "BPPs (Business Process Procedures) Documentation": 1, "Product Assortment Management": 1, "Event-Driven Architecture": 3, "Backend for Frontend (BFF)": 3, "Active Directory": 1, "Continuous Integration/Continuous Deployment (CI/CD)": 1, "Regression Analysis": 1, "Component-Based Architecture": 1, "Multi-tier Distributed Applications": 1, "Oracle Financials Cloud Receivables": 1, "Business Intelligence Publisher (BIP)": 1, "AI": 1, "Chatbot": 1, "AWS Aurora": 1, "Oracle 19c Database Administrator Training from Koenig Database Administration": 1, "Dimensions": 1, "Cloud Data Integration": 2, "Cloud Application Integration": 2, "Materialized Views": 1, "iTech Sharp": 1, "MS Unit Test": 1, "J#": 1, "Oracle Enterprise Manager (OEM)": 1, "WAR file deployment": 1, "OCI - Oracle Cloud Infrastructure Foundations Associate": 2, "Database Migration": 1, "Backup and Recovery": 1, "Import/Export": 1, "AWS Storage": 2, "AWS Active Directory": 2, "Report Development": 1, "RESTful Web Services": 3, "General Accepted Accounting Principles (GAAP)": 1, "Cash Management Integration": 1, "Business Process Documentation": 2, "SAP Certified Functional Consultant": 1, "complex join queries": 1, "Scalable architecture design": 3, "Azure Blueprints": 1, "Android": 2, "iOS": 2, "JUnit (implied)": 1, "Server-Side Encryption (S3)": 1, "Amazon ECR": 3, "Tibco Messaging": 1, "Azure Container Storage": 1, "Azure Tables": 1, "Azure Queues": 1, "Azure Blobs": 1, "RICEF objects": 1, "Analytical Applications": 2, "CDS Annotations": 1, "WebIDE": 2, "UAT (User Acceptance Testing)": 1, "Software Development Lifecycle (SDLC)": 1, "Data Collection and Storage": 1, "Lake Formation": 1, "Fraud Detection": 1, "FinCrime": 1, "Windows Application": 2, "PMO": 1, "Change Management": 1, "Supply Chain Management": 1, "Organizational Change Management (OCM)": 1, "Observable": 1, "Winforms": 2, "Windows Service": 3, "SVN Subversion": 2, "AWS (Amazon Web Services)": 1, "Logic App": 3, "AWS CloudWatch": 2, "Load Balancers": 2, "DNS Delegation": 2, "VPCs": 2, "Oracle Financials Cloud": 1, "Mobile Testing (iOS, Android)": 1, "ETL Design and Development": 1, "Data Warehouse (DWH)": 1, "Slowly Changing Dimensions (SCD Type 1, 2, 3)": 1, "Accounts Receivable (AR) Processing": 1, "Accounts Payable (AP) Processing": 1, "Sankey Charts": 1, "AWS Lambdas": 1, "High-Performance Architecture": 1, "Oracle Data Guard (Active/Passive)": 1, "Oracle 19c Database Administrator Training (Koenig)": 1, "Maven POM.xml": 1, "Virtual Machines (VMs)": 1, "Supply Chain Management (SCM)": 1, "Inbound/Outbound Proxies": 1, "Summary-View": 1, "ER Studio": 1, "ITSM (Service Desk)": 1, "Snyk": 1, "SAST": 1, "DAST": 1, "Data Modeling (Star Schema, Snowflake Schema)": 1, "C# Programming": 1, "Web Sites Design and Development": 1, "Problem Solving": 1, "Process Improvement": 1, "Data & Analytics Architecture Modernization": 1, "Enterprise Systems Integration": 1, "Software Architecture Planning & Design": 1, "Micro-services Architecture Design": 1, "Ruleset locking mechanisms": 1, "Single-page application (SPA)": 1, ".NET Core 5/6/8": 1, "Angular 8/10/12": 1, "Artificial Intelligence (AI)": 1, "Amazon Elastic Beanstalk": 1, ".NET Core Web API": 1, "Red Hat Linux 8.x": 1, "Red Hat Linux 7.x": 1, "Exadata CS X9M-2": 1, "Exadata CS DB X7": 1, "Data Guard Physical Standby": 1, "Data Guard Snapshot Standby": 1, "Export/Import": 1, "Profit Center Accounting (PCBS)": 1, "GAAP": 1, "IFRS": 1, "Data Cleansing": 1, "Load Strategizing": 1, "Code Optimization": 1, "System Integration Testing": 1, "AWS Firehose": 1, "Test Scheduling": 1, "Elasticache": 1, "MS-SQL": 1, "Terraform (IaC)": 1, "Azure Table Storage": 1, "Azure Redis Cache": 1, "Azure API Management (APIM)": 1, "Observables": 1, "AZ-900: Microsoft Azure Fundamentals": 1, "Cloud Design Patterns": 1, "Container Storage": 1, "Azure Storage (Tables, Queues, Blobs)": 1, "Blue-Green Deployment": 1, "Waterfall Methodology": 1, "RESTful": 1, "SNS Event Producers": 1, "SNS Event Consumers": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints", "High-Performance Architecture Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "C# Programming Certified Professional", "complex join queries", "Scalable architecture design", "Azure Blueprints", "Logic App", "VPC", "ASP.NET", "MVC", "High-Performance Architecture", "C# Programming", "Web Sites Design and Development", "Problem Solving", "Process Improvement", "Data & Analytics Architecture Modernization", "Enterprise Systems Integration", "Software Architecture Planning & Design", "Micro-services Architecture Design"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM", "Coded UI", "Oracle PL/SQL", "MS SharePoint Server", "MySQL", "Azure", "SSRS", "<PERSON><PERSON><PERSON>", "Wix", "Git", "Oracle", "PL/SQL", "iTech Sharp", "Postman", "MS Unit Test", "Observable", "MS Excel", "Winforms", "ASP.NET MVC", "Azure Blob Storage", "Azure Table Storage", "Azure App Service", "Azure Redis <PERSON>ache", "Azure Application Insights", "Azure API Management (APIM)", "Azure Logic Apps", "Entity Framework", "Lambda Expressions", "HTML", "CSS", "AJAX", "HTTP", "Observables", "AZ-900: Microsoft Azure Fundamentals"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C", "Pivotal Cloud Foundry (PCF)", "MVC", "Single Page Application (SPA)", "OAuth 2.0", "Saga Pattern", "API Gateway", "Circuit Breaker <PERSON>", "Event-Driven Architecture", "CQRS", "Backend for Frontend (BFF)", "Object-Oriented Programming (OOP)", ".NET", "Single-page application (SPA)", "Circuit Breaker", ".NET Core 5/6/8", "Angular 8/10/12", "IoT", "Blue-Green Deployment", "Waterfall Methodology"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Angular", "React", "XML Web Services", ".NET Core Apps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "T-SQL", "MS SQL Server", "Node.js", "AG Grid", "Domain-Driven Design (DDD)", "Pub/Sub", "ASP", "N-tier applications", "Client-server applications", ".NET", "Auto-scaling", "SQL Server 2016", "Domain Driven Development (DDD)", "RESTful Web Services", "Windows Service", "Test Driven Development (TDD)", "Elasticache", "NoSQL", "MS-SQL", "Terraform (IaC)", "RESTful", "SNS Event Producers", "SNS Event Consumers"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems", "ASP.NET Core", "C#", "Angular", ".NET Framework", "Web Jobs", "Visual Studio", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Tridion CMS", "SQL Server", "PMP", "Internet of Things (IoT)", "HPSM", "SOA", "Subversion", "Saga", "Choreography", "Circuit Breaker", "AKS", "Repository Design Pattern", "Factory Design Pattern", "Abstract Factory Design Pattern", "Azure Cloud", "SEO", "Object-Oriented Programming", "Agile", "SCRUM", "IoT", "Tibco Messaging", "Azure Storage", "Azure Container Storage", "Azure Tables", "Azure Queues", "Azure Blobs", "SVN Subversion", "Azure Active Directory (Azure AD)", "Team Foundation Server (TFS)", "Apache Kafka", "Artificial Intelligence (AI)", "MVC", "Cloud Design Patterns", "Amazon Web Services (AWS)", "Microsoft Azure", "Container Storage", "Azure Storage (Tables, Queues, Blobs)"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder", "Azure SQL", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "<PERSON><PERSON>", "Rally", "Microsoft .NET", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "Azure App Services", "AWS S3", "Amazon Elastic Container Registry (ECR)", "ADFS", "Amazon S3", "SSIS", "SSRS", "Microservices", "Amazon ECR", "Amazon EKS", "AKS", "Azure SQL Server", "AWS (Amazon Web Services)", "Amazon Elastic Beanstalk", ".NET Core Web API"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>", "Linux", "Multi-AZ", "High Availability", "Disaster Recovery", "Maven <PERSON>", "Build.xml", "AWS Storage Services", "Security Groups", "Multi-AZ VPC", "Artifactory", "Active Directory", "AWS Storage", "Virtual Network", "AWS Active Directory", "Maven POM.xml", "Virtual Machines (VMs)"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption", "AWS-Kops (EKS)", "HTTP", "TLS", "Amazon CloudFormation", "Amazon Auto Scaling", "Amazon Lambda", "AWS", "Azure DevOps", "Pivotal Cloud Foundry", "SCM", "Continuous Integration/Continuous Deployment (CI/CD)", "Microservices"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM", "Windows Application Migration", "OTT", "RACI Matrix", "Microsoft Project (MPP)", "Windows Application", "Risk Management", "Stakeholder Management", "PMO", "Program Management", "Project Management", "Release Management", "Change Management", "Supply Chain Management", "Organizational Change Management (OCM)"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001", "Strategic Planning", "EZTrieve", "J#"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization", "S3", "Git", "Informatica PowerCenter", "Business Intelligence", "Data Migration", "Performance Tuning", "Query Optimization", "Peoplesoft Financials", "Peoplesoft Supply Chain Management", "Account Payables", "Account Receivables", "GL", "Billing", "Data Validation", "Dimension Modeling", "Fact Tables", "Physical Database Design", "Database Tuning", "Snowpark API", "PII", "PCI", "Trifacta", "SQL Server", "Oracle 8i", "Oracle 11i", "DB2 8.1", "<PERSON> (GL)", "Python Worksheet", "Certified Professional Data Engineer", "Oracle 8i/11i", "Snowpipe"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "SSIS (SQL Server Integration Services)", "Relational Databases", "Regression Analysis"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "Amazon Web Services (AWS)", "Amazon Athena", "Amazon Lake Formation", "Cloud Migration", "Data Governance", "Neb<PERSON>", "AWS S3", "Data Mining", "DWH", "DM", "Fact Tables", "Dimension Tables", "Dashboards", "Advanced Analytics", "ETL Design", "ETL Development", "Python Scripting", "Informatica Data Management Cloud (IDMC)", "Data Mart", "Project Management", "Requirement Gathering", "Solution Architecture", "Erro<PERSON>", "Oracle Database (11g, 10g, 9i, 8x)", "EC2", "Athena", "Lake Formation", "CloudFormation", "<PERSON>aud Detection", "FinCrime", "ETL Design and Development", "Data Warehouse (DWH)", "Slowly Changing Dimensions (SCD Type 1, 2, 3)", "Oracle 8i", "Data Cleansing", "Load Strategizing", "Performance Tuning", "Code Optimization", "Unit testing", "Integration Testing", "System Integration Testing", "User Acceptance testing"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile", "Business Intelligence", "Advanced Analytics", "Microsoft Excel", "Data Compliance", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Collection", "Data Storage Strategies", "Agile Transformation", "ISO27001 Compliance", "Data Security and Compliance", "Project Management", "KPI Development", "SLA Development", "Microsoft Excel Spreadsheets", "Relational Databases", "Data Collection and Storage"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering", "Mechanical Design", "Service Lifting Tools Design", "2D Drawings Review", "Product Validation", "Microsoft Office"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review", "Unix Shell Scripting", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Technical Design", "Technical Architecture", "Big Data", "PySpark", "Real-time Data Integration", "API Development (ICS, ICRT)", "Production Support (L3)", "Test Plan Development", "Test Script Development", "Business 360", "Data Pipelines", "Dimensions", "Fact Tables", "Cloud Data Integration", "Cloud Application Integration", "Materialized Views", "Stored Procedures", "Data Modeling (<PERSON> Schema, <PERSON><PERSON><PERSON>)", "Software Development Life Cycle (SDLC)"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)", "Amazon Web Services (S3, EC2, Lambda)", "Silverlight", "Node.js", "Software Development Lifecycle (SDLC)", "Amazon Web Services (AWS)"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres", "JUnit", "Dojo", "PostgreSQL", "AWS Aurora"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines", "ITIL Foundation", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "J2EE", "IntelliJ IDEA", "Spiral Model", "Prototype Model", "Spring", "Component-Based Architecture", "Multi-tier Distributed Applications", "WebLogic", "Oracle", "OOPS", "ER Studio", "ITSM (Service Desk)", "Snyk", "SAST", "DAST"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Oracle 19c Data Guard", "Oracle 19c RAC", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle Export/Import", "Transportable Tablespaces", "SQLTrace", "Oracle Real Application Cluster", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "High Availability", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "IAM", "VCN", "Object Storage", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "Exadata X9M-2", "HP", "IBM Power E980", "IBM Power E850", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "ISO/IEC 27001", "ISO 20000", "ISO 27000", "Oracle Database Administration", "Oracle Cloud Infrastructure (OCI)", "Oracle GoldenGate (implied)", "Java JDK", "Terada<PERSON>", "Oracle Grid Control 11g", "SQL*Plus", "OCI AutoScaling", "HP/IBM Power E980", "HP/IBM Power E850", "Exadata CS DBX7", "Oracle 19c Database Administrator Training from Koenig Database Administration", "Oracle Enterprise Manager (OEM)", "WAR file deployment", "OCI - Oracle Cloud Infrastructure Foundations Associate", "Database Migration", "Backup and Recovery", "Performance Tuning", "AWS", "Azure", "Import/Export", "Oracle Data Guard (Active/Passive)", "Oracle 19c Database Administrator Training (Koenig)", "Red Hat Linux 8.x", "Red Hat Linux 7.x", "Exadata CS X9M-2", "Exadata CS DB X7", "Data Guard Physical Standby", "Data Guard Snapshot Standby", "Export/Import"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany", "Oracle Cloud I-Receivables", "Oracle Financials Cloud Receivables", "Business Intelligence Publisher (BIP)", "Oracle Financials Cloud"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)", "Pega 8.4", "Pega 8.4.1", "Summary-View", "Reports", "Ruleset locking mechanisms"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN", "SharePoint", "Microsoft Office", "Continuous Integration", "SCRUM"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation", "Unit testing", "Test Case Execution", "Mobile Web Testing", "SDLC", "Performance Testing", "Ad-hoc Testing", "Test Planning", "Defect Management", "UAT (User Acceptance Testing)", "Test Scheduling"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "Excel", "SSMS", "SQL Server Data Tools", "Speedometer Charts", "Sankey Diagrams", "Pareto Charts", "Waterfall Charts", "Stored Procedures", "Functions", "Triggers", "Indexing", "Database Performance Tuning", "Query Optimization", "Database Partitioning", "Data Mart", "Report Development", "Sankey Charts"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)", "SAP Profit Center Accounting (PCA)", "Automated Bank Reconciliation", "Pricing Strategies", "SAP MM Functionalities", "Business Process Optimization", "Agile Methodologies", "SAP Profit Center Accounting (PCBS)", "SAP AR Processing & Reporting", "Cash Discount Management", "Dispute and Deduction Management", "SAP FI-GL Transactions", "SAP AP/AR Transactions", "Vendor/Customer Open Item Management", "BPP Documentation", "Order-to-Delivery Process Optimization", "Certified SAP Functional Consultant", "SAP Material Master Data Management", "SAP Procurement Processes", "GAAP (Generally Accepted Accounting Principles)", "IFRS (International Financial Reporting Standards)", "SAP Treasury Modules", "PCBS (Profit Center Budgeting System)", "FI-GL Transactions", "AP/AR Transactions", "BPPs (Business Process Procedures) Documentation", "Product Assortment Management", "General Accepted Accounting Principles (GAAP)", "Cash Management Integration", "Business Process Documentation", "SAP Certified Functional Consultant", "Accounts Receivable (AR) Processing", "Accounts Payable (AP) Processing", "Cash Management", "Profit Center Accounting (PCBS)", "GAAP", "IFRS"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "RICEF", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "PFTC Roles", "SAP ECC", "Agile", "Waterfall", "SAP Scripts", "RICEF objects", "Analytical Applications", "CDS Annotations", "WebIDE", "Supply Chain Management (SCM)", "Inbound/Outbound Proxies"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL", "API Gateway", "Amazon Web Services", "MySQL", "Microservices Architecture", "Lambda", "AI", "<PERSON><PERSON><PERSON>", "AWS Lambdas"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering", "Mac filter configuration", "Device Drivers", "LED Manager", "Mesh Networking", "Cisco Aironet", "Unit testing", "Integration Testing", "LTE", "5G", "<PERSON><PERSON>xing"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket", "Amazon Web Services (AWS)", "Lambda", "EC2", "SQS", "S3", "SNS", "CI/CD", "CloudWatch", "Step Functions", "AWS Glue", "Athena", "JDK 8", "JDK 17", "AWS Athena", "IAM", "Firehose", "Server-Side Encryption (S3)", "AWS Lambda", "AWS CloudWatch", "Load Balancers", "DNS Delegation", "VPCs", "AWS Firehose"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C", "IVR Testing", "CTI Testing", "Continuous Integration", "Continuous Deployment", "ATOM (Mainframes/AS400 automation tool)", "Test Automation", "Test Strategy Creation", "Test Coverage", "Requirements Prioritization", "ETL", "Test Estimation", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "OAuth Testing", "SSO Testing", "Android", "iOS", "<PERSON><PERSON><PERSON><PERSON> (implied)", "Mobile Testing (iOS, Android)"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Chary"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "PunniyaKodi V updated resume", "Chary"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite", "Chary"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV", "Vidwaan_vidwan_resume"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Chary", "<PERSON>"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>", "Chary"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Chary"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Laxman_Gite", "Chary"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "<PERSON><PERSON>"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET", "Laxman_Gite"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "Chary"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "PunniyaKodi V updated resume"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead", "Chandra_Resume"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>_DotNET"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON><PERSON>"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chary", "pavani_resume"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Lambda": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON>il .Net lead"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Step Functions": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>_DotNET"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "Chary", "Uday"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary", "PunniyaKodi V updated resume"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "Azure App Services": ["Chary", "<PERSON><PERSON>_DotNET"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Saga Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary", "<PERSON><PERSON>_DotNET"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary", "<PERSON><PERSON>_DotNET"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>il .Net lead"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET", "PunniyaKodi V updated resume"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET", "Chary"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Laxman_Gite"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>il .Net lead"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>", "Chandra_Resume"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet", "Chary"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Puneet"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Puneet"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Puneet"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Puneet"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Puneet"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Chary"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Chandra_Resume"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON>", "DA manager <PERSON><PERSON>"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON>_DotNET"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>", "Chary"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>", "<PERSON><PERSON>_DotNET"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume", "Chary"], "Single Page Application (SPA)": ["Chandra_Resume", "<PERSON><PERSON><PERSON>il .Net lead"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA", "PunniyaKodi V updated resume"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON>"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON>"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday", "<PERSON><PERSON><PERSON>"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)", "Chandra_Resume"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume", "PunniyaKodi V updated resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "<PERSON><PERSON><PERSON><PERSON>"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"], "Test Case Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Custom Visuals (Power BI)": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Through": ["<PERSON><PERSON><PERSON><PERSON>"], "Parameters": ["<PERSON><PERSON><PERSON><PERSON>"], "Cascading Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Interactive Dashboards": ["<PERSON><PERSON><PERSON><PERSON>"], "Reports": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "Pricing Strategies": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM Functionalities": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "IVR Testing": ["SivakumarDega_CV"], "CTI Testing": ["SivakumarDega_CV"], "Continuous Integration": ["SivakumarDega_CV", "pavani_resume"], "Continuous Deployment": ["SivakumarDega_CV"], "High-Performance Architecture Design": ["Laxman_Gite"], "Container-based Architecture Design": ["Laxman_Gite"], "High Throughput System Architecture Design": ["Laxman_Gite"], "Real-Time Data Analytics Solution Architecture Design": ["Laxman_Gite"], "E-commerce Architecture Design": ["Laxman_Gite"], "Microsoft technologies": ["Laxman_Gite"], "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": ["Laxman_Gite"], "Coded UI": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SharePoint Server": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], ".NET Core 6.0": ["PunniyaKodi V updated resume"], ".NET Core 8.0": ["PunniyaKodi V updated resume"], "XML Web Services": ["PunniyaKodi V updated resume"], ".NET Core Apps": ["PunniyaKodi V updated resume"], "Domain Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Web Jobs": ["Chary"], "Model-View-Controller (MVC)": ["Chary"], "Tridion CMS": ["Chary"], "Internet of Things (IoT)": ["Chary"], "Azure SQL": ["<PERSON><PERSON>_DotNET"], "Azure Pipelines": ["<PERSON><PERSON>_DotNET"], "Rally": ["<PERSON><PERSON>_DotNET"], "Multi-AZ": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "High Availability": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Disaster Recovery": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS-Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "HTTP": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "TLS": ["<PERSON><PERSON><PERSON>"], "Windows Application Migration": ["Puneet"], "OTT": ["Puneet"], "RACI Matrix": ["Puneet"], "S3": ["<PERSON>", "Vidwaan_vidwan_resume"], "Performance Tuning": ["<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>"], "Query Optimization": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Informatica Intelligent Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Center (IDMC)": ["<PERSON><PERSON><PERSON>"], "Amazon Lake Formation": ["<PERSON><PERSON><PERSON>"], "Cloud Migration": ["<PERSON><PERSON><PERSON>"], "Nebula": ["<PERSON><PERSON><PERSON>"], "Advanced Analytics": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Data Compliance": ["DA manager <PERSON><PERSON>"], "Key Performance Indicators (KPIs)": ["DA manager <PERSON><PERSON>"], "Service Level Agreement (SLAs)": ["DA manager <PERSON><PERSON>"], "Data Flow Architectures": ["DA manager <PERSON><PERSON>"], "Data Collection": ["DA manager <PERSON><PERSON>"], "Data Storage Strategies": ["DA manager <PERSON><PERSON>"], "Agile Transformation": ["DA manager <PERSON><PERSON>"], "ISO27001 Compliance": ["DA manager <PERSON><PERSON>"], "Mechanical Design": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tools Design": ["<PERSON><PERSON><PERSON>"], "2D Drawings Review": ["<PERSON><PERSON><PERSON>"], "Unix Shell Scripting": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": ["<PERSON><PERSON><PERSON>"], "Technical Design": ["<PERSON><PERSON><PERSON>"], "Technical Architecture": ["<PERSON><PERSON><PERSON>"], "Big Data": ["<PERSON><PERSON><PERSON>"], "Real-time Data Integration": ["<PERSON><PERSON><PERSON>"], "Amazon Web Services (S3, EC2, Lambda)": ["<PERSON><PERSON><PERSON>"], "Silverlight": ["<PERSON><PERSON><PERSON>"], "ITIL Foundation": ["Chandra_Resume"], "AWS Certified Developer - Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator - Associate": ["Chandra_Resume"], "Oracle 19c Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Transportable Tablespaces": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQLTrace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Real Application Cluster": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Fault Tolerance": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Scalability": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Virtualization": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA-Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO/IEC 27001": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 20000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 27000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Pega Marketing Consultant (Certification)": ["<PERSON><PERSON><PERSON>"], "Senior System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "RICEF": ["Uday"], "SAP Script": ["Uday"], "Smart Forms": ["Uday"], "Adobe Forms": ["Uday"], "ALV Reports": ["Uday"], "Mac filter configuration": ["A<PERSON><PERSON>_resume"], "Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "JDK 8": ["Vidwaan_vidwan_resume"], "JDK 17": ["Vidwaan_vidwan_resume"], "Java Development (Certification)": ["Soham_Resume_Java"], "Advanced Java Development (Certification)": ["Soham_Resume_Java"], "Salesforce Platform Administrator (Certification - In process)": ["Soham_Resume_Java"], "Salesforce Platform Developer (Certification - In process)": ["Soham_Resume_Java"], "C# Programming Certified Professional": ["Laxman_Gite"], "Strapi": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Wix": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AG Grid": ["PunniyaKodi V updated resume"], "Domain-Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Pub/Sub": ["PunniyaKodi V updated resume"], "HPSM": ["Chary"], "Subversion": ["Chary"], "Saga": ["Chary"], "Choreography": ["Chary"], "Circuit Breaker": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "AKS": ["Chary", "<PERSON><PERSON>_DotNET"], "Repository Design Pattern": ["Chary"], "Factory Design Pattern": ["Chary"], "Abstract Factory Design Pattern": ["Chary"], "SEO": ["Chary"], "Object-Oriented Programming": ["Chary"], "IoT": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Microsoft .NET": ["<PERSON><PERSON>_DotNET"], "AWS S3": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Amazon Elastic Container Registry (ECR)": ["<PERSON><PERSON>_DotNET"], "ADFS": ["<PERSON><PERSON>_DotNET"], "Maven POM": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Build.xml": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Storage Services": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Security Groups": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Multi-AZ VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon CloudFormation": ["<PERSON><PERSON><PERSON>"], "Amazon Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Microsoft Project (MPP)": ["Puneet"], "Strategic Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "EZTrieve": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Peoplesoft Financials": ["<PERSON>"], "Peoplesoft Supply Chain Management": ["<PERSON>"], "Account Payables": ["<PERSON>"], "Account Receivables": ["<PERSON>"], "GL": ["<PERSON>"], "Billing": ["<PERSON>"], "Dimension Modeling": ["<PERSON>"], "Fact Tables": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Relational Databases": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Data Mining": ["<PERSON><PERSON><PERSON>"], "DWH": ["<PERSON><PERSON><PERSON>"], "DM": ["<PERSON><PERSON><PERSON>"], "Dimension Tables": ["<PERSON><PERSON><PERSON>"], "Dashboards": ["<PERSON><PERSON><PERSON>"], "Data Security and Compliance": ["DA manager <PERSON><PERSON>"], "Product Validation": ["<PERSON><PERSON><PERSON>"], "API Development (ICS, ICRT)": ["<PERSON><PERSON><PERSON>"], "Production Support (L3)": ["<PERSON><PERSON><PERSON>"], "Test Plan Development": ["<PERSON><PERSON><PERSON>"], "Test Script Development": ["<PERSON><PERSON><PERSON>"], "IntelliJ IDEA": ["Chandra_Resume"], "Spiral Model": ["Chandra_Resume"], "Prototype Model": ["Chandra_Resume"], "Oracle Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure (OCI)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle GoldenGate (implied)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Java JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SharePoint": ["pavani_resume"], "Microsoft Office": ["pavani_resume", "<PERSON><PERSON><PERSON>"], "Ad-hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Planning": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SSMS": ["<PERSON><PERSON><PERSON><PERSON>"], "SQL Server Data Tools": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCBS)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Discount Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Dispute and Deduction Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Item Management": ["<PERSON><PERSON><PERSON><PERSON>"], "BPP Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "Certified SAP Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "PFTC Roles": ["Uday"], "SAP ECC": ["Uday"], "SAP Scripts": ["Uday"], "Device Drivers": ["A<PERSON><PERSON>_resume"], "LED Manager": ["A<PERSON><PERSON>_resume"], "Mesh Networking": ["A<PERSON><PERSON>_resume"], "Cisco Aironet": ["A<PERSON><PERSON>_resume"], "ATOM (Mainframes/AS400 automation tool)": ["SivakumarDega_CV"], "Test Automation": ["SivakumarDega_CV"], "Test Strategy Creation": ["SivakumarDega_CV"], "Test Coverage": ["SivakumarDega_CV"], "Requirements Prioritization": ["SivakumarDega_CV"], "ASP": ["PunniyaKodi V updated resume"], "N-tier applications": ["PunniyaKodi V updated resume"], "Client-server applications": ["PunniyaKodi V updated resume"], "Auto-scaling": ["PunniyaKodi V updated resume"], "Artifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Physical Database Design": ["<PERSON>"], "Database Tuning": ["<PERSON>"], "Snowpark API": ["<PERSON>"], "PII": ["<PERSON>"], "PCI": ["<PERSON>"], "Trifacta": ["<PERSON>"], "Oracle 8i": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Oracle 11i": ["<PERSON>"], "DB2 8.1": ["<PERSON>"], "Python Worksheet": ["<PERSON>"], "Certified Professional Data Engineer": ["<PERSON>"], "ETL Design": ["<PERSON><PERSON><PERSON>"], "ETL Development": ["<PERSON><PERSON><PERSON>"], "Python Scripting": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Data Mart": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Requirement Gathering": ["<PERSON><PERSON><PERSON>"], "Solution Architecture": ["<PERSON><PERSON><PERSON>"], "Dojo": ["<PERSON><PERSON>"], "Spring": ["Chandra_Resume"], "Oracle Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Plus": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI AutoScaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS DBX7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Defect Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Speedometer Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Sankey Diagrams": ["<PERSON><PERSON><PERSON><PERSON>"], "Pareto Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Waterfall Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "GAAP (Generally Accepted Accounting Principles)": ["<PERSON><PERSON><PERSON><PERSON>"], "IFRS (International Financial Reporting Standards)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Treasury Modules": ["<PERSON><PERSON><PERSON><PERSON>"], "LTE": ["A<PERSON><PERSON>_resume"], "5G": ["A<PERSON><PERSON>_resume"], "Firehose": ["Vidwaan_vidwan_resume"], "Test Estimation": ["SivakumarDega_CV"], "Cloud Testing (AWS, GCP, Azure, Microsoft)": ["SivakumarDega_CV"], "OAuth Testing": ["SivakumarDega_CV"], "SSO Testing": ["SivakumarDega_CV"], "KPI Development": ["DA manager <PERSON><PERSON>"], "SLA Development": ["DA manager <PERSON><PERSON>"], "Microsoft Excel Spreadsheets": ["DA manager <PERSON><PERSON>"], "Oracle 8i/11i": ["<PERSON>"], "Snowpipe": ["<PERSON>"], "Domain Driven Development (DDD)": ["PunniyaKodi V updated resume"], "Indexing": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Performance Tuning": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Partitioning": ["<PERSON><PERSON><PERSON><PERSON>"], "Error Handling": ["<PERSON><PERSON><PERSON>"], "Oracle Database (11g, 10g, 9i, 8x)": ["<PERSON><PERSON><PERSON>"], "Business 360": ["<PERSON><PERSON><PERSON>"], "Data Pipelines": ["<PERSON><PERSON><PERSON>"], "Bug Fixing": ["A<PERSON><PERSON>_resume"], "Pega 8.4": ["<PERSON><PERSON><PERSON>"], "Pega 8.4.1": ["<PERSON><PERSON><PERSON>"], "PCBS (Profit Center Budgeting System)": ["<PERSON><PERSON><PERSON><PERSON>"], "FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "BPPs (Business Process Procedures) Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Product Assortment Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Event-Driven Architecture": ["<PERSON><PERSON><PERSON>il .Net lead"], "Backend for Frontend (BFF)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Active Directory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Continuous Integration/Continuous Deployment (CI/CD)": ["<PERSON><PERSON><PERSON>"], "Regression Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Component-Based Architecture": ["Chandra_Resume"], "Multi-tier Distributed Applications": ["Chandra_Resume"], "Oracle Financials Cloud Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Intelligence Publisher (BIP)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Chatbot": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS Aurora": ["<PERSON><PERSON>"], "Oracle 19c Database Administrator Training from Koenig Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Dimensions": ["<PERSON><PERSON><PERSON>"], "Cloud Data Integration": ["<PERSON><PERSON><PERSON>"], "Cloud Application Integration": ["<PERSON><PERSON><PERSON>"], "Materialized Views": ["<PERSON><PERSON><PERSON>"], "iTech Sharp": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Unit Test": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "J#": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Oracle Enterprise Manager (OEM)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAR file deployment": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI - Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Database Migration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Import/Export": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWS Storage": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Active Directory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Report Development": ["<PERSON><PERSON><PERSON><PERSON>"], "RESTful Web Services": ["PunniyaKodi V updated resume"], "General Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Management Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Certified Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "complex join queries": ["Laxman_Gite"], "Scalable architecture design": ["Laxman_Gite"], "Azure Blueprints": ["Laxman_Gite"], "Android": ["SivakumarDega_CV"], "iOS": ["SivakumarDega_CV"], "JUnit (implied)": ["SivakumarDega_CV"], "Server-Side Encryption (S3)": ["Vidwaan_vidwan_resume"], "Amazon ECR": ["<PERSON><PERSON>_DotNET"], "Tibco Messaging": ["Chary"], "Azure Container Storage": ["Chary"], "Azure Tables": ["Chary"], "Azure Queues": ["Chary"], "Azure Blobs": ["Chary"], "RICEF objects": ["Uday"], "Analytical Applications": ["Uday"], "CDS Annotations": ["Uday"], "WebIDE": ["Uday"], "UAT (User Acceptance Testing)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Software Development Lifecycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Data Collection and Storage": ["DA manager <PERSON><PERSON>"], "Lake Formation": ["<PERSON><PERSON><PERSON>"], "Fraud Detection": ["<PERSON><PERSON><PERSON>"], "FinCrime": ["<PERSON><PERSON><PERSON>"], "Windows Application": ["Puneet"], "PMO": ["Puneet"], "Change Management": ["Puneet"], "Supply Chain Management": ["Puneet"], "Organizational Change Management (OCM)": ["Puneet"], "Observable": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Winforms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Service": ["PunniyaKodi V updated resume"], "SVN Subversion": ["Chary"], "AWS (Amazon Web Services)": ["<PERSON><PERSON>_DotNET"], "Logic App": ["Laxman_Gite"], "AWS CloudWatch": ["Vidwaan_vidwan_resume"], "Load Balancers": ["Vidwaan_vidwan_resume"], "DNS Delegation": ["Vidwaan_vidwan_resume"], "VPCs": ["Vidwaan_vidwan_resume"], "Oracle Financials Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Mobile Testing (iOS, Android)": ["SivakumarDega_CV"], "ETL Design and Development": ["<PERSON><PERSON><PERSON>"], "Data Warehouse (DWH)": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3)": ["<PERSON><PERSON><PERSON>"], "Accounts Receivable (AR) Processing": ["<PERSON><PERSON><PERSON><PERSON>"], "Accounts Payable (AP) Processing": ["<PERSON><PERSON><PERSON><PERSON>"], "Sankey Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "AWS Lambdas": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "High-Performance Architecture": ["Laxman_Gite"], "Oracle Data Guard (Active/Passive)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training (Koenig)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Maven POM.xml": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Virtual Machines (VMs)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Supply Chain Management (SCM)": ["Uday"], "Inbound/Outbound Proxies": ["Uday"], "Summary-View": ["<PERSON><PERSON><PERSON>"], "ER Studio": ["Chandra_Resume"], "ITSM (Service Desk)": ["Chandra_Resume"], "Snyk": ["Chandra_Resume"], "SAST": ["Chandra_Resume"], "DAST": ["Chandra_Resume"], "Data Modeling (Star Schema, Snowflake Schema)": ["<PERSON><PERSON><PERSON>"], "C# Programming": ["Laxman_Gite"], "Web Sites Design and Development": ["Laxman_Gite"], "Problem Solving": ["Laxman_Gite"], "Process Improvement": ["Laxman_Gite"], "Data & Analytics Architecture Modernization": ["Laxman_Gite"], "Enterprise Systems Integration": ["Laxman_Gite"], "Software Architecture Planning & Design": ["Laxman_Gite"], "Micro-services Architecture Design": ["Laxman_Gite"], "Ruleset locking mechanisms": ["<PERSON><PERSON><PERSON>"], "Single-page application (SPA)": ["<PERSON><PERSON><PERSON>il .Net lead"], ".NET Core 5/6/8": ["<PERSON><PERSON><PERSON>il .Net lead"], "Angular 8/10/12": ["<PERSON><PERSON><PERSON>il .Net lead"], "Artificial Intelligence (AI)": ["Chary"], "Amazon Elastic Beanstalk": ["<PERSON><PERSON>_DotNET"], ".NET Core Web API": ["<PERSON><PERSON>_DotNET"], "Red Hat Linux 8.x": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7.x": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS DB X7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Guard Physical Standby": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Guard Snapshot Standby": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Profit Center Accounting (PCBS)": ["<PERSON><PERSON><PERSON><PERSON>"], "GAAP": ["<PERSON><PERSON><PERSON><PERSON>"], "IFRS": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Cleansing": ["<PERSON><PERSON><PERSON>"], "Load Strategizing": ["<PERSON><PERSON><PERSON>"], "Code Optimization": ["<PERSON><PERSON><PERSON>"], "System Integration Testing": ["<PERSON><PERSON><PERSON>"], "AWS Firehose": ["Vidwaan_vidwan_resume"], "Test Scheduling": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Elasticache": ["PunniyaKodi V updated resume"], "MS-SQL": ["PunniyaKodi V updated resume"], "Terraform (IaC)": ["PunniyaKodi V updated resume"], "Azure Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure Redis Cache": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure API Management (APIM)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Observables": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AZ-900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Cloud Design Patterns": ["Chary"], "Container Storage": ["Chary"], "Azure Storage (Tables, Queues, Blobs)": ["Chary"], "Blue-Green Deployment": ["<PERSON><PERSON><PERSON>il .Net lead"], "Waterfall Methodology": ["<PERSON><PERSON><PERSON>il .Net lead"], "RESTful": ["PunniyaKodi V updated resume"], "SNS Event Producers": ["PunniyaKodi V updated resume"], "SNS Event Consumers": ["PunniyaKodi V updated resume"]}, "skill_categories": {}, "skill_metadata": {}}