{"all_skills": ["JMeter", "Cash Management", "BrowserStack", "Transportable Tablespaces", "WRICEF Documentation", "Dashboarding", "SmartView", "Dev Studio", "Gre", "Datadog", "DevExpress", "FIT-GAP Analysis", "Microsoft Project", "Data Marts", "SVN", "Real-time Data Analytics", "EN ISO 14122", "Pricing Strategies", "JSP", "Azure Data Factory", "Redis", "Oracle TDE", "Oracle Cloud Budgetary Control", "MS SharePoint Server", "TFS", "SAP Integration", "<PERSON>", "C# 9.0", "Oracle Allocations", "LINQ to Objects", "ADO.NET", "EN-13155", "Visual Studio 2008", "Next.js", "Mainframes testing", "MyEclipse", "Crystal Reports", "Salesforce APIs", "802.11", "Power Query", "OLAP", "Visual Studio 2010", "<PERSON><PERSON><PERSON>ber", "Object-Oriented Programming (OOP)", "Technical Architecture", "Product Life Cycle Management (PLM)", "IAM Role Management", "Material Design", "OCI WAF", "Oracle Autonomous Data Warehouse (ADW)", "TLS", "Tracer", "RTR", "Java Development", "Choreography Pattern", "Reverse Engineering", "Angular 7", "Operating Systems", "AWS Certified Solutions Architect - Associate", "Oracle Export/Import", "Azure Monitor", "ReactJS", "<PERSON><PERSON>", "Mantine UI", "Normalization", "<PERSON><PERSON><PERSON><PERSON>", "SAP FI", "Oracle OCI", "Caliburn.Micro", "Groovy", "Swift", "Python", "Azure APIM", "Microservices Architecture", "EKS", "Test Scripting", "HTML", "Screen Flows", "MCA", "SQR 6.0", "Java Development (Certification)", "Blob Storage", "Athena", "Flow Actions", "Spark SQL", "ASP.NET", "Agents", "C# 10.0", "FullStory", "Oracle Enterprise Manager Grid Control 11g", "<PERSON><PERSON>", "<PERSON><PERSON>", "RFID", "Module Pool Programming", "Webpack", "IBM MQ", "IBM Power E980", "Data Architecture", "Angular 8", "JSON", "Production Planning (PP)", "Snowflake", "Procure to Pay (PTP)", "SiteMinder", "App Studio", "UDP", "Perfecto", "Event Hub", "Pega Marketing Consultant", "Oracle Cloud Accounts Payable", "IAM", "<PERSON>", "Java Mail", "Cisco Prime Infrastructure", "Pivotal Cloud Foundry", "NETCONF", "Logility", "Service Registration", "SAP", "Subversion (SVN)", "JSF", "UI Testing", "Machine Learning", "Amazon RDS", "CE Marking", "Waterfall Methodologies", "CDH", "<PERSON><PERSON>", "YANG", "ISTQB Certified Tester Foundation Level", "DB2", "Django", "SAP Financial Accounting (FI)", "Data Storage Strategies", "Apache ECharts", "Technical Design Documentation", "TypeScript", "Oracle Financial Accounting Hub", "Azure SQL Server", "ICS", "AZ-304", "WebJobs", "SAP CO", "IBM Power E850", "OpenID Connect", "Auto Scaling", "YAML Pipelines", "Azure DevOps", "Amazon ECS", "Key Management", "Ping Identity", "Test Case Development", "WebSphere", "MongoDB", "Postman", "Windows Services", "MySQL Aurora", "SOAP", "Server-Side Encryption", "IBM DB2", "OKTA", "SSRS (SQL Server Reporting Services)", "JavaScript", "BP Integrations", "XAML", "Angular 12", "Test Case Execution", "Informatica Power Center", "GCP", "Open API", "Virtualization", "WinSCP", "Salesforce Platform Developer (Certification - In process)", "Spring IOC", "React.js", "Circuit Breaker <PERSON>", "Continuous Integration", "OpenWRT", "Oracle PL/SQL", "Tridion CMS 2011", "Continuous Integration and Continuous Deployment (CI/CD)", "Technical Architecture Documentation", "Predictive Analysis", "SPAU", "Pa<PERSON><PERSON>", "Oracle Data Guard", "UML", "Data Management", "Pega Marketing Consultant (Certification)", "Appium", "txText Control", "Machinery Directive 2006/42/EC", "Angular", "Oracle 19c Database Administrator", "VCN", "Deep Learning", "Android App Development", "NumPy", "Oracle Cloud Financials", "Omniture", "InstallShield", "Amazon Web Services (AWS)", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "Web Dynpro ABAP", "Visual Studio 2003", "LINQ", "International Financial Reporting Standards (IFRS)", "JUnits", "Ubus", "Web Services", "Microsoft Certified Professional", "Pega Product Builder", "Data Integration", "IOS Testing", "CATIA", "Clipboard", "Apache Kafka", "SAFe", "Service Lifting Tools Design", "JUnit", "Sheet Metal Design", "Disaster Recovery", "Azure Service Fabric", "Desktop Application Testing", "Spring MVC", "Google Data Analytics Professional Certificate", "ASPX", "SCM", "MS Excel", "CloudWatch", "HP Quality Center", "AML Compliance", "<PERSON><PERSON>", "Generally Accepted Accounting Principles (GAAP)", "Oracle E-Business Suite R12", "Financial Data Management (FDMEE)", "Red Hat Linux 8", "Application Load Balancer", "WebLogic 14c", "Microsoft Azure", "Database Migration Service", "HP ALM", "ELT", "Bluetooth", "Azure Pipelines", "Agile Project Management", "SAP Warehouse Management", "CRM", ".NET 6", "End-to-End testing", "Enterprise Reporting", "SSIS (SQL Server Integration Services)", "ADFS (Active Directory Federation Services)", "VPC", "AWS Certified Developer Associate", "Financial Reporting Studio (FRS)", "<PERSON>er", "SAP Controlling (CO)", "SAML", "Amazon S3", "PowerShell", "OCA-Oracle Database Administrator Certified", "Data Warehouse testing", "Advanced Java Development", "Redux Toolkit", "Red Hat Linux", "SOLID principles", "RabbitMQ", "Putty", "Sitecore", "Business Process Optimization", "BIP", "AireOS controller", "SAP Fiori Launchpad", "DAX", "ASAP Methodology", "Microsoft technologies", "Windows Server 2016", "Oracle Transactional Business Intelligence (OTBI)", "High-Throughput System Architecture", "IDoc", "<PERSON><PERSON>", "Big Data", "NUnit", "Azure Active Directory (Azure AD)", "Fixed Assets", "WCF", "Fiori Elements", "ISO27001 Compliance", "Silverlight", "Interactive Dashboards", "Compatibility Testing", "Test effort estimation", "Oracle Cloud Accounts Receivable", "SQL Server Reporting Services (SSRS)", "AWS Certified Developer - Associate", "MVC", "AZ-104", "Object Oriented Programming", "AZ900: Microsoft Azure Fundamentals", "Google Cloud Platform (GCP)", "Data Profiling", "Order Management", "JMS", "SAS Visual Investigator", "Customer Exits", "Power BI", "Data Transformation", "Cypress", "Windows Application Migration", "ATOM", "Unix Shell Scripting", "Process Management", "Azure Data Lake", "Toad", "Amazon EKS", "Coded UI", "Teradata Certified Administrator (V2R5)", "Test Strategy", "A<PERSON>os", "Data Pipelining", "AWS API Gateway", "Activities", "CloudFront", "SAP Accounts Receivable (AR)", "Oracle Financials Cloud Accounts Payable", "Encryption", "Spring Data", "<PERSON><PERSON>", "XML Web Services", "AWR", "GraphQL APIs", "Admin Studio", "Senior System Architect (Certification)", "Profit Center Accounting (PCA)", "VPN", "AWS-Kops (EKS)", "Azure", "Test Driven Development (TDD)", "Netezza", "<PERSON><PERSON>", "Azure Key Vault", "OCI Load Balancing", "Data Migration", "Selenium", "Struts 2", "Azure Kubernetes Service (AKS)", "Inhouse Cash Management", "Federated Database Design", "Salesforce", "ITIL Foundation 2011", "NodeJS", "Microsoft SQL Server", "Cost Analysis", "Agile Methodologies", "MS SQL Server 2019", "Data Loader", "HttpClient", "SEO Optimization", "SQL Server 2016", "SAP Cash Management (CM)", "Tridion CMS 2013", "Client Management", "Scalability", "Browser compatibility testing", "SAP Accounts Payable (AP)", "Test Schedule Creation", "BottleRocket", "Orchestration", "ISO 20000", "AWS Aurora Postgres", "ITIL Foundation", "AWS CLI", "OCA - Oracle Database Administrator Certified", "<PERSON><PERSON>", "Web Application Testing", "Team Foundation Server (TFS)", "HFM", "SQL Server 2000", "Ethernet", "Service-Oriented Architecture (SOA)", "SQL", "WebLogic 12c", "AWS Certified Solutions Architect Associate", "Order to Cash (O2C)", "Custom Visuals (Power BI)", "Visual Studio 2012", "Amazon SQS", "OAuth 2.0", "STATSPACK", "Pig", "SSRS", "Sub Ledger Accounting (SLA)", "Azure Storage Services", "Amazon ECR (Elastic Container Registry)", "Azure Logic Apps", "Project Management", "Cost Optimization", "Hibernate", "Agile Methodology", "Release Management", "Cognito", "AWS Lake Formation", "Visual Studio 2017", "Informatica Intelligent Cloud Services (IICS)", "SQLTrace", "OIDC", "REST APIs", "Business Intelligence", "ALE IDOCs", "Model-View-Controller (MVC)", "ASP.NET Core", "Hybrid Solution Architecture", "Entity Framework", "AngularJS", "Amazon Web Services", "SmartSheet", "Data Cleaning", "POLARIS", "Getting Started with Power BI", "Pega Rules Process Engine", "Data Collection", "Prototype", "<PERSON><PERSON><PERSON>", "Cloud Data Governance", "Amazon SNS", "Test Management", "Data Compliance", "ETL testing", "OData", "SAP Certified Development Associate - SAP Fiori Application Developer", "Declarative Rules", "CSS", "Micro-services", ".NET Core", "Kendo UI", "Material Master Data Management", "JMS Hermes", "Microservices testing", "Android Studio", "BTP", "Saga Pattern", "Informatica Data Management Center (IDMC)", "JR<PERSON>el", "ServiceNow", "Tibco", "macOS", "Data Decryption", "<PERSON><PERSON><PERSON>", "PCBS", "Azure Developer", "Machined Parts Design", "Exploratory testing", "Process Flows", "Ka<PERSON><PERSON>", "SoapUI", "NuGet", "XML Parser", "BDC", "FDMEE", "Enhancement Points", "SAP SD", "Advanced Java Development (Certification)", "Predictive Modeling", "GitLab", "CMMI Level 5", "PivotTables", "Hive", "GD&T", "Service Level Agreement (SLAs)", "Test-Driven Development (TDD)", "JAX-RS", "System Integration", "Azure Cloud Architectures", "Software Development Life Cycle (SDLC)", "XML", "User Acceptance testing", "MicroStrategy", "Design and Analysis of Algorithms", "Azure Cloud", "App Services", "SAP UI5 Framework", "PLA", "Unix", "AT&T Echo controller", "RESTful APIs", "Insurance", "Source Code Management (SCM)", "AWS Step Functions", "React", "Tomcat", "Bitbucket", "AIM Methodology", "Prince2", "Windows", "VMware", "Test Plan Creation", "IoT Systems", "Oracle Cloud", "Azure Active Directory", "Salesforce Platform Administrator (Certification - In process)", "JDBC", "Oracle 19c Data Guard", "<PERSON><PERSON><PERSON>", "Automatic Payment Program (F110)", "Code Review", "<PERSON><PERSON>", "Cisco Catalyst 9800 Wireless Controller", "Cosmos DB", "Angular 10", "VPC Design", "SNS", "2D Drawing Review", "Inbound/Outbound Proxy", "ICRT", "IVR Testing", "Razor View Engine", "Azure Log Analytics", "EC2", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "High Throughput System Architecture Design", "BSP", "OOAD", "CloudFormation", "V<PERSON>am Backup and Recovery", "Build/Release Management", "FI-AR", "MuleSoft", "Bug Reporting", "Cash Pooling", "App Insights", "Tortoise SVN", "OBIEE", "QMI", "Google Tag Manager", "Snowsight", ".NET Core 6.0", "<PERSON> (GL)", "Amplify", "Lambda", "Configuration Management", "RMAN", "Manual Testing", "Oracle Cloud Fixed Assets", "<PERSON><PERSON><PERSON>", "ios-xe asr 1K router", "AWS CDK", "<PERSON>", "EF Core", "Gateway Aggregation", "Data Quality", "EJB", "PL/SQL", "AWS Certified SysOps Administrator Associate", "SSIS", "ANSYS", "Amazon Athena", "Data Pump", "YAML Pipeline", "Purchase Order (PO) Management", "El<PERSON>", "Power BI Service", "Cisco catalyst 3750 Switch", "Talwar controller", "ETL", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Dependency Injection", "Azure Container Registry", "Scenario Assessment", "AR Processing & Reporting", "Agile", "Predictive Forecasting", "Apache Tomcat", "ElasticSearch", "Repository Pattern", "Data Structures", "Accounts Receivable", "Microsoft Excel", "Accounts Payable", ".NET Framework 4.5", "Azure Entra ID", "Moq", "IP", "Data Warehousing", "Web IDE", "JSTL", "Data Enrichment", "Azure Data Factory (ADF)", "PFTC", "Power Pivot", "Confluence", "IntelliJ", "Peoplesoft FSCM", "ITIL V3 Foundation", "IBM LTO 9", "gRPC", "WebLogic 11g", "RDS", "Tridion CMS 2009", "Statistical Analysis", "Oracle Real Application Cluster", "Snowpark", "Database testing", "Active Reports", "Serverless Architecture", "HP Service Manager (HPSM)", "Brainbench C# 5.0", "Containerization", "Dependency Inversion Principle", "NestJS", "Mechanical Design", "TestNG", "Kali Linux", "Antifactory", "Oracle 19c Database Administrator Training", "Mechanical Component Design", "AWS Glue", "Component Localization", "Blue Yonder", "ETL Processes", "Mechanical Product Design", "ISO 27000", "MIS Management", "Performance Tuning", "Functional Testing", "Web Jobs", "ANT", "SQL Server 2012", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "Factory Pattern", "SAP Materials Management (MM)", "Kafka", "SAP Profit Center Accounting (PCA)", "Visual Studio 2013", "SAP MM", "Quartz", "Amazon Lambda", "Alert Management", "MS Access", "Azure Virtual Network", "Spring REST", "Stored Procedures", "Procure to Pay (P2P)", "SQL*Trace", "Stack Up Analysis", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "Oracle Cloud I-Expenses", "Swagger", "CAPM", "Informatica PowerCenter", "Visual Studio 2022", "NetBeans", "SDLC", "Elastic APM", "Network Security", "DBMS", "Dimensional Modeling", "Azure Container Registry (ACR)", "IBM BMP", "MudBlazor", "YAML", "JWT", "Summary-View Reports", "Single Page Application (SPA)", ".NET Framework 4.7", "API", "SAP S/4HANA", "CAPWAP", "Row-Level Security (RLS)", "Autonomous Transaction Processing (ATP)", "Tridion CMS 8.5", "MVC Design Pattern", "Cloud Data Catalog", "Star Schema", "Team Foundation Server", "Rule Resolution", "HTTP", "WCF RESTful", ".NET Framework 2.0", "Design Patterns", "Customer Accounting", "RBAC", "Google Analytics", "Sanity", "GitHub Copilot", "OOPS", "IBM Infosphere DataStage", "Cross-functional Collaboration", "React Router", "DDD", "Inventory Planning", "Data Security", "Demand Forecasting", "High Availability", "Resin", "E-commerce Architecture Design", "DNVGL", "Ansible", "Rally", "Index Design", "SSO", "AWS Auto Scaling", "High-Performance Architecture Design", "<PERSON><PERSON>", "Object-Oriented ABAP (OOABAP)", "Service Virtualization", "Java", "Regression Testing", "Crontab", "DFM", "Styled Components", "VS Code", "Excel", "Stimulsoft", "SonarQube", "User Exits", "Oracle Enterprise Manager", "Visio", "Real-Time Data Analytics Solution Architecture Design", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "Shell Scripting", "Tridion CMS", "ISO/IEC 27001", "Gulp", "IDMC", "I-Receivables", "Oracle Enterprise Manager 12c", "Oracle Autonomous Transaction Processing (ATP)", "Firebase Cloud Services", "CI/CD Pipeline", "Automation Testing", "Apache POI", "AMDP", "Business Objects (BO)", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "I-Expenses", "MS Azure", "Triggers", "Git", "Amazon EBS", "OTBI", "HP", "Ruleset locking", "VSAM", "Query Performance Optimization", "EN ISO 50308", "Technical Design", "Queue Processors", "Sections", "Power Automate", "Node.js", "Confluent <PERSON><PERSON><PERSON>", "Artificial Intelligence", "Oracle Cloud Cash Management", "Prism", "FI-AP", "F110 Automatic Payment Program", "Azure Functions", "ASP.NET Core 6.0", "Column <PERSON>", "E-commerce Architecture", "Delivery Management", "RFCs", "Domain Driven Design (DDD)", "<PERSON><PERSON>", "Terada<PERSON>", "<PERSON><PERSON>", "JBehave", "<PERSON><PERSON><PERSON><PERSON>", "LambdaTest", "RICEF", "ASME Y14.5", "ACCELQ", "General <PERSON><PERSON>", "Defect Tracking", "Azure AD", "Risk-based testing", "AGGrid", "Data Analysis", ".NET Framework 4.0", "Cisco Access Points", "Data Masking", "SAP MM Functionalities", "Vite", "DataMart", "PostgreSQL", "Log4j", "Redux", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Amazon Glue", "Azure SQL Database", "Waterfall", "Scrum Master", "CDL", "Fault Tolerance", "The Complete Python Developer", "AutoCAD", "Service Extension", "ShadCN UI", "Elastic Load Balancers", "Logic Apps", "R", "T-SQL", "Oracle Database Administrator Certified", "WAF", "PSM", "ECS", "WiFi", "GlassFish", "RDBMS", "Karate Framework", "JAX-WS", "Hangfire", "Oracle Financials Cloud General Ledger", "Azure App Service", "FRS", "ATC", "Bank Reconciliation", "AWS Lambda", "Data Flows", "SolidWorks", "ISO 27001", "Case Management", "C# 8.0", "2D Drawings Review", "Amazon IAM", "Web services testing", "Power BI Desktop", ".NET Framework 3.5", "Data Capture (CDC)", "Windows Server", "SAS Data Integration Studio", "Oracle Fusion Applications", "AIF", "Sanity Testing", "AWS CloudFormation", "Amazon Lake Formation", "Angular 9", "ABAP", "Sufi", "Advanced Analytics", "AWS", "Document review", "AKS (Azure Kubernetes Service)", "Web Application Automation", "Selenium RC", "PMP", "Mobile Web Testing", "SSAS", "Reports", "Oracle 9i", "Selenium Grid", "Quality Center", "Visual Studio 2015", "Pega 8", "BRF+", "Hyperion FRS", "Amazon Route 53", "Enterprise Class Structure", "Agile (SCRUM)", "FI-GL (FICO)", "Table Storage", "REST", "SAP UI5", "Smoke Testing", "Automated Data Entry", "Dynatrace", "Oracle 19c RAC", "Web API", "Oracle 11g/12c", "Sigma", "SeeTest (Experitest)", "CDS Views", "Ezetrieves", "Decision Rules", "Hypothesis Testing", "SWR", "PI/PO", "AWS SAM", "Azure Service Bus", "Azure Blob Storage", "Functions (Database)", "Functions", "Informatica Cloud Services (IICS)", "API Management", "Blazor", "MySQL", "<PERSON><PERSON><PERSON>", "Senior System Architect", "Regulatory Reporting", "User Acceptance Testing (UAT)", "Nx Monorepo", "Fivetran", "Automated Bank Reconciliation", "Lambda Expressions", "JDeveloper", "Snowf<PERSON>a", "Oracle Cloud Infrastructure", "<PERSON>adata", "SAP Fiori List Report Application", "WLAN", "Procurement Processes", "Amazon ELB", "WebLogic", "Salesforce Platform Administrator", "Infrastructure as Code (IaC)", "CI/CD", "Stakeholder Management", "Amazon CloudFront", "Vendor/Customer Open Items", "AWS Athena", "Data Automation", "Apache", "Cascading Filters", "Red Hat Linux Enterprise 8", "OCI IAM", "User Interface Testing", "Docker Registries", "BAPI", "UFT", "SeeTest/Experitest", "Key Performance Indicators (KPIs)", "HTML5", "@task", "AWS Elastic Kubernetes Service (EKS)", "Virtual Network", "TCP", "SQL Server 2008", "Exadata X9M-2", "A<PERSON> (EKS)", "Sparx Enterprise Architect", "Parameters", "SQL Server 2005", "Oracle 11g", "JRockit Mission Control", "S3", "SQL Server 2014", "<PERSON> Data Modeler", "Qualcomm SDX hardware", "Terraform", "Program Management", "OLTP", "OCI-Oracle Cloud Infrastructure Foundations Associate", ".NET Core Apps", "Entity Framework 7.0", "Data Gateway", "iText", "Business Process Management (BPM)", "ASP.NET Core 8.0", "Selenium WebDriver", "Spring Framework", "Application Design", "Intelligent Data Management Cloud (IDMC)", "Object Storage", "Data Analytics", "Oracle 21c", "ActiveMQ", "Advanced Planner Optimizer (APO)", "SQS", "NoSQL Databases", "Real-time Data Analytics Solution Architecture", "Data Modeling", "Avro", "NoSQL", "Microsoft Azure Certified Professional", "Data Modernization", "OCI VCN", "Amazon DynamoDB", "Integration Testing", "Real-time Data Integration", "S3 (Amazon S3)", "M Language", "Tailwind CSS", "Visual Studio", "CSV", "Oracle 8x", "Performance Testing", "Oracle RAC", "OAuth2", "Ad hoc Testing", "Query Plan Optimization", "TKPROF", "Servlets", "Linux Shell Scripting", "OpenSearch", "Classification", "Drill Down", "Event Grid", "AZ-204", "C", "Data Transforms", "Adobe Forms", "CICS", "CSS3", "Design Calculations", "ADDM", "Eclipse", "Mobile Testing", "TDD", "TortoiseSVN", "Elastic Beanstalk", "Amazon Web Services (S3, EC2, Lambda)", "Informatica 10.2", "OUM Methodology", "DevOps", "SCRUM", "Direct Connect", "Pega Group Benefits Insurance Framework", "Drill Through", "CVS", "Real-time Cash Visibility System", "HTTP (TLS)", "Smart Forms", "CA DevTest", "Query Optimization", "Financial Analysis", "List-View", "SQLite", "Abstract Factory Pattern", "PySpark", "Spark", "PHP", "Internet of Things (IoT)", "Test Execution", "LDAP", "Oracle DBA", "Flat Files", "SQL Server Integration Services (SSIS)", "MathCAD", "<PERSON>", "MVVM", "Azure Storage", "Autonomous Data Warehouse (ADW)", "API testing", "SQL Server 2017", "QTP", "Project Planning", "J2EE", "Record to Report (R2R)", "Warehouse Management", "Angular Reactive Forms", "Business 360 Console", "LINQ to SQL", "Multi-AZ", "Package locking", "Azure App Services", "Test Design", "Bootstrap", "Neb<PERSON>", "SAP Best Practices", "Windows 2007/2008/2010", "Blueprints", "EXPLAIN PLAN", "JPA", "CQRS", "Selenium IDE", "Microsoft Power BI", "Web Testing", "Amazon Redshift", "IBM LTO 8", "Resource Management", "Cloud Migration", "Amazon VPC", "Design FMEA", "Slowly Changing Dimensions (SCD)", "Streamset", "Pega 7.3", "Linux", "Data Dictionary (DDIC)", "BADIs", ".NET", "Amazon Elastic Kubernetes Service (EKS)", "Real-time Data Ingestion", ".NET MAUI", "JCL", "Message Queue", "OCI Auto Scaling", "ASP.NET MVC", "JDK", "E-Commerce", "OTT", "<PERSON><PERSON><PERSON>", "Design Standardization", "Data Governance", "Oracle 10g", "Data Wrangling", "AT interfaces", "Customer Telephony Integration (CTI) testing", "Mac filter configuration", "Flask", "High Throughput System Architecture", "Kubernetes", "RAP", "Container-based Architecture Design", "File-Aid", "Azure SQL", "SAP Sales and Distribution (SD)", ".NET Framework", "Bamboo", "Mainframe Testing", "MS SQL Server", "Visual Studio 2005", "SAP Security", "Data Encryption", "Product locking", "Red Hat Linux Enterprise 7", "VB.NET", "Report Definitions", "Business Workflow", "Coded UI Testing", "Extended Warehouse Management (EWM)", "RACI Matrix", "ES6", "SeeTest", "Oracle", "<PERSON><PERSON>", "Visual Studio 2019", "Continuous Deployment", "OpenTelemetry", "Smart View", "Mac filtering", "Unit testing", "Telerik", "C++", "Oracle 19c", "j<PERSON><PERSON><PERSON>", "Application Insights", "SAS Visual Analytics", "Snow SQL", "Azure API Management", "Production Support", "Order to Cash Management (OTC)", "Container-based Architecture", "Strapi CMS", "Talwar Simulator", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Visual Studio Code", "Talend", "<PERSON><PERSON><PERSON>", "Oracle Cloud General Ledger", "CDN", "Spring JPA", "Peoplesoft HCM", "LeanFT", "Onshore Rigging Calculations", "Android Testing", "DBT", "<PERSON><PERSON><PERSON>", "Microsoft BI Stack", "Oracle (PL/SQL)", "Red Hat Linux 7", "Microservices", "Software Architecture", "Amazon Firehose", "Data Visualization", "Amazon EC2", "Pivotal Cloud Foundry (PCF)", "Data Intelligence", ".NET Core 8.0", "Oracle Fusion Financials", "OCI CDN", "ALV Reports", "JDK 8", "ClearCase", "<PERSON><PERSON>", "Material UI", "JDK 17", "DynamoDB", "GitLab Pipelines", "<PERSON><PERSON> (Project Management Professional)", "SAP Script", "Cisco Aironet outdoor mesh access points", "System Architect", "Hyperion Financial Management (HFM)", "Azure Application Insights", "Regression", "AI-powered Automation", "Test Driven Development", "OCI Object Storage", "DFA", "REST Assured", "Data Flow Architectures", "Amazon CloudWatch", "Sass", "C#", "CTI Testing", "AWS Certified SysOps Administrator - Associate", "Databricks", "Order-to-Delivery Process", "Quality Management (QM)", "SAP NetWeaver Gateway", "Step Functions", "Apache Airflow", "Jersey REST", "Time Series Forecasting", "System Testing", "SOA", "AeroScout tags", "CosmosDB", "Service Lifting Tool Design", "Financial Reporting", "SPDD", "GraphQL", "Cobol", "WPF", "CentOS", "Interactive Voice Response (IVR) testing", "Risk Management", "Spring Boot", "Pega 7.2.2", "GDB", "Intercompany", "UG-NX", "Data Validation", "Test Case Design", "Agile Transformation", "SAP Solution Design", "API Gateway", "System Architect (Certification)", "AJAX", "Data Processing", "Business Process Mapping", "Log Analytics", "Snow Pipe", "SQL Server", "OAuth", "IICS", "Mobile Application Automation", "Domain Driven Design", "GitHub", "Requirements mapping", "IIS", "Oracle 12c", "Service Bus", "Salesforce Platform Developer", "Pega 7.4", "Harvest"], "skill_frequency": {"Python": 35, "JavaScript": 25, "Java": 29, "C#": 18, "SQL": 34, ".NET 6": 4, "ASP.NET Core": 11, "ASP.NET MVC": 8, "Angular": 24, "Web API": 14, "Azure": 18, "Azure Functions": 14, "Azure Developer": 4, "Azure Logic Apps": 3, "Azure Service Bus": 7, "Azure API Management": 1, "Azure Storage": 7, "Cosmos DB": 6, "Redis Cache": 4, "Azure Active Directory (Azure AD)": 2, "Azure Virtual Network": 1, "Azure Application Insights": 1, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 7, "Azure Container Registry": 4, "Azure Service Fabric": 4, "Azure Data Lake": 5, "YAML Pipelines": 3, "Docker": 19, "Kubernetes": 17, "CI/CD": 22, "Microservices": 13, "Serverless Architecture": 6, "HTML": 24, "CSS": 15, "jQuery": 18, "Event Grid": 4, "Event Hub": 4, "SQL Server": 24, "MySQL": 23, "Snowflake": 9, "T-SQL": 10, "PL/SQL": 11, "Stored Procedures": 10, "Triggers": 6, "Functions (Database)": 1, "Amazon Web Services (AWS)": 18, "Microsoft Azure": 10, "Agile Methodology": 8, "Design Patterns": 7, "Microservices Architecture": 3, "Federated Database Design": 3, "Container-based Architecture": 2, "High-Throughput System Architecture": 1, "Real-time Data Analytics Solution Architecture": 2, "E-commerce Architecture": 3, "Hybrid Solution Architecture": 3, "VPC Design": 3, "Direct Connect": 3, "VPN": 3, "Query Performance Optimization": 4, "Data Modeling": 18, "Microsoft Certified Professional": 2, "WPF": 3, "MVC": 3, "MS Azure": 3, "WCF": 10, "Blob Storage": 3, "Table Storage": 3, "App Services": 3, "Redis": 6, "App Insights": 3, "Azure APIM": 3, "Logic Apps": 6, "AZ900: Microsoft Azure Fundamentals": 3, "Caliburn.Micro": 3, "Prism": 3, "Entity Framework 7.0": 3, "XML Parser": 3, "LINQ": 7, "Stimulsoft": 3, "Angular Reactive Forms": 3, "HttpClient": 3, "NUnit": 6, "Coded UI Testing": 2, "ADO.NET": 10, "SQL Server Reporting Services (SSRS)": 3, "Strapi CMS": 3, "Windows Services": 5, "WCF RESTful": 3, "MS SQL Server 2019": 2, "PostgreSQL": 18, "SQLite": 3, "Oracle (PL/SQL)": 2, "MS Access": 6, "InstallShield": 3, "GitHub": 18, "TFS": 7, "SVN": 14, "IIS": 3, "Apache Tomcat": 3, "DevExpress": 3, "Brainbench C# 5.0": 2, "MVVM": 1, "ASP.NET": 9, ".NET Framework": 5, "Entity Framework": 7, "EF Core": 3, "Razor View Engine": 3, "Bootstrap": 14, "CosmosDB": 3, "ElasticSearch": 7, "Apache Kafka": 10, "ActiveMQ": 3, "Pivotal Cloud Foundry": 3, "Azure App Service": 5, "Node.js": 14, "React": 10, "OAuth2": 3, "Swagger": 6, "OOPS": 3, "SOLID principles": 5, "Team Foundation Server (TFS)": 3, "Git": 21, "Jira": 28, "Azure DevOps": 18, "Moq": 3, "Agile Methodologies": 3, "SCRUM": 10, "Waterfall Methodologies": 3, "Test-Driven Development (TDD)": 4, "C++": 8, "Service Bus": 3, "API Management": 3, "YAML Pipeline": 1, "Azure AD": 2, "Virtual Network": 3, "Application Insights": 3, "Log Analytics": 3, "Key Vault": 3, "Functions": 4, "Software Architecture": 2, "Micro-services": 1, "High Throughput System Architecture": 1, "Microsoft Azure Certified Professional": 1, "MCA": 1, "Open API": 2, "C": 8, ".NET Core": 6, "AJAX": 6, "AngularJS": 2, "ReactJS": 7, "XML": 10, "HTML5": 8, "Sass": 2, "TypeScript": 14, "JSON": 16, "DynamoDB": 6, "OpenSearch": 2, "EC2": 7, "CloudFront": 2, "IAM": 7, "ECS": 2, "SQS": 5, "SNS": 5, "Lambda": 2, "API Gateway": 7, "RDS": 2, "CloudWatch": 7, "Step Functions": 3, "Elastic Cache": 2, "NodeJS": 4, "AGGrid": 2, "txText Control": 2, "ASPX": 2, "SOAP": 15, "RESTful APIs": 2, "Crystal Reports": 5, "Active Reports": 2, "SSRS": 7, "SSIS": 7, "YAML": 2, "Terraform": 7, "DDD": 1, "TDD": 1, "Agile": 25, "NuGet": 1, "Object-Oriented Programming (OOP)": 3, "VB.NET": 2, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 2, "OpenTelemetry": 2, "FullStory": 2, "Google Analytics": 4, "ASP.NET Core 6.0": 1, "ASP.NET Core 8.0": 1, ".NET MAUI": 2, "XAML": 2, "C# 8.0": 1, "C# 9.0": 1, "C# 10.0": 1, "Web Services": 2, "REST": 8, "Angular 7": 1, "Angular 8": 1, "Angular 9": 1, "Angular 10": 1, "Angular 12": 1, "Material Design": 2, ".NET Framework 2.0": 1, ".NET Framework 3.5": 1, ".NET Framework 4.0": 1, ".NET Framework 4.5": 1, ".NET Framework 4.7": 2, "CI/CD Pipeline": 2, "Splunk": 6, "RabbitMQ": 2, "Amazon DynamoDB": 4, "Kendo UI": 4, "Amazon EC2": 10, "AWS Lambda": 8, "Azure App Services": 2, "WebJobs": 1, "Azure Active Directory": 2, "ServiceNow": 5, "HP Service Manager (HPSM)": 2, "Service-Oriented Architecture (SOA)": 2, "OAuth 2.0": 2, "OKTA": 4, "Azure Entra ID": 2, "Bitbucket": 9, "Team Foundation Server": 2, "Subversion (SVN)": 4, "TortoiseSVN": 3, "Visual Studio 2003": 1, "Visual Studio 2005": 1, "Visual Studio 2008": 1, "Visual Studio 2010": 1, "Visual Studio 2012": 1, "Visual Studio 2013": 1, "Visual Studio 2015": 1, "Visual Studio 2017": 1, "Visual Studio 2019": 1, "Visual Studio 2022": 1, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 3, "OpenID Connect": 1, "Ping Identity": 1, "Salesforce APIs": 1, "CQRS": 2, "Saga Pattern": 2, "Choreography Pattern": 2, "Gateway Aggregation": 1, "Circuit Breaker Pattern": 2, "Message Queue": 1, "MuleSoft": 2, "Kafka": 6, "Tibco": 2, "AKS (Azure Kubernetes Service)": 2, "MVC Design Pattern": 1, "Repository Pattern": 2, "Dependency Inversion Principle": 2, "Dependency Injection": 2, "Factory Pattern": 2, "Abstract Factory Pattern": 2, "Tridion CMS 2009": 1, "Tridion CMS 2011": 1, "Tridion CMS 2013": 1, "Tridion CMS 8.5": 1, "Sitecore": 2, "SEO Optimization": 2, "Omniture": 2, "Google Tag Manager": 2, "SQL Server 2000": 1, "SQL Server 2005": 1, "SQL Server 2008": 1, "SQL Server 2012": 1, "SQL Server 2014": 1, "SQL Server 2017": 1, "Azure SQL Server": 1, "Oracle PL/SQL": 5, "Selenium": 2, "Azure Data Factory": 2, "PMP (Project Management Professional)": 1, "Agile (SCRUM)": 2, "Kanban": 4, "AZ-104": 2, "AZ-204": 2, "AZ-304": 2, "Machine Learning": 6, "Deep Learning": 2, "Predictive Analysis": 2, "Artificial Intelligence": 2, "IoT Systems": 1, ".NET": 4, "gRPC": 2, "SSIS (SQL Server Integration Services)": 3, "SSRS (SQL Server Reporting Services)": 2, "LINQ to SQL": 2, "LINQ to Objects": 2, "Lambda Expressions": 2, "S3 (Amazon S3)": 2, "Amazon Elastic Kubernetes Service (EKS)": 2, "Amazon ECR (Elastic Container Registry)": 2, "Elastic Beanstalk": 4, "Application Load Balancer": 1, "NoSQL": 2, "Datadog": 2, "Azure Container Registry (ACR)": 2, "Azure Kubernetes Service (AKS)": 2, "Azure Blob Storage": 5, "Blazor": 2, "MudBlazor": 2, "Telerik": 2, "Redux": 2, "Hangfire": 2, "ADFS (Active Directory Federation Services)": 2, "Tableau": 15, "DB2": 9, "SAP": 4, "IDoc": 2, "Logility": 2, "Blue Yonder": 2, "CloudFormation": 2, "VPC": 2, "Jenkins": 20, "SonarQube": 4, "Antifactory": 2, "AWS Elastic Kubernetes Service (EKS)": 2, "ANT": 8, "Maven": 20, "Shell Scripting": 5, "Ansible": 4, "PowerShell": 5, "Tomcat": 10, "JBoss": 6, "WebLogic": 6, "WebSphere": 6, "Windows Server": 2, "Red Hat Linux": 1, "Unix": 6, "CentOS": 1, "VMware": 2, "Elastic Load Balancers": 2, "Waterfall": 12, "Batch Scripting": 2, "Amazon ECS": 2, "Amazon S3": 9, "Amazon EBS": 2, "Amazon VPC": 2, "Amazon ELB": 2, "Amazon SNS": 3, "Amazon RDS": 2, "Amazon IAM": 2, "Amazon Route 53": 2, "AWS CloudFormation": 4, "AWS Auto Scaling": 2, "Amazon CloudFront": 2, "Amazon CloudWatch": 3, "AWS CLI": 2, "Vault": 2, "Docker Hub": 2, "Docker Registries": 2, "AWS Kops (EKS)": 1, "Groovy": 2, "GitLab": 7, "Apache": 1, "Grafana": 2, "Pivotal Cloud Foundry (PCF)": 2, "Infrastructure as Code (IaC)": 2, "Configuration Management": 4, "Containerization": 2, "Orchestration": 2, "Build/Release Management": 2, "Source Code Management (SCM)": 2, "HTTP (TLS)": 1, "Key Management": 1, "Encryption": 1, "J2EE": 4, "SAFe": 2, "Confluence": 6, "Microsoft Project": 2, "SmartSheet": 2, "DevOps": 2, "Warehouse Management": 2, "CMMI Level 5": 4, "PMP": 3, "PSM": 2, "Agile Project Management": 2, "Scrum Master": 2, "Program Management": 4, "Project Management": 4, "Project Planning": 4, "Risk Management": 4, "Cost Analysis": 4, "Resource Management": 2, "Stakeholder Management": 2, "Delivery Management": 2, "Client Management": 2, "Release Management": 2, "Microsoft Excel": 3, "Azure Cloud": 4, "Cobol": 7, "Ezetrieves": 2, "IBM BMP": 2, "ISO 27001": 2, "DBT": 2, "AWS": 7, "Azure Data Factory (ADF)": 2, "Databricks": 4, "Database Migration Service": 2, "AWS Glue": 5, "Fivetran": 2, "Snow SQL": 2, "Streamset": 2, "Snowpark": 2, "Column Masking": 2, "Data Encryption": 2, "Data Decryption": 2, "Data Masking": 2, "Data Governance": 3, "Hive": 4, "Pig": 2, "Sqoop": 2, "PySpark": 3, "Sigma": 2, "Apache Airflow": 2, "Informatica Power Center": 1, "Talend": 5, "Peoplesoft FSCM": 2, "Peoplesoft HCM": 2, "Oracle": 10, "MS SQL Server": 4, "OLTP": 4, "OLAP": 4, "Data Warehousing": 13, "Data Architecture": 2, "Data Integration": 6, "ELT": 2, "ETL": 11, "Data Quality": 6, "Real-time Data Ingestion": 2, "Snow Pipe": 2, "Confluent Kafka": 2, "Snowsight": 2, "SQR 6.0": 2, "Avro": 2, "Parquet": 2, "CSV": 3, "Index Design": 2, "Query Plan Optimization": 1, "Data Analysis": 4, "Business Intelligence": 7, "Data Management": 2, "ETL Processes": 2, "Excel": 3, "Power BI": 9, "DAX": 5, "Statistical Analysis": 2, "Regression": 2, "Hypothesis Testing": 2, "Predictive Modeling": 2, "Time Series Forecasting": 2, "Classification": 2, "Data Cleaning": 2, "Data Transformation": 5, "Data Automation": 2, "PivotTables": 2, "Power Query": 5, "Pandas": 2, "NumPy": 2, "SQL Server Integration Services (SSIS)": 1, "R": 2, "Google Data Analytics Professional Certificate": 2, "Getting Started with Power BI": 2, "The Complete Python Developer": 2, "ISTQB Certified Tester Foundation Level": 2, "Informatica PowerCenter": 5, "IICS": 1, "IDMC": 1, "IBM Infosphere DataStage": 2, "SAS Data Integration Studio": 2, "Oracle 11g": 4, "Oracle 10g": 4, "Oracle 9i": 2, "Oracle 8x": 2, "Microsoft SQL Server": 2, "Amazon Redshift": 2, "Data Migration": 5, "Data Modernization": 2, "Data Enrichment": 2, "Data Validation": 2, "Data Processing": 1, "Data Pipelining": 2, "Data Visualization": 4, "Enterprise Reporting": 1, "Dashboarding": 1, "AWS Athena": 1, "AWS Lake Formation": 1, "Microsoft Power BI": 2, "OBIEE": 2, "SAS Visual Investigator": 2, "SAS Visual Analytics": 2, "Erwin Data Modeler": 4, "Sparx Enterprise Architect": 2, "RDBMS": 4, "Star Schema": 7, "Snowflake Schema": 7, "Slowly Changing Dimensions (SCD)": 2, "Normalization": 2, "Flat Files": 1, "Predictive Forecasting": 1, "Alert Management": 1, "Regulatory Reporting": 1, "AML Compliance": 1, "Data Intelligence": 1, "Scenario Assessment": 1, "MIS Management": 1, "MS Excel": 1, "Data Security": 2, "Data Wrangling": 2, "Visual Studio": 3, "Mechanical Product Design": 2, "Mechanical Component Design": 1, "System Integration": 2, "Sheet Metal Design": 2, "Machined Parts Design": 2, "Design Standardization": 2, "Component Localization": 2, "Cost Optimization": 2, "Design Calculations": 2, "Cross-functional Collaboration": 2, "Onshore Rigging Calculations": 2, "Service Lifting Tool Design": 1, "Process Management": 2, "UG-NX": 2, "SolidWorks": 2, "CATIA": 2, "AutoCAD": 2, "ANSYS": 2, "Design FMEA": 2, "DFM": 2, "DFA": 2, "GD&T": 2, "Stack Up Analysis": 2, "ASME Y14.5": 2, "2D Drawing Review": 1, "MathCAD": 2, "CE Marking": 2, "DNVGL": 2, "EN-13155": 2, "Machinery Directive 2006/42/EC": 2, "EN ISO 50308": 2, "EN ISO 14122": 2, "Reverse Engineering": 2, "Informatica Cloud Services (IICS)": 2, "Intelligent Data Management Cloud (IDMC)": 2, "Netezza": 2, "Teradata": 2, "Windows": 7, "Spark": 2, "Data Profiling": 2, "Business 360 Console": 2, "Cloud Data Governance": 2, "Cloud Data Catalog": 2, "Data Marts": 1, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 2, "API": 2, "ICS": 2, "ICRT": 2, "Nifi": 2, "Technical Design Documentation": 1, "Technical Architecture Documentation": 1, "Production Support": 5, "Code Review": 3, "Redux Toolkit": 2, "Axios": 2, "SWR": 2, "Formik": 2, "React Router": 2, "CSS3": 4, "ES6": 2, "Material UI": 2, "Tailwind CSS": 2, "PHP": 2, "Amazon Lambda": 2, "Gulp": 2, "Grunt": 2, "Webpack": 2, "GitHub Copilot": 2, "JWT": 2, "RBAC": 2, "Software Development Life Cycle (SDLC)": 1, "Spring Boot": 9, "Struts 2": 2, "Spring IOC": 2, "Spring MVC": 4, "Spring Data": 2, "Spring REST": 2, "Jersey REST": 2, "JSF": 2, "Apache POI": 2, "iText": 2, "Servlets": 4, "JSP": 4, "JDBC": 4, "JAX-WS": 2, "JAX-RS": 2, "Java Mail": 2, "JMS": 4, "JUnits": 1, "IBM MQ": 2, "Amazon EKS": 2, "Cucumber": 5, "Cypress": 4, "Dojo Toolkit": 2, "MongoDB": 6, "Quartz": 2, "Hibernate": 4, "Spring JPA": 2, "Putty": 2, "WinSCP": 2, "Bamboo": 2, "AWS Aurora Postgres": 2, "EJB": 2, "JSTL": 2, "JPA": 2, "Struts": 2, "Spring Framework": 2, "NestJS": 4, "WebLogic 11g": 2, "GlassFish": 4, "Resin": 2, "Oracle 11g/12c": 2, "IBM DB2": 2, "Eclipse": 2, "NetBeans": 2, "JDeveloper": 2, "IntelliJ": 2, "MyEclipse": 2, "VS Code": 2, "Toad": 8, "Visio": 2, "UML": 2, "CVS": 2, "SoapUI": 5, "JMS Hermes": 2, "JUnit": 14, "Log4j": 2, "JRockit Mission Control": 2, "JMeter": 2, "JRebel": 2, "Spiral": 2, "Prototype": 2, "Google Cloud Platform (GCP)": 4, "ITIL Foundation 2011": 1, "AWS Certified Solutions Architect Associate": 1, "AWS Certified Developer Associate": 1, "AWS Certified SysOps Administrator Associate": 1, "Dynatrace": 2, "LDAP": 2, "SiteMinder": 2, "SAML": 2, "Harvest": 2, "Nx Monorepo": 2, "OOAD": 2, "SOA": 2, "Single Page Application (SPA)": 2, "AWS CDK": 5, "@task": 2, "GitLab Pipelines": 2, "Oracle DBA": 2, "Oracle OCI": 2, "Oracle 19c": 2, "Oracle 12c": 2, "Oracle 21c": 2, "Oracle RAC": 1, "Oracle Data Guard": 2, "Oracle Enterprise Manager": 1, "Oracle TDE": 2, "Data Pump": 2, "Oracle Cloud Infrastructure": 2, "RMAN": 2, "Linux Shell Scripting": 2, "Crontab": 2, "AWR": 2, "ADDM": 2, "EXPLAIN PLAN": 2, "SQL*Trace": 1, "TKPROF": 2, "STATSPACK": 2, "WebLogic 14c": 2, "WebLogic 12c": 2, "JDK": 2, "SQL Server 2016": 2, "Veeam Backup and Recovery": 2, "Red Hat Linux 7": 1, "Red Hat Linux 8": 1, "Exadata": 2, "IBM LTO 9": 2, "IBM LTO 8": 2, "OCI IAM": 1, "OCI VCN": 1, "OCI Object Storage": 1, "OCI Load Balancing": 1, "OCI Auto Scaling": 1, "OCI CDN": 1, "OCI WAF": 1, "Autonomous Data Warehouse (ADW)": 1, "Autonomous Transaction Processing (ATP)": 1, "ITIL V3 Foundation": 2, "Prince2": 2, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 1, "Oracle 19c Database Administrator Training": 1, "Teradata Certified Administrator (V2R5)": 2, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 2, "Oracle E-Business Suite R12": 3, "Oracle Cloud Financials": 2, "Oracle Cloud General Ledger": 1, "Oracle Cloud Accounts Payable": 1, "Oracle Cloud Accounts Receivable": 1, "Oracle Cloud Fixed Assets": 1, "Oracle Cloud Cash Management": 1, "Oracle Cloud I-Expenses": 1, "Oracle Cloud Budgetary Control": 3, "Oracle Financial Accounting Hub": 3, "Oracle Transactional Business Intelligence (OTBI)": 2, "Financial Reporting Studio (FRS)": 2, "Smart View": 2, "Data Loader": 3, "Hyperion FRS": 3, "Business Process Management (BPM)": 3, "AIM Methodology": 3, "OUM Methodology": 3, "Sub Ledger Accounting (SLA)": 3, "Windows 2007/2008/2010": 3, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 3, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 3, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 3, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 3, "BIP": 3, "Pega Rules Process Engine": 2, "Pega Group Benefits Insurance Framework": 2, "Pega Product Builder": 1, "Pega 7.2.2": 2, "Pega 7.3": 2, "Pega 7.4": 2, "Pega 8": 2, "Unit testing": 2, "Harness": 2, "Sections": 2, "Flow Actions": 2, "List-View": 2, "Summary-View Reports": 2, "Report Definitions": 2, "Clipboard": 2, "Tracer": 2, "PLA": 2, "Product locking": 1, "Package locking": 1, "Ruleset locking": 1, "SDLC": 3, "E-Commerce": 2, "Insurance": 2, "Agents": 2, "Queue Processors": 2, "Decision Rules": 2, "Declarative Rules": 2, "Application Design": 2, "Case Management": 2, "Process Flows": 2, "Screen Flows": 2, "Data Transforms": 2, "Activities": 2, "Rule Resolution": 2, "Enterprise Class Structure": 2, "Dev Studio": 2, "App Studio": 2, "Admin Studio": 2, "CDH": 2, "Document review": 2, "Pega Marketing Consultant": 1, "Senior System Architect": 1, "System Architect": 1, "Postman": 7, "Selenium IDE": 3, "Selenium RC": 3, "Selenium WebDriver": 9, "Selenium Grid": 3, "TestNG": 9, "QTP": 3, "Gherkin": 3, "Ruby": 5, "Tortoise SVN": 2, "HP Quality Center": 3, "SeeTest (Experitest)": 2, "ACCELQ": 3, "JBehave": 3, "HP ALM": 5, "BrowserStack": 3, "LambdaTest": 3, "Functional Testing": 6, "Smoke Testing": 3, "System Testing": 6, "Integration Testing": 6, "Regression Testing": 6, "User Acceptance Testing (UAT)": 3, "UI Testing": 5, "Mobile Testing": 6, "Automation Testing": 4, "Web Testing": 3, "Compatibility Testing": 3, "Sanity Testing": 3, "Ad hoc Testing": 3, "Test Case Design": 2, "Test Plan Creation": 4, "Test Scripting": 1, "Test Execution": 2, "Defect Tracking": 3, "Bug Reporting": 1, "Test Management": 4, "AI-powered Automation": 2, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 3, "Android Testing": 3, "SSAS": 3, "Power BI Desktop": 3, "Power BI Service": 3, "M Language": 3, "Dimensional Modeling": 3, "Microsoft BI Stack": 3, "Power Pivot": 3, "Data Gateway": 3, "Row-Level Security (RLS)": 2, "Data Flows": 2, "DataMart": 2, "Power Automate": 3, "Visual Studio Code": 3, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 3, "ASAP Methodology": 3, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 3, "Business Process Mapping": 3, "FIT-GAP Analysis": 3, "Financial Reporting": 3, "SAP Solution Design": 3, "SAP Warehouse Management": 3, "Material Master Data Management": 3, "Procurement Processes": 3, "Order-to-Delivery Process": 2, "Demand Forecasting": 3, "Cash Pooling": 3, "Bank Reconciliation": 2, "F110 Automatic Payment Program": 2, "Real-time Cash Visibility System": 3, "Inhouse Cash Management": 3, "SAP Best Practices": 3, "Generally Accepted Accounting Principles (GAAP)": 3, "International Financial Reporting Standards (IFRS)": 3, "Financial Analysis": 3, "Automated Data Entry": 1, "AR Processing & Reporting": 1, "Customer Accounting": 1, "Vendor/Customer Open Items": 1, "SAP Integration": 1, "SAP S/4HANA": 2, "ABAP": 2, "OData": 2, "SAP UI5": 2, "Fiori": 2, "Fiori Elements": 2, "PI/PO": 2, "AIF": 2, "BRF+": 2, "Business Workflow": 2, "CRM": 2, "Web Dynpro ABAP": 2, "RAP": 2, "BTP": 2, "CAPM": 2, "Procure to Pay (PTP)": 2, "Order to Cash Management (OTC)": 2, "Production Planning (PP)": 2, "Quality Management (QM)": 2, "FI-AP": 2, "FI-AR": 2, "FI-GL (FICO)": 2, "RTR": 2, "SCM": 2, "Product Life Cycle Management (PLM)": 2, "Advanced Planner Optimizer (APO)": 2, "Extended Warehouse Management (EWM)": 2, "Data Dictionary (DDIC)": 2, "Module Pool Programming": 2, "Object-Oriented ABAP (OOABAP)": 2, "RFCs": 2, "BADIs": 2, "BDC": 2, "BAPI": 2, "BP Integrations": 2, "Enhancement Points": 2, "User Exits": 2, "Customer Exits": 2, "ALE IDOCs": 2, "Inbound/Outbound Proxy": 1, "SAP NetWeaver Gateway": 2, "Service Registration": 2, "Service Extension": 2, "CDS Views": 2, "AMDP": 2, "SAP Fiori List Report Application": 2, "Web IDE": 2, "BSP": 2, "SAP Fiori Launchpad": 2, "SAP UI5 Framework": 1, "Business Objects (BO)": 2, "ATC": 2, "SPDD": 2, "SPAU": 2, "SAP Security": 2, "PFTC": 2, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 2, "SAP Certified Development Associate - SAP Fiori Application Developer": 2, "Next.js": 2, "REST APIs": 6, "GraphQL APIs": 2, "AWS SAM": 2, "Apache ECharts": 2, "Cognito": 2, "OIDC": 2, "Mantine UI": 2, "Vite": 2, "MySQL Aurora": 2, "AWS API Gateway": 1, "Styled Components": 2, "Sanity": 2, "Amplify": 2, "ShadCN UI": 2, "Salesforce": 2, "CDL": 2, "Cisco Catalyst 9800 Wireless Controller": 2, "Talwar controller": 2, "AireOS controller": 2, "Cisco Access Points": 2, "Talwar Simulator": 2, "WiFi": 2, "802.11": 2, "WLAN": 2, "Ethernet": 2, "IP": 2, "TCP": 2, "UDP": 2, "CAPWAP": 2, "NETCONF": 2, "YANG": 2, "Swift": 2, "ClearCase": 2, "Cisco catalyst 3750 Switch": 2, "ios-xe asr 1K router": 2, "OpenWRT": 2, "Linux": 6, "QMI": 2, "AT interfaces": 2, "Ubus": 2, "Qualcomm SDX hardware": 2, "AT&T Echo controller": 2, "POLARIS": 2, "GDB": 2, "Gre": 2, "RFID": 2, "AeroScout tags": 2, "Cisco Aironet outdoor mesh access points": 2, "Cisco Prime Infrastructure": 2, "Mac filtering": 1, "Bash": 3, "Android App Development": 3, "Flask": 3, "Django": 3, "GraphQL": 3, "Amazon Web Services": 3, "macOS": 3, "Kali Linux": 3, "OAuth": 6, "AWS Certified Solutions Architect - Associate": 4, "Amazon SQS": 1, "Amazon Athena": 2, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 4, "Test Driven Development (TDD)": 2, "Mockito": 4, "Spark SQL": 2, "Server-Side Encryption": 2, "IAM Role Management": 2, "EKS": 2, "BottleRocket": 2, "React.js": 2, "Firebase Cloud Services": 2, "Cassandra": 2, "Android Studio": 2, "Bluetooth": 2, "Java Development": 1, "Advanced Java Development": 1, "Salesforce Platform Administrator": 1, "Salesforce Platform Developer": 1, "Appium": 3, "Perfecto": 3, "SeeTest": 3, "REST Assured": 3, "Karate Framework": 3, "UFT": 3, "LeanFT": 3, "Zephyr": 2, "Quality Center": 2, "Informatica 10.2": 3, "MicroStrategy": 3, "CICS": 3, "JCL": 3, "VSAM": 3, "Sufi": 3, "File-Aid": 3, "CA DevTest": 3, "ATOM": 1, "GCP": 3, "SSO": 3, "Test Strategy": 1, "Test Design": 1, "Test effort estimation": 1, "Requirements mapping": 1, "Risk-based testing": 3, "End-to-End testing": 3, "User Acceptance testing": 3, "Database testing": 3, "API testing": 3, "Web services testing": 3, "Microservices testing": 2, "Browser compatibility testing": 3, "Exploratory testing": 3, "ETL testing": 3, "Data Warehouse testing": 3, "Interactive Voice Response (IVR) testing": 1, "Customer Telephony Integration (CTI) testing": 1, "Mainframes testing": 1, "Service Virtualization": 3, "Continuous Integration and Continuous Deployment (CI/CD)": 1, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 2, "Oracle Financials Cloud Accounts Payable": 2, "Accounts Payable": 2, "Accounts Receivable": 2, "General Ledger": 2, "Fixed Assets": 2, "Cash Management": 2, "I-Expenses": 2, "I-Receivables": 2, "Order Management": 2, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 2, "Order to Cash (O2C)": 2, "Record to Report (R2R)": 2, "Oracle Allocations": 2, "Oracle Cloud": 1, "Intercompany": 2, "SeeTest/Experitest": 1, "Test Case Development": 1, "Test Schedule Creation": 1, "SAP Financial Accounting (FI)": 2, "SAP Controlling (CO)": 2, "SAP Sales and Distribution (SD)": 2, "SAP Materials Management (MM)": 2, "Hyperion Financial Management (HFM)": 2, "Financial Data Management (FDMEE)": 2, "Profit Center Accounting (PCA)": 1, "SAP Accounts Receivable (AR)": 2, "SAP Accounts Payable (AP)": 2, "General Ledger (GL)": 2, "Purchase Order (PO) Management": 1, "Inventory Planning": 2, "Automatic Payment Program (F110)": 1, "Network Security": 2, "Object Oriented Programming": 2, "Operating Systems": 2, "Design and Analysis of Algorithms": 2, "DBMS": 2, "Mainframe Testing": 2, "Performance Testing": 1, "User Interface Testing": 2, "Manual Testing": 1, "Mobile Web Testing": 2, "Desktop Application Testing": 1, "Web Application Testing": 1, "Data Analytics": 2, "Real-time Data Analytics": 1, "NoSQL Databases": 1, "Blueprints": 1, "Test Case Execution": 1, "Custom Visuals (Power BI)": 1, "Drill Down": 1, "Drill Through": 1, "Parameters": 1, "Cascading Filters": 1, "Interactive Dashboards": 1, "Reports": 1, "SAP Profit Center Accounting (PCA)": 1, "Automated Bank Reconciliation": 1, "Pricing Strategies": 1, "SAP MM Functionalities": 1, "Business Process Optimization": 1, "IVR Testing": 1, "CTI Testing": 1, "Continuous Integration": 1, "Continuous Deployment": 1, "High-Performance Architecture Design": 1, "Container-based Architecture Design": 1, "High Throughput System Architecture Design": 1, "Real-Time Data Analytics Solution Architecture Design": 1, "E-commerce Architecture Design": 1, "Microsoft technologies": 1, "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": 1, "Coded UI": 1, "MS SharePoint Server": 1, ".NET Core 6.0": 1, ".NET Core 8.0": 1, "XML Web Services": 1, ".NET Core Apps": 1, "Domain Driven Design (DDD)": 1, "Web Jobs": 1, "Model-View-Controller (MVC)": 1, "Tridion CMS": 1, "Internet of Things (IoT)": 1, "Azure SQL": 1, "Azure Pipelines": 1, "Rally": 1, "Multi-AZ": 1, "High Availability": 2, "Disaster Recovery": 1, "AWS-Kops (EKS)": 1, "HTTP": 1, "TLS": 1, "Windows Application Migration": 1, "OTT": 1, "RACI Matrix": 1, "S3": 2, "Performance Tuning": 1, "Query Optimization": 1, "Informatica Intelligent Cloud Services (IICS)": 1, "Informatica Data Management Center (IDMC)": 1, "Amazon Lake Formation": 1, "Cloud Migration": 1, "Nebula": 1, "Advanced Analytics": 1, "Data Compliance": 1, "Key Performance Indicators (KPIs)": 1, "Service Level Agreement (SLAs)": 1, "Data Flow Architectures": 1, "Data Collection": 1, "Data Storage Strategies": 1, "Agile Transformation": 1, "ISO27001 Compliance": 1, "Mechanical Design": 1, "Service Lifting Tools Design": 1, "2D Drawings Review": 1, "Unix Shell Scripting": 1, "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": 1, "Technical Design": 1, "Technical Architecture": 1, "Big Data": 1, "Real-time Data Integration": 1, "Amazon Web Services (S3, EC2, Lambda)": 1, "Silverlight": 1, "ITIL Foundation": 1, "AWS Certified Developer - Associate": 1, "AWS Certified SysOps Administrator - Associate": 1, "Oracle 19c Data Guard": 1, "Oracle 19c RAC": 1, "Red Hat Linux Enterprise 8": 1, "Red Hat Linux Enterprise 7": 1, "Oracle Enterprise Manager 12c": 1, "Oracle Enterprise Manager Grid Control 11g": 1, "Oracle Export/Import": 1, "Transportable Tablespaces": 1, "SQLTrace": 1, "Oracle Real Application Cluster": 1, "Windows Server 2016": 1, "Fault Tolerance": 1, "Scalability": 1, "Virtualization": 1, "Oracle Autonomous Data Warehouse (ADW)": 1, "Oracle Autonomous Transaction Processing (ATP)": 1, "VCN": 1, "Object Storage": 1, "Load Balancing": 1, "Auto Scaling": 1, "CDN": 1, "WAF": 1, "Exadata X9M-2": 1, "HP": 1, "IBM Power E980": 1, "IBM Power E850": 1, "OCI-Oracle Cloud Infrastructure Foundations Associate": 1, "OCA-Oracle Database Administrator Certified": 1, "Oracle 19c Database Administrator": 1, "ISO/IEC 27001": 1, "ISO 20000": 1, "ISO 27000": 1, "Pega Marketing Consultant (Certification)": 1, "Senior System Architect (Certification)": 1, "System Architect (Certification)": 1, "RICEF": 1, "SAP Script": 1, "Smart Forms": 1, "Adobe Forms": 1, "ALV Reports": 1, "Mac filter configuration": 1, "Athena": 1, "JDK 8": 1, "JDK 17": 1, "Java Development (Certification)": 1, "Advanced Java Development (Certification)": 1, "Salesforce Platform Administrator (Certification - In process)": 1, "Salesforce Platform Developer (Certification - In process)": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints", "High-Performance Architecture Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM", "Coded UI", "Oracle PL/SQL", "MS SharePoint Server", "MySQL"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Angular", "React", "XML Web Services", ".NET Core Apps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems", "ASP.NET Core", "C#", "Angular", ".NET Framework", "Web Jobs", "Visual Studio", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Tridion CMS", "SQL Server", "PMP", "Internet of Things (IoT)"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder", "Azure SQL", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "<PERSON><PERSON>", "Rally"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>", "Linux", "Multi-AZ", "High Availability", "Disaster Recovery"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption", "AWS-Kops (EKS)", "HTTP", "TLS"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM", "Windows Application Migration", "OTT", "RACI Matrix"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization", "S3", "Git", "Informatica PowerCenter", "Business Intelligence", "Data Migration", "Performance Tuning", "Query Optimization"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "SSIS (SQL Server Integration Services)"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "Amazon Web Services (AWS)", "Amazon Athena", "Amazon Lake Formation", "Cloud Migration", "Data Governance", "Neb<PERSON>"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile", "Business Intelligence", "Advanced Analytics", "Microsoft Excel", "Data Compliance", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Collection", "Data Storage Strategies", "Agile Transformation", "ISO27001 Compliance"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering", "Mechanical Design", "Service Lifting Tools Design", "2D Drawings Review"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review", "Unix Shell Scripting", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Technical Design", "Technical Architecture", "Big Data", "PySpark", "Real-time Data Integration"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)", "Amazon Web Services (S3, EC2, Lambda)", "Silverlight"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres", "JUnit"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines", "ITIL Foundation", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Oracle 19c Data Guard", "Oracle 19c RAC", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle Export/Import", "Transportable Tablespaces", "SQLTrace", "Oracle Real Application Cluster", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "High Availability", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "IAM", "VCN", "Object Storage", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "Exadata X9M-2", "HP", "IBM Power E980", "IBM Power E850", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "ISO/IEC 27001", "ISO 20000", "ISO 27000"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation", "Unit testing", "Test Case Execution", "Mobile Web Testing", "SDLC"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "Excel"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)", "SAP Profit Center Accounting (PCA)", "Automated Bank Reconciliation", "Pricing Strategies", "SAP MM Functionalities", "Business Process Optimization"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "RICEF", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL", "API Gateway", "Amazon Web Services"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering", "Mac filter configuration"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket", "Amazon Web Services (AWS)", "Lambda", "EC2", "SQS", "S3", "SNS", "CI/CD", "CloudWatch", "Step Functions", "AWS Glue", "Athena", "JDK 8", "JDK 17"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C", "IVR Testing", "CTI Testing", "Continuous Integration", "Continuous Deployment"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Chary"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "PunniyaKodi V updated resume", "Chary"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV", "Vidwaan_vidwan_resume"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Chary"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "PunniyaKodi V updated resume"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON>"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>_DotNET"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Lambda": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Step Functions": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary", "PunniyaKodi V updated resume"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure App Services": ["Chary"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary"], "Saga Pattern": ["Chary"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary", "<PERSON><PERSON>_DotNET"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET", "Chary"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet", "Chary"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON>", "DA manager <PERSON><PERSON>"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>", "Chary"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume"], "Single Page Application (SPA)": ["Chandra_Resume"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)", "Chandra_Resume"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"], "Test Case Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Custom Visuals (Power BI)": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Through": ["<PERSON><PERSON><PERSON><PERSON>"], "Parameters": ["<PERSON><PERSON><PERSON><PERSON>"], "Cascading Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Interactive Dashboards": ["<PERSON><PERSON><PERSON><PERSON>"], "Reports": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "Pricing Strategies": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM Functionalities": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "IVR Testing": ["SivakumarDega_CV"], "CTI Testing": ["SivakumarDega_CV"], "Continuous Integration": ["SivakumarDega_CV"], "Continuous Deployment": ["SivakumarDega_CV"], "High-Performance Architecture Design": ["Laxman_Gite"], "Container-based Architecture Design": ["Laxman_Gite"], "High Throughput System Architecture Design": ["Laxman_Gite"], "Real-Time Data Analytics Solution Architecture Design": ["Laxman_Gite"], "E-commerce Architecture Design": ["Laxman_Gite"], "Microsoft technologies": ["Laxman_Gite"], "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": ["Laxman_Gite"], "Coded UI": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SharePoint Server": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], ".NET Core 6.0": ["PunniyaKodi V updated resume"], ".NET Core 8.0": ["PunniyaKodi V updated resume"], "XML Web Services": ["PunniyaKodi V updated resume"], ".NET Core Apps": ["PunniyaKodi V updated resume"], "Domain Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Web Jobs": ["Chary"], "Model-View-Controller (MVC)": ["Chary"], "Tridion CMS": ["Chary"], "Internet of Things (IoT)": ["Chary"], "Azure SQL": ["<PERSON><PERSON>_DotNET"], "Azure Pipelines": ["<PERSON><PERSON>_DotNET"], "Rally": ["<PERSON><PERSON>_DotNET"], "Multi-AZ": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "High Availability": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Disaster Recovery": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS-Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "HTTP": ["<PERSON><PERSON><PERSON>"], "TLS": ["<PERSON><PERSON><PERSON>"], "Windows Application Migration": ["Puneet"], "OTT": ["Puneet"], "RACI Matrix": ["Puneet"], "S3": ["<PERSON>", "Vidwaan_vidwan_resume"], "Performance Tuning": ["<PERSON>"], "Query Optimization": ["<PERSON>"], "Informatica Intelligent Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Center (IDMC)": ["<PERSON><PERSON><PERSON>"], "Amazon Lake Formation": ["<PERSON><PERSON><PERSON>"], "Cloud Migration": ["<PERSON><PERSON><PERSON>"], "Nebula": ["<PERSON><PERSON><PERSON>"], "Advanced Analytics": ["DA manager <PERSON><PERSON>"], "Data Compliance": ["DA manager <PERSON><PERSON>"], "Key Performance Indicators (KPIs)": ["DA manager <PERSON><PERSON>"], "Service Level Agreement (SLAs)": ["DA manager <PERSON><PERSON>"], "Data Flow Architectures": ["DA manager <PERSON><PERSON>"], "Data Collection": ["DA manager <PERSON><PERSON>"], "Data Storage Strategies": ["DA manager <PERSON><PERSON>"], "Agile Transformation": ["DA manager <PERSON><PERSON>"], "ISO27001 Compliance": ["DA manager <PERSON><PERSON>"], "Mechanical Design": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tools Design": ["<PERSON><PERSON><PERSON>"], "2D Drawings Review": ["<PERSON><PERSON><PERSON>"], "Unix Shell Scripting": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": ["<PERSON><PERSON><PERSON>"], "Technical Design": ["<PERSON><PERSON><PERSON>"], "Technical Architecture": ["<PERSON><PERSON><PERSON>"], "Big Data": ["<PERSON><PERSON><PERSON>"], "Real-time Data Integration": ["<PERSON><PERSON><PERSON>"], "Amazon Web Services (S3, EC2, Lambda)": ["<PERSON><PERSON><PERSON>"], "Silverlight": ["<PERSON><PERSON><PERSON>"], "ITIL Foundation": ["Chandra_Resume"], "AWS Certified Developer - Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator - Associate": ["Chandra_Resume"], "Oracle 19c Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Transportable Tablespaces": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQLTrace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Real Application Cluster": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Fault Tolerance": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Scalability": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Virtualization": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA-Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO/IEC 27001": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 20000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 27000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Pega Marketing Consultant (Certification)": ["<PERSON><PERSON><PERSON>"], "Senior System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "RICEF": ["Uday"], "SAP Script": ["Uday"], "Smart Forms": ["Uday"], "Adobe Forms": ["Uday"], "ALV Reports": ["Uday"], "Mac filter configuration": ["A<PERSON><PERSON>_resume"], "Athena": ["Vidwaan_vidwan_resume"], "JDK 8": ["Vidwaan_vidwan_resume"], "JDK 17": ["Vidwaan_vidwan_resume"], "Java Development (Certification)": ["Soham_Resume_Java"], "Advanced Java Development (Certification)": ["Soham_Resume_Java"], "Salesforce Platform Administrator (Certification - In process)": ["Soham_Resume_Java"], "Salesforce Platform Developer (Certification - In process)": ["Soham_Resume_Java"]}, "skill_categories": {}, "skill_metadata": {}}