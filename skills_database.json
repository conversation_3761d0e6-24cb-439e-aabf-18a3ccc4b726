{"all_skills": ["<PERSON><PERSON><PERSON><PERSON>", "Bluetooth", "Requirements mapping", "Databricks", "CDH", "Gateway Aggregation", "Azure AD", "Salesforce APIs", "AZ900: Microsoft Azure Fundamentals", "Design and Analysis of Algorithms", "Power BI Service", "Automation Testing", "Qualcomm SDX hardware", "PI/PO", "SQR 6.0", "Project Planning", "R", "<PERSON><PERSON><PERSON>", "AWS Certified SysOps Administrator Associate", "OBIEE", "ISTQB Certified Tester Foundation Level", "Functions", "I-Receivables", "A<PERSON> (EKS)", "JavaScript", "Data Processing", "ICS", "Design Calculations", "Scrum Master", "ACCELQ", "Cisco catalyst 3750 Switch", "SCRUM", "SAP Fiori Launchpad", "BottleRocket", "Azure Virtual Network", "Swagger", "Windows", "Direct Connect", "<PERSON><PERSON>", "OCI IAM", "LINQ to SQL", "Android App Development", "SAP UI5", "AngularJS", "AWS Certified Solutions Architect Associate", "MySQL Aurora", "MVVM", "Apache ECharts", "AJAX", "Single Page Application (SPA)", "OpenTelemetry", "Red Hat Linux 8", "Ansible", "NoSQL", "ANSYS", "SDLC", "Ruleset locking", "Stimulsoft", "EC2", "Amazon ELB", "Advanced Planner Optimizer (APO)", "Oracle 21c", "Avro", "Mainframe Testing", "Container-based Architecture", "Data Pipelining", "Azure App Service", "Visual Studio 2003", "Procurement Processes", "DDD", "SQL Server 2014", "SQL", "ANT", "Confluent <PERSON><PERSON><PERSON>", "Data Capture (CDC)", "SSAS", "Service-Oriented Architecture (SOA)", "Customer Telephony Integration (CTI) testing", "Omniture", "Java Development", "BADIs", "PySpark", "App Studio", "OTBI", "PLA", "Declarative Rules", "Entity Framework 7.0", "<PERSON><PERSON>", "Oracle Data Guard", "AZ-104", "Prototype", "Streamset", "WRICEF Documentation", "SAS Visual Analytics", "Flow Actions", "Redis", "Predictive Forecasting", "<PERSON><PERSON><PERSON>", "SonarQube", "Harvest", "Data Cleaning", "Browser compatibility testing", "Dependency Inversion Principle", "Onshore Rigging Calculations", "Java", "ETL testing", "Source Code Management (SCM)", "Peoplesoft FSCM", "API Management", "Appium", "Fixed Assets", "Eclipse", "OOPS", "MS Excel", "VMware", "MyEclipse", "Oracle 19c", "SOA", "CSS", "RDS", "Test Case Development", "CosmosDB", "Blazor", "Software Development Life Cycle (SDLC)", "Perfecto", "SOLID principles", "Stakeholder Management", "ABAP", "CMMI Level 5", "SQLite", "OUM Methodology", "Dimensional Modeling", "Business Process Mapping", "Test-Driven Development (TDD)", "Product locking", "High-Throughput System Architecture", "Azure Service Bus", "RBAC", "<PERSON> Data Modeler", "Webpack", "SAP Cash Management (CM)", "Activities", "Material Master Data Management", "CI/CD Pipeline", "Oracle 19c Database Administrator Training", "Sanity", "Resource Management", "Cross-functional Collaboration", "Microservices Architecture", "Azure Container Registry (ACR)", "SAS Visual Investigator", "AWR", "OCI WAF", "Extended Warehouse Management (EWM)", "Salesforce Platform Developer", "VS Code", "Oracle Cloud", "Mobile Application Automation", "ASPX", "Ubus", "Inventory Planning", "Data Governance", "Oracle Fusion Financials", "User Acceptance Testing (UAT)", ".NET Framework", "Red Hat Linux", "ITIL Foundation 2011", "MudBlazor", "Business Process Management (BPM)", "VB.NET", "Red Hat Linux 7", "BP Integrations", "BRF+", "Spring Boot", "Windows Services", "Amazon ECS", "Cash Pooling", "FDMEE", "Predictive Modeling", "Data Security", "Azure Storage Services", "HTTP (TLS)", "EF Core", "JDeveloper", "AWS Step Functions", "Key Management", "FRS", "Sparx Enterprise Architect", "WebSphere", "AT interfaces", "RFCs", "SeeTest/Experitest", "Data Enrichment", "HP Service Manager (HPSM)", "Jersey REST", "Microsoft Azure Certified Professional", "Product Life Cycle Management (PLM)", "EN-13155", "InstallShield", "Test Driven Development", "SAP Controlling (CO)", "ASP.NET Core 6.0", "Google Data Analytics Professional Certificate", "OCI Auto Scaling", "Service Bus", "SQL Server 2005", "Regression", "DBMS", "WCF", "Toad", "<PERSON>", "Struts 2", "Scenario Assessment", "Azure App Services", "Razor View Engine", "Event Hub", "ETL Processes", ".NET Framework 4.7", "JAX-RS", "Amazon IAM", "Cloud Data Catalog", "JUnit", "BSP", "Data Dictionary (DDIC)", "Regression Testing", "SAS Data Integration Studio", "Exploratory testing", "802.11", "<PERSON><PERSON>", "Data Validation", "Mechanical Product Design", "Mechanical Component Design", "<PERSON><PERSON> (Project Management Professional)", "Business Objects (BO)", "JSON", "<PERSON><PERSON><PERSON>", "El<PERSON>", "Inbound/Outbound Proxy", "<PERSON><PERSON><PERSON>", "Hyperion Financial Management (HFM)", "HTML5", "M Language", "Azure Data Factory", "Apache Kafka", "Data Modernization", "BAPI", "AWS Lake Formation", "Regulatory Reporting", "Talwar controller", "Snowpark", "ES6", "TypeScript", "Software Architecture", "Normalization", "DAX", "Azure SQL Server", "Time Series Forecasting", "Agile Methodologies", "Hangfire", "Docker Registries", "Alert Management", "Oracle OCI", "Visual Studio 2017", "SQL*Trace", "JR<PERSON>el", "Data Architecture", "Power Query", "Mobile Web Testing", "Tortoise SVN", "Oracle Cloud Accounts Receivable", "Pega Marketing Consultant", "Visual Studio 2019", "Intelligent Data Management Cloud (IDMC)", "Visual Studio Code", "DBT", "E-commerce Architecture", "AutoCAD", "Federated Database Design", "ASME Y14.5", "Event Grid", "Snowsight", "Serverless Architecture", "Data Quality", "Material UI", "S3 (Amazon S3)", "Azure Key Vault", "OAuth 2.0", "Amazon Athena", "SEO Optimization", "Application Load Balancer", "Inhouse Cash Management", "Generally Accepted Accounting Principles (GAAP)", "Procure to Pay (PTP)", "Amazon RDS", "List-View", "AWS Certified Solutions Architect - Associate", "IAM", "Dev Studio", "Design FMEA", "Sigma", "Procure to Pay (P2P)", "Moq", "RTR", "Cisco Prime Infrastructure", "2D Drawing Review", "Amazon EKS", "Azure Active Directory", "ECS", "WinSCP", "TFS", "SSO", "Data Migration", "Google Tag Manager", "Service Virtualization", "SQL Server 2000", "C", "MVC", "LINQ to Objects", "Gulp", "Apache Airflow", "IIS", "SPDD", "Production Support", "Web Application Testing", "JMS Hermes", "Choreography Pattern", "Material Design", "XML Parser", "Agile Project Management", "Real-time Data Ingestion", "REST Assured", "SeeTest", "REST APIs", "Data Management", "TKPROF", "EKS", "Component Localization", "HttpClient", "Object Oriented Programming", "MicroStrategy", "Infrastructure as Code (IaC)", "Amazon CloudFront", "Service Registration", "Swift", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "V<PERSON>am Backup and Recovery", "AWS API Gateway", "Crontab", "SoapUI", "Angular 12", "ATOM", "Informatica Cloud Services (IICS)", "DataMart", ".NET 6", "Hibernate", "PivotTables", "Oracle 11g", "JSP", "EJB", "Netezza", "Quartz", "Amazon ECR (Elastic Container Registry)", "Triggers", "SAP Integration", "Azure Monitor", "Postman", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "API testing", "MySQL", "AWS Elastic Kubernetes Service (EKS)", "WebJobs", "NestJS", "Test Scripting", "Repository Pattern", "ASAP Methodology", "JMeter", "Logic Apps", "Agile", "JPA", "Amazon Glue", "Bitbucket", "Query Plan Optimization", "Fivetran", ".NET Framework 4.0", "ElasticSearch", "Service Extension", "<PERSON><PERSON>", "SQL Server 2012", "Pega Group Benefits Insurance Framework", "Amazon CloudWatch", "SQL Server", "FIT-GAP Analysis", "ASP.NET Core 8.0", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "System Integration", "Spark SQL", "WiFi", "Amazon Web Services", "Bank Reconciliation", "Oracle", "IntelliJ", "AR Processing & Reporting", "J2EE", "Case Management", "Gre", "Data Flows", "Amplify", "SAML", "<PERSON><PERSON>", "Containerization", "Cisco Aironet outdoor mesh access points", "OCA - Oracle Database Administrator Certified", "SmartView", "Performance Testing", "DFM", "International Financial Reporting Standards (IFRS)", "Amazon Elastic Kubernetes Service (EKS)", "Snowf<PERSON>a", "Real-time Data Analytics Solution Architecture", "Azure Active Directory (Azure AD)", "Azure", "Abstract Factory Pattern", "POLARIS", "Compatibility Testing", "Delivery Management", "PFTC", "SSRS", "GitHub Copilot", "IP", "Oracle (PL/SQL)", "gRPC", "Lambda Expressions", "Test effort estimation", "IBM LTO 8", "Mantine UI", "Star Schema", "Admin Studio", "Agents", "EN ISO 14122", "Build/Release Management", "Putty", "Oracle RAC", "GDB", "Technical Design Documentation", "ADDM", "SAP Security", "SVN", "JCL", "<PERSON> (GL)", "ASP.NET MVC", "Lambda", "Database Migration Service", "SmartSheet", "YANG", "Power BI Desktop", "Bootstrap", "Circuit Breaker <PERSON>", "Queue Processors", "CRM", "CICS", "CAPWAP", "VPC Design", "Salesforce", "AWS CDK", "React Router", "Test Strategy", "RDBMS", "JSF", "Business 360 Console", "HFM", "Data Warehousing", "AWS Lambda", "Web services testing", "MS SQL Server", "Snow Pipe", "Deep Learning", "C#", "SolidWorks", "Datadog", "NetBeans", "Table Storage", "Prism", "C# 8.0", "Azure API Management", "Predictive Analysis", "Test Driven Development (TDD)", "SSRS (SQL Server Reporting Services)", "Index Design", "CSS3", "Cost Optimization", "Test Case Design", "SQL Server 2016", "Linux", "SSIS (SQL Server Integration Services)", "Web Dynpro ABAP", "MongoDB", "Risk-based testing", "AZ-204", "Amazon SQS", "XML", "Logility", "AGGrid", "Styled Components", "CloudWatch", "Talend", "Azure Storage", "Tomcat", "Strapi CMS", "Web Services", "End-to-End testing", "ServiceNow", "@task", "IoT Systems", "OAuth", "SAP SD", "Kendo UI", "CATIA", "Apache", "Data Automation", "Demand Forecasting", "Spark", "Power BI", "Pega 7.3", "iText", "Visual Studio 2015", "AML Compliance", "CDS Views", "Oracle PL/SQL", "DevExpress", "SAP Warehouse Management", "AWS Athena", "TDD", "Bamboo", "Visual Studio", "VPN", "Dependency Injection", "Servlets", "Sufi", "Oracle Financials Cloud Accounts Payable", "Agile (SCRUM)", "JDK", "Pega 7.2.2", "IBM LTO 9", "Tibco", "Amazon Lambda", "Angular 8", "Network Security", "MVC Design Pattern", "Production Planning (PP)", "UG-NX", "LeanFT", "PSM", "VPC", "SOAP", "C# 10.0", "Team Foundation Server", "OIDC", "Column <PERSON>", "Project Management", "AKS (Azure Kubernetes Service)", "PostgreSQL", "Release Management", "RFID", "Oracle Fusion Applications", "DynamoDB", "<PERSON><PERSON><PERSON>", "Oracle Financial Accounting Hub", "SQL Server Reporting Services (SSRS)", "SSIS", "Groovy", "CentOS", "MuleSoft", "PCBS", "Data Pump", "ELT", "Statistical Analysis", "OData", "Node.js", "HP Quality Center", "Redux Toolkit", "Row-Level Security (RLS)", "Oracle Cloud Fixed Assets", "SAP NetWeaver Gateway", "Tridion CMS 8.5", "DB2", "Bug Reporting", "Microsoft Azure", "Oracle Cloud General Ledger", "Hive", "Git", "I-Expenses", "Order Management", "High Throughput System Architecture", "Pa<PERSON><PERSON>", "Caliburn.Micro", "WebLogic", "Warehouse Management", "Micro-services", "CloudFormation", "General <PERSON><PERSON>", "Firebase Cloud Services", "Sheet Metal Design", "Angular 9", "Snowflake", "<PERSON><PERSON>", "Ad hoc Testing", "Teradata Certified Administrator (V2R5)", "BTP", "App Services", "SAP Sales and Distribution (SD)", "<PERSON><PERSON><PERSON>ber", "Object-Oriented Programming (OOP)", "Karate Framework", "Azure Application Insights", "Azure APIM", "MIS Management", "NoSQL Databases", "Web Testing", "Mobile Testing", "DevOps", "SCM", "OCI CDN", "Getting Started with Power BI", "Functions (Database)", "Agile Methodology", "Pig", "Domain Driven Design", "PHP", "Oracle Cloud I-Expenses", "Cobol", "Cosmos DB", "Selenium", "JWT", "User Interface Testing", "Data Masking", "Advanced Java Development", "Continuous Integration and Continuous Deployment (CI/CD)", "Android Studio", "Smoke Testing", "AT&T Echo controller", "Redux", ".NET Framework 2.0", "F110 Automatic Payment Program", "Next.js", "Microservices", "Cloud Data Governance", "Tridion CMS 2011", "UFT", "YAML", "Prince2", "Microsoft Excel", "Spring Framework", "Selenium Grid", "SQL Server 2008", "Apache POI", "HP ALM", "Oracle Transactional Business Intelligence (OTBI)", "AWS CLI", "SAP FI", "Tracer", "Data Decryption", "<PERSON><PERSON>", "SAP Solution Design", "Oracle Cloud Accounts Payable", "Amazon Firehose", "AWS CloudFormation", "OOAD", "TestNG", "WCF RESTful", "Amazon EC2", "E-Commerce", "Design Patterns", "Oracle Cloud Cash Management", "EN ISO 50308", "SAP UI5 Framework", "<PERSON>", "Data Wrangling", "SiteMinder", "ETL", "Customer Exits", "Java Mail", "OCI Load Balancing", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "GitLab Pipelines", "LambdaTest", "A<PERSON>os", "Tailwind CSS", "Hypothesis Testing", "Pivotal Cloud Foundry", "Orchestration", "Terraform", "NuGet", "CQRS", "<PERSON><PERSON><PERSON>", "Pega Product Builder", "Dashboarding", "User Exits", "Ping Identity", "Amazon SNS", "Pega 7.4", "Program Management", "MS SQL Server 2019", "API", "Log4j", "Windows Server", "Cash Management", "NUnit", "Pega Rules Process Engine", "ISO 27001", "Subversion (SVN)", "Manual Testing", "Ka<PERSON><PERSON>", "App Insights", "T-SQL", "<PERSON>adata", ".NET MAUI", "PowerShell", "IBM Infosphere DataStage", "Factory Pattern", "BIP", "Angular 10", "Telerik", "SAP Accounts Receivable (AR)", "Data Analytics", "txText Control", "System Testing", "Enterprise Class Structure", "Order to Cash Management (OTC)", "Functional Testing", "FI-AP", "Encryption", "FI-GL (FICO)", "Object-Oriented ABAP (OOABAP)", "Senior System Architect", "Accounts Receivable", "Autonomous Transaction Processing (ATP)", "Log Analytics", "Kubernetes", "Reverse Engineering", "Elastic Load Balancers", "SWR", "Azure Logic Apps", "Pega 8", "Blob Storage", "Tridion CMS 2013", ".NET Framework 3.5", "The Complete Python Developer", "Sass", "Unit testing", "Configuration Management", "Message Queue", "Profit Center Accounting (PCA)", "OpenSearch", "Machinery Directive 2006/42/EC", "TortoiseSVN", "Business Intelligence", "Step Functions", "Ezetrieves", "Mac filtering", "Android Testing", "TCP", "ios-xe asr 1K router", "AMDP", "Oracle DBA", "Test Plan Creation", "CDL", "<PERSON><PERSON>", "<PERSON>", "QTP", "RAP", "OLAP", "Order-to-Delivery Process", "GD&T", "SAP Best Practices", "Oracle TDE", "Amazon EBS", "CSV", "Oracle 9i", "Azure Functions", "ShadCN UI", "Sections", "Microsoft Project", "OpenID Connect", "Linux Shell Scripting", "Virtual Network", "Data Profiling", "Amazon DynamoDB", "Insurance", "FullStory", "BrowserStack", "Interactive Voice Response (IVR) testing", "API Gateway", "Azure DevOps", "Test Design", "Elastic Beanstalk", "<PERSON>", "Salesforce Platform Administrator", "Machined Parts Design", "Data Structures", "ASP.NET Core", "AIM Methodology", "Stack Up Analysis", "Data Analysis", "Package locking", "IDoc", "AWS Aurora Postgres", ".NET", "Windows 2007/2008/2010", "AI-powered Automation", "CloudFront", "Enterprise Reporting", "CVS", "Microsoft SQL Server", "AZ-304", "Selenium IDE", "Flat Files", "<PERSON><PERSON>", "Resin", "<PERSON><PERSON><PERSON><PERSON>", "AIF", "Selenium WebDriver", "Spring IOC", "IBM BMP", "SPAU", "OKTA", "Waterfall Methodologies", "Oracle Enterprise Manager", "Data Modeling", "GCP", "Spring JPA", "ASP.NET", "Microsoft BI Stack", "Flask", "Application Insights", "Server-Side Encryption", "Automatic Payment Program (F110)", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "Module Pool Programming", "AWS Glue", "IBM MQ", "Informatica PowerCenter", "AWS SAM", "Django", "Microservices testing", "SAP Financial Accounting (FI)", "JUnits", "ClearCase", "Artificial Intelligence", "Kali Linux", "Terada<PERSON>", "Hybrid Solution Architecture", "Angular Reactive Forms", "Amazon S3", "System Architect", "Financial Analysis", "WLAN", "SAP S/4HANA", "Integration Testing", "Azure Developer", "Excel", "Data Gateway", "Automated Data Entry", "Enhancement Points", "Angular 7", "Active Reports", "LDAP", "Data Intelligence", "Saga Pattern", "Cisco Catalyst 9800 Wireless Controller", "React.js", "Test Execution", ".NET Core", "WPF", "Test Management", "Defect Tracking", "Web API", "SAP Certified Development Associate - SAP Fiori Application Developer", "Oracle Allocations", "Machine Learning", "Data Visualization", "Crystal Reports", "UDP", "CAPM", "PL/SQL", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "Oracle E-Business Suite R12", "Web IDE", "Vite", "Shell Scripting", "ITIL V3 Foundation", "Spring REST", "ActiveMQ", "STATSPACK", "YAML Pipeline", "Sitecore", "REST", "Power Pivot", "C++", "CI/CD", "Data Transforms", "Elastic APM", "Azure Data Factory (ADF)", "Antifactory", "Rule Resolution", "Desktop Application Testing", "HTML", "PMP", "Selenium RC", "AireOS controller", "Azure Container Registry", "Snow SQL", "OAuth2", "Slowly Changing Dimensions (SCD)", "Oracle Database Administrator Certified", "Web Application Automation", "AWS Certified Developer Associate", "CA DevTest", "Financial Reporting", "Ethernet", "Oracle 11g/12c", ".NET Framework 4.5", "Code Review", "Clipboard", "Test Schedule Creation", "React", "MCA", "Smart View", "Process Management", "JSTL", "MathCAD", "UI Testing", "Blue Yonder", "Data Transformation", "Financial Reporting Studio (FRS)", "OpenWRT", "GraphQL", "Angular", "Entity Framework", "SAP Accounts Payable (AP)", "Amazon VPC", "GlassFish", "User Acceptance testing", "Data Integration", "Cost Analysis", "Data Loader", "QMI", "Spring Data", "ADO.NET", "SAP CO", "Data Warehouse testing", "NumPy", "IDMC", "Power Automate", "Cypress", "Team Foundation Server (TFS)", "Operating Systems", "Decision Rules", "JBehave", "Cognito", "Oracle Financials Cloud General Ledger", "Spring MVC", "Informatica 10.2", "Azure Service Fabric", "SQS", "Apache Tomcat", "FI-AR", "Oracle 10g", "NodeJS", "IAM Role Management", "MS Access", "Blueprints", "Database testing", "File-Aid", "ReactJS", "Visual Studio 2010", "Amazon Redshift", "Order to Cash (O2C)", "GitLab", "Real-time Cash Visibility System", "Sub Ledger Accounting (SLA)", "Peoplesoft HCM", "Design Standardization", "Azure Log Analytics", "RMAN", "<PERSON>er", "JMS", "Hyperion FRS", "WebLogic 12c", "Talwar Simulator", "Classification", "CE Marking", "Azure Blob Storage", "Pivotal Cloud Foundry (PCF)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "XAML", "SQL Server 2017", "GraphQL APIs", "<PERSON><PERSON><PERSON>", "SeeTest (Experitest)", "GitHub", "Oracle Cloud Financials", "SAP MM", "Tridion CMS 2009", "Risk Management", "Microsoft Certified Professional", "MS Azure", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Visual Studio 2012", "OLTP", "SAP", "Screen Flows", "Informatica Power Center", "Query Performance Optimization", "Client Management", "Technical Architecture Documentation", "OCI Object Storage", "UML", "Record to Report (R2R)", "AWS", "Amazon Route 53", "Vendor/Customer Open Items", "IOS Testing", "Azure Cloud", "JRockit Mission Control", "AeroScout tags", "Microsoft Power BI", "Oracle 12c", "DFA", "Quality Center", "JDBC", "IBM DB2", "IICS", "JAX-WS", "<PERSON><PERSON>", "Azure Cloud Architectures", "C# 9.0", "Intercompany", "<PERSON><PERSON>", "VSAM", "Quality Management (QM)", "Visual Studio 2022", "Python", "LINQ", "Brainbench C# 5.0", "Summary-View Reports", "Azure Data Lake", "Financial Data Management (FDMEE)", "Application Design", "Autonomous Data Warehouse (ADW)", "Visual Studio 2008", "SAP Materials Management (MM)", "Purchase Order (PO) Management", "<PERSON><PERSON>", "Visual Studio 2013", "Document review", "RESTful APIs", "Google Analytics", "Oracle Cloud Budgetary Control", "SAP Fiori List Report Application", "Azure SQL Database", "ADFS (Active Directory Federation Services)", "AWS Auto Scaling", "Stored Procedures", "Kafka", "Real-time Data Analytics", "Nx Monorepo", "j<PERSON><PERSON><PERSON>", "Business Workflow", "SAFe", "WebLogic 14c", "Oracle 8x", "Sanity Testing", "BDC", "SNS", "Accounts Payable", "Fiori Elements", "YAML Pipelines", "Azure Entra ID", "ATC", "RabbitMQ", "Google Cloud Platform (GCP)", "Confluence", "Oracle Cloud Infrastructure", "<PERSON><PERSON>", "macOS", "Amazon Web Services (AWS)", "DNVGL", "Cisco Access Points", "Azure Kubernetes Service (AKS)", "Open API", "Service Lifting Tool Design", "Data Encryption", "Report Definitions", "Mainframes testing", "WebLogic 11g", "Dynatrace", "Customer Accounting", "Visual Studio 2005", "Visio", "Waterfall", "SQL Server Integration Services (SSIS)", "OCI VCN", "ALE IDOCs", "Unix", "EXPLAIN PLAN", "Data Marts", "NETCONF", "Process Flows", "Coded UI Testing", "ICRT"], "skill_frequency": {"Python": 20, "JavaScript": 14, "Java": 17, "C#": 11, "SQL": 19, ".NET 6": 3, "ASP.NET Core": 7, "ASP.NET MVC": 5, "Angular": 13, "Web API": 9, "Azure": 11, "Azure Functions": 9, "Azure Developer": 3, "Azure Logic Apps": 2, "Azure Service Bus": 5, "Azure API Management": 1, "Azure Storage": 5, "Cosmos DB": 4, "Redis Cache": 3, "Azure Active Directory (Azure AD)": 2, "Azure Virtual Network": 1, "Azure Application Insights": 1, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 5, "Azure Container Registry": 3, "Azure Service Fabric": 3, "Azure Data Lake": 4, "YAML Pipelines": 2, "Docker": 11, "Kubernetes": 10, "CI/CD": 13, "Microservices": 8, "Serverless Architecture": 4, "HTML": 15, "CSS": 9, "jQuery": 11, "Event Grid": 3, "Event Hub": 3, "SQL Server": 15, "MySQL": 13, "Snowflake": 6, "T-SQL": 7, "PL/SQL": 7, "Stored Procedures": 6, "Triggers": 4, "Functions (Database)": 1, "Amazon Web Services (AWS)": 9, "Microsoft Azure": 6, "Agile Methodology": 5, "Design Patterns": 4, "Microservices Architecture": 2, "Federated Database Design": 2, "Container-based Architecture": 2, "High-Throughput System Architecture": 1, "Real-time Data Analytics Solution Architecture": 2, "E-commerce Architecture": 3, "Hybrid Solution Architecture": 2, "VPC Design": 2, "Direct Connect": 2, "VPN": 2, "Query Performance Optimization": 3, "Data Modeling": 10, "Microsoft Certified Professional": 2, "WPF": 2, "MVC": 2, "MS Azure": 2, "WCF": 6, "Blob Storage": 2, "Table Storage": 2, "App Services": 2, "Redis": 4, "App Insights": 2, "Azure APIM": 2, "Logic Apps": 4, "AZ900: Microsoft Azure Fundamentals": 2, "Caliburn.Micro": 2, "Prism": 2, "Entity Framework 7.0": 2, "XML Parser": 2, "LINQ": 4, "Stimulsoft": 2, "Angular Reactive Forms": 2, "HttpClient": 2, "NUnit": 4, "Coded UI Testing": 2, "ADO.NET": 6, "SQL Server Reporting Services (SSRS)": 2, "Strapi CMS": 2, "Windows Services": 3, "WCF RESTful": 2, "MS SQL Server 2019": 2, "PostgreSQL": 10, "SQLite": 2, "Oracle (PL/SQL)": 2, "MS Access": 4, "InstallShield": 2, "GitHub": 10, "TFS": 4, "SVN": 8, "IIS": 2, "Apache Tomcat": 2, "DevExpress": 2, "Brainbench C# 5.0": 2, "MVVM": 1, "ASP.NET": 5, ".NET Framework": 3, "Entity Framework": 4, "EF Core": 2, "Razor View Engine": 2, "Bootstrap": 8, "CosmosDB": 2, "ElasticSearch": 4, "Apache Kafka": 6, "ActiveMQ": 2, "Pivotal Cloud Foundry": 2, "Azure App Service": 3, "Node.js": 8, "React": 5, "OAuth2": 2, "Swagger": 4, "OOPS": 2, "SOLID principles": 3, "Team Foundation Server (TFS)": 2, "Git": 11, "Jira": 15, "Azure DevOps": 11, "Moq": 2, "Agile Methodologies": 2, "SCRUM": 6, "Waterfall Methodologies": 2, "Test-Driven Development (TDD)": 2, "C++": 5, "Service Bus": 2, "API Management": 2, "YAML Pipeline": 1, "Azure AD": 1, "Virtual Network": 2, "Application Insights": 2, "Log Analytics": 2, "Key Vault": 2, "Functions": 2, "Software Architecture": 1, "Micro-services": 1, "High Throughput System Architecture": 1, "Microsoft Azure Certified Professional": 1, "MCA": 1, "Open API": 1, "C": 5, ".NET Core": 4, "AJAX": 3, "AngularJS": 1, "ReactJS": 4, "XML": 6, "HTML5": 4, "Sass": 1, "TypeScript": 7, "JSON": 9, "DynamoDB": 3, "OpenSearch": 1, "EC2": 3, "CloudFront": 1, "IAM": 3, "ECS": 1, "SQS": 2, "SNS": 2, "Lambda": 1, "API Gateway": 3, "RDS": 1, "CloudWatch": 3, "Step Functions": 1, "Elastic Cache": 1, "NodeJS": 2, "AGGrid": 1, "txText Control": 1, "ASPX": 1, "SOAP": 8, "RESTful APIs": 1, "Crystal Reports": 3, "Active Reports": 1, "SSRS": 4, "SSIS": 4, "YAML": 1, "Terraform": 4, "DDD": 1, "TDD": 1, "Agile": 15, "NuGet": 1, "Object-Oriented Programming (OOP)": 2, "VB.NET": 1, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 1, "OpenTelemetry": 1, "FullStory": 1, "Google Analytics": 2, "ASP.NET Core 6.0": 1, "ASP.NET Core 8.0": 1, ".NET MAUI": 1, "XAML": 1, "C# 8.0": 1, "C# 9.0": 1, "C# 10.0": 1, "Web Services": 1, "REST": 4, "Angular 7": 1, "Angular 8": 1, "Angular 9": 1, "Angular 10": 1, "Angular 12": 1, "Material Design": 1, ".NET Framework 2.0": 1, ".NET Framework 3.5": 1, ".NET Framework 4.0": 1, ".NET Framework 4.5": 1, ".NET Framework 4.7": 1, "CI/CD Pipeline": 1, "Splunk": 3, "RabbitMQ": 1, "Amazon DynamoDB": 2, "Kendo UI": 2, "Amazon EC2": 6, "AWS Lambda": 4, "Azure App Services": 1, "WebJobs": 1, "Azure Active Directory": 1, "ServiceNow": 3, "HP Service Manager (HPSM)": 1, "Service-Oriented Architecture (SOA)": 1, "OAuth 2.0": 1, "OKTA": 2, "Azure Entra ID": 1, "Bitbucket": 5, "Team Foundation Server": 1, "Subversion (SVN)": 2, "TortoiseSVN": 2, "Visual Studio 2003": 1, "Visual Studio 2005": 1, "Visual Studio 2008": 1, "Visual Studio 2010": 1, "Visual Studio 2012": 1, "Visual Studio 2013": 1, "Visual Studio 2015": 1, "Visual Studio 2017": 1, "Visual Studio 2019": 1, "Visual Studio 2022": 1, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 2, "OpenID Connect": 1, "Ping Identity": 1, "Salesforce APIs": 1, "CQRS": 1, "Saga Pattern": 1, "Choreography Pattern": 1, "Gateway Aggregation": 1, "Circuit Breaker Pattern": 1, "Message Queue": 1, "MuleSoft": 1, "Kafka": 3, "Tibco": 1, "AKS (Azure Kubernetes Service)": 1, "MVC Design Pattern": 1, "Repository Pattern": 1, "Dependency Inversion Principle": 1, "Dependency Injection": 1, "Factory Pattern": 1, "Abstract Factory Pattern": 1, "Tridion CMS 2009": 1, "Tridion CMS 2011": 1, "Tridion CMS 2013": 1, "Tridion CMS 8.5": 1, "Sitecore": 1, "SEO Optimization": 1, "Omniture": 1, "Google Tag Manager": 1, "SQL Server 2000": 1, "SQL Server 2005": 1, "SQL Server 2008": 1, "SQL Server 2012": 1, "SQL Server 2014": 1, "SQL Server 2017": 1, "Azure SQL Server": 1, "Oracle PL/SQL": 2, "Selenium": 1, "Azure Data Factory": 1, "PMP (Project Management Professional)": 1, "Agile (SCRUM)": 1, "Kanban": 2, "AZ-104": 1, "AZ-204": 1, "AZ-304": 1, "Machine Learning": 3, "Deep Learning": 1, "Predictive Analysis": 1, "Artificial Intelligence": 1, "IoT Systems": 1, ".NET": 2, "gRPC": 1, "SSIS (SQL Server Integration Services)": 1, "SSRS (SQL Server Reporting Services)": 1, "LINQ to SQL": 1, "LINQ to Objects": 1, "Lambda Expressions": 1, "S3 (Amazon S3)": 1, "Amazon Elastic Kubernetes Service (EKS)": 1, "Amazon ECR (Elastic Container Registry)": 1, "Elastic Beanstalk": 2, "Application Load Balancer": 1, "NoSQL": 1, "Datadog": 1, "Azure Container Registry (ACR)": 1, "Azure Kubernetes Service (AKS)": 1, "Azure Blob Storage": 3, "Blazor": 1, "MudBlazor": 1, "Telerik": 1, "Redux": 1, "Hangfire": 1, "ADFS (Active Directory Federation Services)": 1, "Tableau": 8, "DB2": 5, "SAP": 2, "IDoc": 1, "Logility": 1, "Blue Yonder": 1, "CloudFormation": 1, "VPC": 1, "Jenkins": 11, "SonarQube": 2, "Antifactory": 1, "AWS Elastic Kubernetes Service (EKS)": 1, "ANT": 4, "Maven": 12, "Shell Scripting": 3, "Ansible": 2, "PowerShell": 3, "Tomcat": 5, "JBoss": 3, "WebLogic": 3, "WebSphere": 3, "Windows Server": 1, "Red Hat Linux": 1, "Unix": 4, "CentOS": 1, "VMware": 1, "Elastic Load Balancers": 1, "Waterfall": 7, "Batch Scripting": 1, "Amazon ECS": 1, "Amazon S3": 6, "Amazon EBS": 1, "Amazon VPC": 1, "Amazon ELB": 1, "Amazon SNS": 2, "Amazon RDS": 1, "Amazon IAM": 1, "Amazon Route 53": 1, "AWS CloudFormation": 2, "AWS Auto Scaling": 1, "Amazon CloudFront": 1, "Amazon CloudWatch": 2, "AWS CLI": 1, "Vault": 1, "Docker Hub": 1, "Docker Registries": 1, "AWS Kops (EKS)": 1, "Groovy": 1, "GitLab": 4, "Apache": 1, "Grafana": 1, "Pivotal Cloud Foundry (PCF)": 1, "Infrastructure as Code (IaC)": 1, "Configuration Management": 2, "Containerization": 1, "Orchestration": 1, "Build/Release Management": 1, "Source Code Management (SCM)": 1, "HTTP (TLS)": 1, "Key Management": 1, "Encryption": 1, "J2EE": 2, "SAFe": 1, "Confluence": 3, "Microsoft Project": 1, "SmartSheet": 1, "DevOps": 1, "Warehouse Management": 1, "CMMI Level 5": 2, "PMP": 1, "PSM": 1, "Agile Project Management": 1, "Scrum Master": 1, "Program Management": 2, "Project Management": 2, "Project Planning": 2, "Risk Management": 2, "Cost Analysis": 2, "Resource Management": 1, "Stakeholder Management": 1, "Delivery Management": 1, "Client Management": 1, "Release Management": 1, "Microsoft Excel": 1, "Azure Cloud": 2, "Cobol": 4, "Ezetrieves": 1, "IBM BMP": 1, "ISO 27001": 1, "DBT": 1, "AWS": 4, "Azure Data Factory (ADF)": 1, "Databricks": 2, "Database Migration Service": 1, "AWS Glue": 2, "Fivetran": 1, "Snow SQL": 1, "Streamset": 1, "Snowpark": 1, "Column Masking": 1, "Data Encryption": 1, "Data Decryption": 1, "Data Masking": 1, "Data Governance": 1, "Hive": 2, "Pig": 1, "Sqoop": 1, "PySpark": 1, "Sigma": 1, "Apache Airflow": 1, "Informatica Power Center": 1, "Talend": 3, "Peoplesoft FSCM": 1, "Peoplesoft HCM": 1, "Oracle": 6, "MS SQL Server": 2, "OLTP": 2, "OLAP": 2, "Data Warehousing": 7, "Data Architecture": 1, "Data Integration": 3, "ELT": 1, "ETL": 6, "Data Quality": 3, "Real-time Data Ingestion": 1, "Snow Pipe": 1, "Confluent Kafka": 1, "Snowsight": 1, "SQR 6.0": 1, "Avro": 1, "Parquet": 1, "CSV": 2, "Index Design": 1, "Query Plan Optimization": 1, "Data Analysis": 2, "Business Intelligence": 3, "Data Management": 1, "ETL Processes": 1, "Excel": 1, "Power BI": 5, "DAX": 3, "Statistical Analysis": 1, "Regression": 1, "Hypothesis Testing": 1, "Predictive Modeling": 1, "Time Series Forecasting": 1, "Classification": 1, "Data Cleaning": 1, "Data Transformation": 2, "Data Automation": 1, "PivotTables": 1, "Power Query": 3, "Pandas": 1, "NumPy": 1, "SQL Server Integration Services (SSIS)": 1, "R": 1, "Google Data Analytics Professional Certificate": 1, "Getting Started with Power BI": 1, "The Complete Python Developer": 1, "ISTQB Certified Tester Foundation Level": 1, "Informatica PowerCenter": 2, "IICS": 1, "IDMC": 1, "IBM Infosphere DataStage": 1, "SAS Data Integration Studio": 1, "Oracle 11g": 2, "Oracle 10g": 2, "Oracle 9i": 1, "Oracle 8x": 1, "Microsoft SQL Server": 1, "Amazon Redshift": 1, "Data Migration": 2, "Data Modernization": 1, "Data Enrichment": 1, "Data Validation": 1, "Data Processing": 1, "Data Pipelining": 1, "Data Visualization": 2, "Enterprise Reporting": 1, "Dashboarding": 1, "AWS Athena": 1, "AWS Lake Formation": 1, "Microsoft Power BI": 1, "OBIEE": 1, "SAS Visual Investigator": 1, "SAS Visual Analytics": 1, "Erwin Data Modeler": 2, "Sparx Enterprise Architect": 1, "RDBMS": 2, "Star Schema": 4, "Snowflake Schema": 4, "Slowly Changing Dimensions (SCD)": 1, "Normalization": 1, "Flat Files": 1, "Predictive Forecasting": 1, "Alert Management": 1, "Regulatory Reporting": 1, "AML Compliance": 1, "Data Intelligence": 1, "Scenario Assessment": 1, "MIS Management": 1, "MS Excel": 1, "Data Security": 1, "Data Wrangling": 1, "Visual Studio": 1, "Mechanical Product Design": 1, "Mechanical Component Design": 1, "System Integration": 1, "Sheet Metal Design": 1, "Machined Parts Design": 1, "Design Standardization": 1, "Component Localization": 1, "Cost Optimization": 1, "Design Calculations": 1, "Cross-functional Collaboration": 1, "Onshore Rigging Calculations": 1, "Service Lifting Tool Design": 1, "Process Management": 1, "UG-NX": 1, "SolidWorks": 1, "CATIA": 1, "AutoCAD": 1, "ANSYS": 1, "Design FMEA": 1, "DFM": 1, "DFA": 1, "GD&T": 1, "Stack Up Analysis": 1, "ASME Y14.5": 1, "2D Drawing Review": 1, "MathCAD": 1, "CE Marking": 1, "DNVGL": 1, "EN-13155": 1, "Machinery Directive 2006/42/EC": 1, "EN ISO 50308": 1, "EN ISO 14122": 1, "Reverse Engineering": 1, "Informatica Cloud Services (IICS)": 1, "Intelligent Data Management Cloud (IDMC)": 1, "Netezza": 1, "Teradata": 1, "Windows": 5, "Spark": 1, "Data Profiling": 1, "Business 360 Console": 1, "Cloud Data Governance": 1, "Cloud Data Catalog": 1, "Data Marts": 1, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 1, "API": 1, "ICS": 1, "ICRT": 1, "Nifi": 1, "Technical Design Documentation": 1, "Technical Architecture Documentation": 1, "Production Support": 3, "Code Review": 2, "Redux Toolkit": 1, "Axios": 1, "SWR": 1, "Formik": 1, "React Router": 1, "CSS3": 2, "ES6": 1, "Material UI": 1, "Tailwind CSS": 1, "PHP": 1, "Amazon Lambda": 2, "Gulp": 1, "Grunt": 1, "Webpack": 1, "GitHub Copilot": 1, "JWT": 1, "RBAC": 1, "Software Development Life Cycle (SDLC)": 1, "Spring Boot": 5, "Struts 2": 1, "Spring IOC": 1, "Spring MVC": 2, "Spring Data": 1, "Spring REST": 1, "Jersey REST": 1, "JSF": 1, "Apache POI": 1, "iText": 1, "Servlets": 2, "JSP": 2, "JDBC": 2, "JAX-WS": 1, "JAX-RS": 1, "Java Mail": 1, "JMS": 2, "JUnits": 1, "IBM MQ": 1, "Amazon EKS": 1, "Cucumber": 3, "Cypress": 2, "Dojo Toolkit": 1, "MongoDB": 3, "Quartz": 1, "Hibernate": 2, "Spring JPA": 1, "Putty": 1, "WinSCP": 1, "Bamboo": 1, "AWS Aurora Postgres": 1, "EJB": 1, "JSTL": 1, "JPA": 1, "Struts": 1, "Spring Framework": 1, "NestJS": 2, "WebLogic 11g": 1, "GlassFish": 2, "Resin": 1, "Oracle 11g/12c": 1, "IBM DB2": 1, "Eclipse": 1, "NetBeans": 1, "JDeveloper": 1, "IntelliJ": 1, "MyEclipse": 1, "VS Code": 1, "Toad": 5, "Visio": 1, "UML": 1, "CVS": 1, "SoapUI": 3, "JMS Hermes": 1, "JUnit": 8, "Log4j": 1, "JRockit Mission Control": 1, "JMeter": 1, "JRebel": 1, "Spiral": 1, "Prototype": 1, "Google Cloud Platform (GCP)": 2, "ITIL Foundation 2011": 1, "AWS Certified Solutions Architect Associate": 1, "AWS Certified Developer Associate": 1, "AWS Certified SysOps Administrator Associate": 1, "Dynatrace": 1, "LDAP": 1, "SiteMinder": 1, "SAML": 1, "Harvest": 1, "Nx Monorepo": 1, "OOAD": 1, "SOA": 1, "Single Page Application (SPA)": 1, "AWS CDK": 3, "@task": 1, "GitLab Pipelines": 1, "Oracle DBA": 1, "Oracle OCI": 1, "Oracle 19c": 1, "Oracle 12c": 1, "Oracle 21c": 1, "Oracle RAC": 1, "Oracle Data Guard": 1, "Oracle Enterprise Manager": 1, "Oracle TDE": 1, "Data Pump": 1, "Oracle Cloud Infrastructure": 1, "RMAN": 1, "Linux Shell Scripting": 1, "Crontab": 1, "AWR": 1, "ADDM": 1, "EXPLAIN PLAN": 1, "SQL*Trace": 1, "TKPROF": 1, "STATSPACK": 1, "WebLogic 14c": 1, "WebLogic 12c": 1, "JDK": 1, "SQL Server 2016": 1, "Veeam Backup and Recovery": 1, "Red Hat Linux 7": 1, "Red Hat Linux 8": 1, "Exadata": 1, "IBM LTO 9": 1, "IBM LTO 8": 1, "OCI IAM": 1, "OCI VCN": 1, "OCI Object Storage": 1, "OCI Load Balancing": 1, "OCI Auto Scaling": 1, "OCI CDN": 1, "OCI WAF": 1, "Autonomous Data Warehouse (ADW)": 1, "Autonomous Transaction Processing (ATP)": 1, "ITIL V3 Foundation": 1, "Prince2": 1, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 1, "Oracle 19c Database Administrator Training": 1, "Teradata Certified Administrator (V2R5)": 1, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 1, "Oracle E-Business Suite R12": 2, "Oracle Cloud Financials": 1, "Oracle Cloud General Ledger": 1, "Oracle Cloud Accounts Payable": 1, "Oracle Cloud Accounts Receivable": 1, "Oracle Cloud Fixed Assets": 1, "Oracle Cloud Cash Management": 1, "Oracle Cloud I-Expenses": 1, "Oracle Cloud Budgetary Control": 2, "Oracle Financial Accounting Hub": 2, "Oracle Transactional Business Intelligence (OTBI)": 1, "Financial Reporting Studio (FRS)": 1, "Smart View": 1, "Data Loader": 2, "Hyperion FRS": 2, "Business Process Management (BPM)": 2, "AIM Methodology": 2, "OUM Methodology": 2, "Sub Ledger Accounting (SLA)": 2, "Windows 2007/2008/2010": 2, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 2, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 2, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 2, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 2, "BIP": 2, "Pega Rules Process Engine": 1, "Pega Group Benefits Insurance Framework": 1, "Pega Product Builder": 1, "Pega 7.2.2": 1, "Pega 7.3": 1, "Pega 7.4": 1, "Pega 8": 1, "Unit testing": 1, "Harness": 1, "Sections": 1, "Flow Actions": 1, "List-View": 1, "Summary-View Reports": 1, "Report Definitions": 1, "Clipboard": 1, "Tracer": 1, "PLA": 1, "Product locking": 1, "Package locking": 1, "Ruleset locking": 1, "SDLC": 1, "E-Commerce": 1, "Insurance": 1, "Agents": 1, "Queue Processors": 1, "Decision Rules": 1, "Declarative Rules": 1, "Application Design": 1, "Case Management": 1, "Process Flows": 1, "Screen Flows": 1, "Data Transforms": 1, "Activities": 1, "Rule Resolution": 1, "Enterprise Class Structure": 1, "Dev Studio": 1, "App Studio": 1, "Admin Studio": 1, "CDH": 1, "Document review": 1, "Pega Marketing Consultant": 1, "Senior System Architect": 1, "System Architect": 1, "Postman": 4, "Selenium IDE": 2, "Selenium RC": 2, "Selenium WebDriver": 6, "Selenium Grid": 2, "TestNG": 6, "QTP": 2, "Gherkin": 2, "Ruby": 3, "Tortoise SVN": 1, "HP Quality Center": 2, "SeeTest (Experitest)": 1, "ACCELQ": 2, "JBehave": 2, "HP ALM": 3, "BrowserStack": 2, "LambdaTest": 2, "Functional Testing": 4, "Smoke Testing": 2, "System Testing": 4, "Integration Testing": 4, "Regression Testing": 4, "User Acceptance Testing (UAT)": 2, "UI Testing": 3, "Mobile Testing": 4, "Automation Testing": 3, "Web Testing": 2, "Compatibility Testing": 2, "Sanity Testing": 2, "Ad hoc Testing": 2, "Test Case Design": 1, "Test Plan Creation": 3, "Test Scripting": 1, "Test Execution": 2, "Defect Tracking": 2, "Bug Reporting": 1, "Test Management": 3, "AI-powered Automation": 1, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 2, "Android Testing": 2, "SSAS": 2, "Power BI Desktop": 2, "Power BI Service": 2, "M Language": 2, "Dimensional Modeling": 2, "Microsoft BI Stack": 2, "Power Pivot": 2, "Data Gateway": 2, "Row-Level Security (RLS)": 1, "Data Flows": 1, "DataMart": 1, "Power Automate": 2, "Visual Studio Code": 2, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 2, "ASAP Methodology": 2, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 2, "Business Process Mapping": 2, "FIT-GAP Analysis": 2, "Financial Reporting": 2, "SAP Solution Design": 2, "SAP Warehouse Management": 2, "Material Master Data Management": 2, "Procurement Processes": 2, "Order-to-Delivery Process": 1, "Demand Forecasting": 2, "Cash Pooling": 2, "Bank Reconciliation": 2, "F110 Automatic Payment Program": 1, "Real-time Cash Visibility System": 2, "Inhouse Cash Management": 2, "SAP Best Practices": 2, "Generally Accepted Accounting Principles (GAAP)": 2, "International Financial Reporting Standards (IFRS)": 2, "Financial Analysis": 2, "Automated Data Entry": 1, "AR Processing & Reporting": 1, "Customer Accounting": 1, "Vendor/Customer Open Items": 1, "SAP Integration": 1, "SAP S/4HANA": 1, "ABAP": 1, "OData": 1, "SAP UI5": 1, "Fiori": 1, "Fiori Elements": 1, "PI/PO": 1, "AIF": 1, "BRF+": 1, "Business Workflow": 1, "CRM": 1, "Web Dynpro ABAP": 1, "RAP": 1, "BTP": 1, "CAPM": 1, "Procure to Pay (PTP)": 1, "Order to Cash Management (OTC)": 1, "Production Planning (PP)": 1, "Quality Management (QM)": 1, "FI-AP": 1, "FI-AR": 1, "FI-GL (FICO)": 1, "RTR": 1, "SCM": 1, "Product Life Cycle Management (PLM)": 1, "Advanced Planner Optimizer (APO)": 1, "Extended Warehouse Management (EWM)": 1, "Data Dictionary (DDIC)": 1, "Module Pool Programming": 1, "Object-Oriented ABAP (OOABAP)": 1, "RFCs": 1, "BADIs": 1, "BDC": 1, "BAPI": 1, "BP Integrations": 1, "Enhancement Points": 1, "User Exits": 1, "Customer Exits": 1, "ALE IDOCs": 1, "Inbound/Outbound Proxy": 1, "SAP NetWeaver Gateway": 1, "Service Registration": 1, "Service Extension": 1, "CDS Views": 1, "AMDP": 1, "SAP Fiori List Report Application": 1, "Web IDE": 1, "BSP": 1, "SAP Fiori Launchpad": 1, "SAP UI5 Framework": 1, "Business Objects (BO)": 1, "ATC": 1, "SPDD": 1, "SPAU": 1, "SAP Security": 1, "PFTC": 1, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 1, "SAP Certified Development Associate - SAP Fiori Application Developer": 1, "Next.js": 1, "REST APIs": 3, "GraphQL APIs": 1, "AWS SAM": 1, "Apache ECharts": 1, "Cognito": 1, "OIDC": 1, "Mantine UI": 1, "Vite": 1, "MySQL Aurora": 1, "AWS API Gateway": 1, "Styled Components": 1, "Sanity": 1, "Amplify": 1, "ShadCN UI": 1, "Salesforce": 1, "CDL": 1, "Cisco Catalyst 9800 Wireless Controller": 1, "Talwar controller": 1, "AireOS controller": 1, "Cisco Access Points": 1, "Talwar Simulator": 1, "WiFi": 1, "802.11": 1, "WLAN": 1, "Ethernet": 1, "IP": 1, "TCP": 1, "UDP": 1, "CAPWAP": 1, "NETCONF": 1, "YANG": 1, "Swift": 1, "ClearCase": 1, "Cisco catalyst 3750 Switch": 1, "ios-xe asr 1K router": 1, "OpenWRT": 1, "Linux": 3, "QMI": 1, "AT interfaces": 1, "Ubus": 1, "Qualcomm SDX hardware": 1, "AT&T Echo controller": 1, "POLARIS": 1, "GDB": 1, "Gre": 1, "RFID": 1, "AeroScout tags": 1, "Cisco Aironet outdoor mesh access points": 1, "Cisco Prime Infrastructure": 1, "Mac filtering": 1, "Bash": 2, "Android App Development": 2, "Flask": 2, "Django": 2, "GraphQL": 2, "Amazon Web Services": 1, "macOS": 2, "Kali Linux": 2, "OAuth": 4, "AWS Certified Solutions Architect - Associate": 2, "Amazon SQS": 1, "Amazon Athena": 1, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 2, "Test Driven Development (TDD)": 1, "Mockito": 2, "Spark SQL": 1, "Server-Side Encryption": 1, "IAM Role Management": 1, "EKS": 1, "BottleRocket": 1, "React.js": 1, "Firebase Cloud Services": 1, "Cassandra": 1, "Android Studio": 1, "Bluetooth": 1, "Java Development": 1, "Advanced Java Development": 1, "Salesforce Platform Administrator": 1, "Salesforce Platform Developer": 1, "Appium": 2, "Perfecto": 2, "SeeTest": 2, "REST Assured": 2, "Karate Framework": 2, "UFT": 2, "LeanFT": 2, "Zephyr": 1, "Quality Center": 1, "Informatica 10.2": 2, "MicroStrategy": 2, "CICS": 2, "JCL": 2, "VSAM": 2, "Sufi": 2, "File-Aid": 2, "CA DevTest": 2, "ATOM": 1, "GCP": 2, "SSO": 2, "Test Strategy": 1, "Test Design": 1, "Test effort estimation": 1, "Requirements mapping": 1, "Risk-based testing": 2, "End-to-End testing": 2, "User Acceptance testing": 2, "Database testing": 2, "API testing": 2, "Web services testing": 2, "Microservices testing": 1, "Browser compatibility testing": 2, "Exploratory testing": 2, "ETL testing": 2, "Data Warehouse testing": 2, "Interactive Voice Response (IVR) testing": 1, "Customer Telephony Integration (CTI) testing": 1, "Mainframes testing": 1, "Service Virtualization": 2, "Continuous Integration and Continuous Deployment (CI/CD)": 1, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 1, "Oracle Financials Cloud Accounts Payable": 1, "Accounts Payable": 1, "Accounts Receivable": 1, "General Ledger": 1, "Fixed Assets": 1, "Cash Management": 1, "I-Expenses": 1, "I-Receivables": 1, "Order Management": 1, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 1, "Order to Cash (O2C)": 1, "Record to Report (R2R)": 1, "Oracle Allocations": 1, "Oracle Cloud": 1, "Intercompany": 1, "SeeTest/Experitest": 1, "Test Case Development": 1, "Test Schedule Creation": 1, "SAP Financial Accounting (FI)": 1, "SAP Controlling (CO)": 1, "SAP Sales and Distribution (SD)": 1, "SAP Materials Management (MM)": 1, "Hyperion Financial Management (HFM)": 1, "Financial Data Management (FDMEE)": 1, "Profit Center Accounting (PCA)": 1, "SAP Accounts Receivable (AR)": 1, "SAP Accounts Payable (AP)": 1, "General Ledger (GL)": 1, "Purchase Order (PO) Management": 1, "Inventory Planning": 1, "Automatic Payment Program (F110)": 1, "Network Security": 1, "Object Oriented Programming": 1, "Operating Systems": 1, "Design and Analysis of Algorithms": 1, "DBMS": 1, "Mainframe Testing": 1, "Performance Testing": 1, "User Interface Testing": 1, "Manual Testing": 1, "Mobile Web Testing": 1, "Desktop Application Testing": 1, "Web Application Testing": 1, "Data Analytics": 1, "Real-time Data Analytics": 1, "NoSQL Databases": 1, "Blueprints": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>"], "Lambda": ["PunniyaKodi V updated resume"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>"], "Step Functions": ["PunniyaKodi V updated resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure App Services": ["Chary"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary"], "Saga Pattern": ["Chary"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume"], "Single Page Application (SPA)": ["Chandra_Resume"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"]}, "skill_categories": {}, "skill_metadata": {}}