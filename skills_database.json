{"all_skills": ["JUnit", "Design FMEA", "HTML", "Mechanical Product Design", "A<PERSON> (EKS)", "Cross-functional Collaboration", "<PERSON> (GL)", "Microsoft Azure", "IBM LTO 9", "Data Automation", "Browser compatibility testing", "OTBI", "Cascading Filters", "Prototype Model", "<PERSON><PERSON>", "AT&T Echo controller", "GDB", "Styled Components", "Intercompany", "Artificial Intelligence", "CA DevTest", "Generally Accepted Accounting Principles (GAAP)", "Salesforce Platform Administrator", "Test Planning", "Firebase Cloud Services", "SAP Financial Accounting (FI)", "WLAN", "IDMC", "Change Management", "LED Manager", "JSF", "Power BI Service", "VB.NET", "Oracle Enterprise Manager (OEM)", "Informatica Power Center", "Data Transformation", "Tortoise SVN", "CSS3", "macOS", "Quality Center", "Container-based Architecture", "MS Excel", "Kafka", "AWS Auto Scaling", "Production Support (L3)", "Microsoft Project (MPP)", "RAP", "SSO", "Data Capture (CDC)", "Microservices testing", "Amazon VPC", "MVVM", "JPA", "Oracle Financial Accounting Hub", "SeeTest", "TFS", "System Testing", "Account Receivables", "Queue Processors", "ITIL Foundation", "Angular Reactive Forms", "React", "Kendo UI", "Security Groups", "Data Collection", "AireOS controller", "TortoiseSVN", "AWS Active Directory", "Amazon CloudFront", "Microsoft Office", "Object-Oriented ABAP (OOABAP)", "GitHub Copilot", "IBM LTO 8", "Tridion CMS 2009", "ServiceNow", "Multi-AZ VPC", "KPI Development", "UML", "IP", "Machinery Directive 2006/42/EC", "NoSQL Databases", "Oracle Enterprise Manager Grid Control 11g", "Team Foundation Server (TFS)", "j<PERSON><PERSON><PERSON>", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "RDS", "OCI Load Balancing", "WCF", "Python Worksheet", "ATOM (Mainframes/AS400 automation tool)", "Ezetrieves", "Test Coverage", "Database Migration Service", "Data Enrichment", "Amazon Elastic Kubernetes Service (EKS)", "Domain Driven Design (DDD)", "Query Optimization", "Informatica PowerCenter", "MyEclipse", "Visual Studio", "SAP Fiori Launchpad", "ASP", "ASPX", "SAP Material Master Data Management", "ShadCN UI", "Demand Forecasting", "Mainframe Testing", "Data Marts", "Scalability", "Peoplesoft Supply Chain Management", "SAP Scripts", "AWS CDK", "Windows Services", "Oracle Financials Cloud Accounts Payable", "AWS Certified Solutions Architect - Associate", "SAP Profit Center Accounting (PCBS)", "XML Web Services", "Dev Studio", "Java Mail", "SAP Procurement Processes", "DAX", "OTT", "Data Mining", "Oracle Fusion Financials", "Cash Discount Management", "C++", "Azure Tables", "JDBC", "<PERSON><PERSON>", "DB2 8.1", "<PERSON><PERSON><PERSON>", "SAP SD", "NUnit", "Quality Management (QM)", "Oracle 19c Database Administrator Training", "WebLogic 11g", "Process Management", "ETL Processes", "Eclipse", "Network Security", "JRockit Mission Control", "JAX-WS", "Data Compliance", "Index Design", "Data Dictionary (DDIC)", "Google Analytics", "API Management", "Prototype", "Netezza", "Requirements Prioritization", "SmartSheet", "Scenario Assessment", "Choreography", "MS Unit Test", "Web Jobs", "EJB", "Oracle 19c RAC", "Oracle 19c Database Administrator Training from Koenig Database Administration", "Kubernetes", "Object-Oriented Programming", "Oracle 11i", "AI", "Toad", "MVC Design Pattern", "SQL Server 2017", "Predictive Modeling", "OCI IAM", "Decision Rules", "Microsoft BI Stack", "SSAS", "AP/AR Transactions", "AWS Aurora Postgres", "El<PERSON>", "I-Receivables", "BPPs (Business Process Procedures) Documentation", "Pig", "ElasticSearch", "Visual Studio Code", "ATOM", "Order to Cash Management (OTC)", "ADO.NET", "JCL", "AWS Glue", "Oracle 19c", "IntelliJ", "Web IDE", "Snowflake", "Test Scripting", "ALV Reports", "SQLTrace", "Choreography Pattern", "Amazon EKS", "Azure Container Storage", "Spark", "JAX-RS", "Defect Tracking", "C", "Report Development", "JBehave", "S3 (Amazon S3)", "OCI AutoScaling", "Ethernet", "Advanced Planner Optimizer (APO)", "Oracle Real Application Cluster", "Automated Data Entry", "Solution Architecture", "Oracle 9i", "Power Pivot", "OLTP", "RDBMS", "REST Assured", "Oracle DBA", "System Architect (Certification)", "Cash Pooling", "SharePoint", "Informatica Data Management Center (IDMC)", "<PERSON><PERSON><PERSON>", "General <PERSON><PERSON>", "Log4j", "Git", "SWR", "Inventory Planning", "SAP NetWeaver Gateway", "Data Integration", "Model-View-Controller (MVC)", "Resin", "CTI Testing", "Pareto Charts", "Drill Through", "Python Scripting", "SAP MM Functionalities", "Test effort estimation", "Neb<PERSON>", "Web Services", "SAP Security", "Power Query", "NetBeans", "Factory Pattern", "<PERSON><PERSON>", "Subversion (SVN)", "API Development (ICS, ICRT)", "Smart View", "Technical Design", "Cloud Data Governance", "Salesforce APIs", "OCI-Oracle Cloud Infrastructure Foundations Associate", "SSIS", "Bitbucket", "SolidWorks", "Slowly Changing Dimensions (SCD)", "Onshore Rigging Calculations", "Service Registration", "AWS Storage", "AWS CloudFormation", "UAT (User Acceptance Testing)", "Windows Server", "Orchestration", "Amazon ELB", "FinCrime", "Data Security and Compliance", "RFCs", "ITIL V3 Foundation", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "Nx Monorepo", "Pega 8", "Financial Reporting Studio (FRS)", "Oracle 11g", "Windows 2007/2008/2010", "Hyperion FRS", "MongoDB", "Cost Analysis", "User Exits", "<PERSON><PERSON>", "E-commerce Architecture Design", "DevOps", "Clipboard", "Project Planning", "Data Cleaning", "Selenium WebDriver", "Spring REST", "Transportable Tablespaces", "S3", "Microsoft Power BI", "Red Hat Linux 8", "Application Design", "AKS", "Risk-based testing", "Azure SQL Server", "SQL Server Integration Services (SSIS)", "Classification", "CloudFront", "Circuit Breaker", "NumPy", "System Integration", "Advanced Java Development (Certification)", "Microsoft Azure Certified Professional", "<PERSON><PERSON> (Project Management Professional)", "PMP", "SQL Server Data Tools", "SCRUM", "Agile Project Management", "ANSYS", "DBMS", "Hybrid Solution Architecture", "MS SQL Server 2019", "Ad-hoc Testing", "Mechanical Component Design", "HP Quality Center", "Organizational Change Management (OCM)", "Module Pool Programming", ".NET Framework", "Firehose", "Data Loader", "Data Governance", "Dimension Tables", "SAP CO", "Angular", "BrowserStack", "Informatica Intelligent Cloud Services (IICS)", "Appium", "System Architect", "Gre", "Harvest", "<PERSON><PERSON>", "Azure Monitor", "Agile (SCRUM)", "FI-GL (FICO)", "Risk Management", "Lake Formation", "Abstract Factory Design Pattern", "Test Execution", "Omniture", "SPAU", "Sections", "Test Plan Creation", "VS Code", "Agile Transformation", "Dependency Inversion Principle", "Unix Shell Scripting", "Enhancement Points", "HP", "Exadata CS DBX7", "Procure to Pay (PTP)", "Tracer", "AMDP", "OOAD", "Object Storage", "XML Parser", "Waterfall", "React Router", "Customer Exits", "Predictive Forecasting", "ETL Development", "Oracle 12c", "Azure Storage", "Compatibility Testing", "AWS API Gateway", "Flask", "Amplify", "SAP UI5 Framework", "Oracle RAC", "<PERSON>adata", "OCI Auto Scaling", "AR Processing & Reporting", "XAML", "Spring Data", "Service Extension", "VCN", "JDeveloper", "Stack Up Analysis", "Angular 9", "User Interface Testing", "ASP.NET MVC", "Dispute and Deduction Management", "Agile Methodologies", "SOA", "Spring JPA", "Struts 2", "Case Management", "SAS Data Integration Studio", "Cisco Prime Infrastructure", "Amazon Elastic Container Registry (ECR)", "Windows Service", "Entity Framework 7.0", "Component-Based Architecture", "Source Code Management (SCM)", "Sparx Enterprise Architect", "Build.xml", "SAP AP/AR Transactions", "Next.js", "Azure Container Registry (ACR)", "Amazon Web Services", "Amazon Glue", "T-SQL", "ReactJS", "<PERSON>", "Oracle Database Administrator Certified", "Query Plan Optimization", "SAP Solution Design", "Service Level Agreement (SLAs)", "Subversion", "Redis", "Blueprints", "Dojo", "R", "Order-to-Delivery Process Optimization", "SQL Server 2012", "SQL Server 2000", "JWT", "User Acceptance testing", "Domain Driven Design", "IBM DB2", "iText", "HP/IBM Power E850", "Column <PERSON>", "Business Workflow", "<PERSON><PERSON>", "802.11", "Server-Side Encryption", "Gulp", "Pivotal Cloud Foundry", "SoapUI", "SAP FI-GL Transactions", "AWS Step Functions", "Microsoft .NET", "AWS", "gRPC", "Azure SQL Database", "ISO/IEC 27001", "J#", "Django", "Event Grid", "Apache", "TDD", "Pega Marketing Consultant (Certification)", "Windows Application Migration", "Data Structures", "Dimensional Modeling", "Query Performance Optimization", "SQL Server 2014", "Ad hoc Testing", "Auto-scaling", "Cloud Migration", "Vite", "<PERSON><PERSON><PERSON>", "TestNG", "Oracle Cloud", "FRS", "ECS", "SQLite", "Azure", "Web API", "Business 360 Console", "SAP Materials Management (MM)", "Design Standardization", "AWS Lake Formation", "IBM MQ", "Key Management", "Cosmos DB", "Real-time Data Analytics", "Azure Blueprints", "Cloud Data Catalog", "FullStory", "Test Schedule Creation", "Windows Application", "Azure Application Insights", "Tailwind CSS", "Alert Management", "PCI", "Oracle Cloud Accounts Payable", "Flow Actions", "Object Oriented Programming", "Client Management", "AWR", "Mobile Web Testing", "Azure SQL", "Oracle 21c", "Windows", "OOPS", "Spiral Model", "NestJS", "Selenium IDE", "Federated Database Design", "Cost Optimization", "AT interfaces", "OAuth Testing", "SAP Best Practices", "Functional Testing", "CAPM", "GAAP (Generally Accepted Accounting Principles)", "Oracle Financials Cloud General Ledger", "SQL Server 2016", "Wix", "Advanced Analytics", "CentOS", "Data Mart", "Swift", "Data Modeling", "Regression Testing", "Application Load Balancer", "GlassFish", "Snowpipe", "Software Development Life Cycle (SDLC)", "JSON", "2D Drawing Review", "Salesforce Platform Administrator (Certification - In process)", "Real-Time Data Analytics Solution Architecture Design", "Functions (Database)", "Oracle Autonomous Data Warehouse (ADW)", "SOLID principles", "Oracle Autonomous Transaction Processing (ATP)", "Angular 7", "Database Performance Tuning", "Smart Forms", "RICEF", "Test Case Design", "Selenium", "Angular 8", "Data Warehouse testing", "Machined Parts Design", "Container-based Architecture Design", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "CMMI Level 5", "BIP", "Oracle Cloud I-Expenses", "PMO", "Spark SQL", "Product Assortment Management", "Oracle Cloud I-Receivables", "<PERSON><PERSON>", "Time Series Forecasting", "Stimulsoft", "Oracle E-Business Suite R12", "Rally", "CE Marking", "Blue Yonder", "EXPLAIN PLAN", "Ruleset locking", "Test Case Development", "BPP Documentation", "Terada<PERSON>", "Groovy", "IIS", "Warehouse Management", "Oracle Cloud Fixed Assets", "Containerization", "Senior System Architect", "M Language", "GraphQL APIs", "SAP Fiori List Report Application", "Oracle 8x", "TCP", "Azure Queues", "ADDM", "AWS Certified Solutions Architect Associate", "Angular 12", "Azure Active Directory", "AI-powered Automation", "General Accepted Accounting Principles (GAAP)", "Test-Driven Development (TDD)", "Amazon ECR", "GCP", "Salesforce", "Infrastructure as Code (IaC)", "Purchase Order (PO) Management", "Database Migration", "V<PERSON>am Backup and Recovery", "BSP", "Design Calculations", "SSRS", "Pricing Strategies", "Team Foundation Server", "Confluence", "Azure Service Bus", "Continuous Integration and Continuous Deployment (CI/CD)", "List-View", "Selenium Grid", "LambdaTest", "Cobol", "Dimension Modeling", "Financial Data Management (FDMEE)", "DFA", "Mantine UI", "Oracle GoldenGate (implied)", "Red Hat Linux", "Tridion CMS 2013", ".NET Core Apps", "Microsoft Certified Professional", "Quartz", "SAP", "Erro<PERSON>", "Star Schema", "Data Visualization", "C# Programming Certified Professional", "Windows Server 2016", "Amazon SQS", "Athena", "Data Decryption", "VPN", "<PERSON>", "ASME Y14.5", "STATSPACK", "BP Integrations", "OLAP", "Mechanical Design", "Microsoft Excel Spreadsheets", "Ubus", "Business Process Mapping", "ASP.NET Core 6.0", "Tibco", "OUM Methodology", "SSRS (SQL Server Reporting Services)", "LINQ", "E-commerce Architecture", "DBT", "Dashboards", "Data Modernization", "RESTful APIs", "SSIS (SQL Server Integration Services)", "EN ISO 50308", "Event-Driven Architecture", "Pega Group Benefits Insurance Framework", "Direct Connect", "Real-time Data Integration", "SAP Warehouse Management", "Android App Development", "SQR 6.0", "Data Profiling", "CosmosDB", "<PERSON> Data Modeler", "AWS Aurora", "ASAP Methodology", "Spring IOC", "Circuit Breaker <PERSON>", "FI-GL Transactions", "Amazon Athena", "Report Definitions", "WebSphere", "PostgreSQL", "<PERSON><PERSON>", "Factory Design Pattern", "Java Development", "Sitecore", "PySpark", "DevExpress", "DWH", "Snowsight", "Real-time Cash Visibility System", "GitLab", "Functions", "SQL Server 2008", "Trifacta", "Silverlight", "Repository Design Pattern", "RabbitMQ", "<PERSON><PERSON><PERSON><PERSON> (implied)", "Snowpark", "OpenWRT", "ADFS", "<PERSON><PERSON>", "Oracle 19c Database Administrator", "Data Gateway", "Regression", "Visual Studio 2022", "Agile", "MS SharePoint Server", "Mainframes testing", "SAP Integration", "Amazon Lambda", "Open API", "FI-AP", "Data Encryption", "OCI CDN", "OAuth", "IOS Testing", "PII", "AWS Certified SysOps Administrator Associate", "Delivery Management", "Oracle Grid Control 11g", "Disaster Recovery", "Single Page Application (SPA)", "SmartView", "SAS Visual Analytics", "Data Analysis", "Visual Studio 2005", "Message Queue", "Test Design", "User Acceptance Testing (UAT)", "IDoc", "Profit Center Accounting (PCA)", "UFT", "Hyperion Financial Management (HFM)", "Key Performance Indicators (KPIs)", "IFRS (International Financial Reporting Standards)", "Technical Architecture Documentation", "SQL*Trace", "Active Directory", "CSV", "Data Flows", "JUnits", "Databricks", "iTech Sharp", "Waterfall Charts", "UG-NX", "AWS (Amazon Web Services)", "AWS Certified Developer Associate", "Cypress", "Procure to Pay (P2P)", "Encryption", "Record to Report (R2R)", "CDS Annotations", "Design Patterns", "Redux Toolkit", "ACCELQ", "Oracle Cloud Cash Management", "CATIA", "Automatic Payment Program (F110)", "Web Dynpro ABAP", "Analytical Applications", "Azure API Management", "SAP MM", "Exadata X9M-2", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "CDN", "Moq", "WPF", "Cisco Aironet", "Qualcomm SDX hardware", "Multi-AZ", "Strategic Planning", "Product locking", "DB2", "Customer Accounting", "Document review", "Dashboarding", "IBM BMP", "Apache Airflow", "Production Support", "Fact Tables", "SAS Visual Investigator", "Active Reports", "DataMart", "Datadog", "Power BI", "International Financial Reporting Standards (IFRS)", "WebLogic", "Data Flow Architectures", "RFID", "<PERSON><PERSON><PERSON>", "SAP Profit Center Accounting (PCA)", "ETL", "SNS", "OData", "OAuth2", "High Availability", "Oracle", "MySQL Aurora", "Data Storage Strategies", "WinSCP", "Logility", "Test Management", "AGGrid", "iOS", "Data Migration", "Android Testing", "Regulatory Reporting", "Extended Warehouse Management (EWM)", "Apache Kafka", "CVS", "Cloud Data Integration", "<PERSON><PERSON><PERSON>", "SSO Testing", "SQL Server 2005", "SLA Development", "Fault Tolerance", "Entity Framework", "Material Master Data Management", "Avro", "EC2", "Test Case Execution", "ITIL Foundation 2011", "High Throughput System Architecture Design", "Test Script Development", "EKS", "Logic App", "OpenTelemetry", "Salesforce Platform Developer", "PCBS (Profit Center Budgeting System)", "Visual Studio 2013", "Red Hat Linux Enterprise 8", "Inhouse Cash Management", "Fivetran", "Row-Level Security (RLS)", "Azure Data Factory (ADF)", "Azure Logic Apps", "Spring", "Business Process Optimization", "BRF+", "Sigma", "QTP", "Spring Boot", "API", "Continuous Deployment", "Stored Procedures", "End-to-End testing", "CQRS", "C#", "Bootstrap", "IoT", "Azure Service Fabric", "Test Plan Development", "Antifactory", "Cognito", "Google Tag Manager", "Amazon EC2", "SAP FI", "Materialized Views", "IVR Testing", "ISTQB Certified Tester Foundation Level", "<PERSON>er", "ATC", "ALE IDOCs", "Microsoft SQL Server", "2D Drawings Review", "Operating Systems", "Visual Studio 2019", "OpenSearch", "Real-time Data Ingestion", "Elastic Beanstalk", "SeeTest (Experitest)", "ICS", "AWS-Kops (EKS)", "ASP.NET Core 8.0", "<PERSON><PERSON><PERSON>ber", "Snowf<PERSON>a", "AIF", "JavaScript", "Winforms", "Oracle Cloud Accounts Receivable", "Snowpark API", "Red Hat Linux 7", "The Complete Python Developer", "Performance Tuning", "Client-server applications", "Azure App Services", "Coded UI Testing", "Amazon CloudFormation", "Test Automation", "Apache Tomcat", "POLARIS", "Azure DevOps", "Visio", "E-Commerce", "AWS Certified SysOps Administrator - Associate", "DynamoDB", "Pega Rules Process Engine", "PCBS", "Kali Linux", "DNVGL", "Informatica 10.2", "XML", "C# 8.0", "REST", "Database Partitioning", "Package locking", "Domain Driven Development (DDD)", "ICRT", "Data Wrangling", "Certified Professional Data Engineer", "Build/Release Management", "Test Estimation", "Azure Data Lake", "Integration Testing", "HP/IBM Power E980", "WebLogic 12c", "SAP AR Processing & Reporting", "Node.js", ".NET MAUI", "Azure Container Registry", "Amazon IAM", "Data Analytics", "React.js", "Amazon ECS", "Amazon Web Services (S3, EC2, Lambda)", "Shell Scripting", "OCI VCN", "Production Planning (PP)", "Azure Blob Storage", "UI Testing", "ISO 27000", "Azure Data Factory", "Mac filter configuration", "Oracle 19c Data Guard", "Indexing", "Java Development (Certification)", "Pega 7.2.2", "Release Management", "Fixed Assets", "Virtual Network", "Hypothesis Testing", "Apache ECharts", "Step Functions", "IAM Role Management", "CloudWatch", "Product Validation", "GitLab Pipelines", "AIM Methodology", "MS Access", "NuGet", "Business Intelligence Publisher (BIP)", "Confluent <PERSON><PERSON><PERSON>", "Regression Analysis", "Log Analytics", ".NET Framework 4.7", "Drill Down", "Oracle 8i/11i", "VMware", "Crystal Reports", "Financial Reporting", "Azure Blobs", "SCM", "Custom Visuals (Power BI)", "Strapi CMS", "Android Studio", "AJAX", "Stakeholder Management", "Internet of Things (IoT)", "High Throughput System Architecture", "Data Pipelines", "SDLC", "Caliburn.Micro", "High-Throughput System Architecture", "AWS Athena", "Parameters", "Agile Methodology", "EN-13155", "Business Objects (BO)", "OpenID Connect", "SonarQube", "Sufi", "HP Service Manager (HPSM)", "LTE", "Service Lifting Tools Design", "Performance Testing", "Azure AD", "Configuration Management", "AG Grid", "SiteMinder", "AWS Storage Services", "Oracle 11g/12c", "Snow SQL", "Pub/Sub", "WebJobs", "PowerShell", "Triggers", "Spring Framework", "Saga", "API testing", "PI/PO", "SQL", "Server-Side Encryption (S3)", "MathCAD", "Informatica Data Management Cloud (IDMC)", "Service Bus", "SEO", "AWS CLI", "MicroStrategy", "Oracle Fusion Applications", "GraphQL", "<PERSON>", "Dimensions", "JSP", "SAML", "Backup and Recovery", "ActiveMQ", "OAuth 2.0", "Mesh Networking", "Azure Virtual Network", "<PERSON><PERSON>", "Data Validation", "NETCONF", "Snow Pipe", "<PERSON><PERSON>xing", "DM", "Coded UI", "Reports", "Autonomous Transaction Processing (ATP)", "MS Azure", "Azure App Service", "BDC", "Teradata Certified Administrator (V2R5)", "TypeScript", "Artifactory", ".NET Core 6.0", "Order Management", "QMI", "Design and Analysis of Algorithms", "5G", "Amazon DynamoDB", "WRICEF Documentation", "Cisco catalyst 3750 Switch", "Activities", "<PERSON><PERSON><PERSON>", "ClearCase", "Application Insights", "LeanFT", "Oracle Database Administration", "SAP Controlling (CO)", "Physical Database Design", "Material UI", "Scrum Master", "ANT", "Microsoft Project", "Database Tuning", "AZ-204", "Visual Studio 2015", "SQL*Plus", "OCI - Oracle Cloud Infrastructure Foundations Associate", "RESTful Web Services", "Peoplesoft HCM", "App Services", "Scalable architecture design", "Tomcat", "Flat Files", "Pega 7.4", "Telerik", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Data Quality", "AZ-104", "<PERSON><PERSON>", "Billing", "Data Pump", "ABAP", "UDP", "CSS", "CDS Views", "Azure Cloud Architectures", "WebLogic 14c", "Business 360", "Continuous Integration", "Azure Log Analytics", "OCA-Oracle Database Administrator Certified", "WAF", "Amazon CloudWatch", "SSMS", "Power BI Desktop", "Blob Storage", "Mac filtering", "AML Compliance", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "Observable", "J2EE", "Insurance", "AZ900: Microsoft Azure Fundamentals", "Servlets", "Oracle TDE", "Test Driven Development (TDD)", "Machine Learning", "RBAC", "Test Strategy", "Lambda", "Spring MVC", "PSM", "Declarative Rules", "CAPWAP", "Program Management", "Accounts Receivable", "Amazon S3", "HP ALM", "SAP Sales and Distribution (SD)", "I-Expenses", "App Insights", "Amazon RDS", "Waterfall Methodologies", "Tibco Messaging", "Azure Kubernetes Service (AKS)", "RACI Matrix", "Oracle Cloud General Ledger", "<PERSON><PERSON><PERSON>", "WAR file deployment", "Technical Design Documentation", "JDK", "WebIDE", "<PERSON><PERSON>", "Oracle 10g", "Android", "Automated Bank Reconciliation", "Postman", "Oracle Data Guard", "Cloud Application Integration", "File-Aid", "AWS SAM", "MySQL", "Micro-services", "txText Control", "Continuous Integration/Continuous Deployment (CI/CD)", "Blazor", "Streamset", "Hibernate", "LINQ to Objects", "GL", "Test Driven Development", "Apache POI", "Terraform", "Intelligent Data Management Cloud (IDMC)", "PivotTables", "Relational Databases", "Unix", "SAP Certified Development Associate - SAP Fiori Application Developer", "Mobile Application Automation", "Sanity", "RICEF objects", "Pivotal Cloud Foundry (PCF)", "SVN", "Visual Studio 2012", "BAPI", "VSAM", "Data Masking", "Requirement Gathering", "Redux", "Service-Oriented Architecture (SOA)", "Azure Developer", "Vendor/Customer Open Item Management", "REST APIs", "<PERSON><PERSON><PERSON><PERSON>", "Oracle (PL/SQL)", "JSTL", "Talend", "<PERSON><PERSON>", "Hangfire", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "Web Testing", "Statistical Analysis", "Normalization", "Real-time Data Analytics Solution Architecture", "App Studio", "IBM Power E850", "YAML Pipeline", "Web Application Testing", "Agents", "HttpClient", "Data Processing", "AKS (Azure Kubernetes Service)", "WiFi", "<PERSON>", "Pega 8.4.1", "Sass", "Sheet Metal Design", "SAP S/4HANA", "MS SQL Server", "PHP", "ASP.NET", "Power Automate", "DFM", ".NET", "Oracle 8i", "Procurement Processes", "Amazon Web Services (AWS)", "OCI WAF", "Cisco Aironet outdoor mesh access points", ".NET Framework 2.0", "Advanced Java Development", "Webpack", "Test Strategy Creation", "SAP Accounts Receivable (AR)", "Supply Chain Management", "JMeter", "Manual Testing", "Azure Key Vault", "C# 10.0", "YAML", "AngularJS", "ETL Design", "OCI Object Storage", "Talwar Simulator", "Prince2", "ISO 27001", "Data Architecture", "Data Transforms", "BTP", "Reverse Engineering", "Defect Management", "Sub Ledger Accounting (SLA)", "Material Design", "Pa<PERSON><PERSON>", "Summary-View Reports", "Financial Analysis", "Oracle Cloud Infrastructure", "Cisco Access Points", "Domain-Driven Design (DDD)", "Business Intelligence", "CDL", "Event Hub", "Data Warehousing", "Talwar controller", "Cisco Catalyst 9800 Wireless Controller", "VPC Design", "Bamboo", "Microsoft Excel", "Microservices Architecture", "Azure Storage Services", "High-Performance Architecture Design", "LINQ to SQL", "Bluetooth", "<PERSON><PERSON>", "NoSQL", "Razor View Engine", "HTTP", "Java", "Dependency Injection", "RMAN", "<PERSON>aud Detection", "Database testing", "FIT-GAP Analysis", "SAP Accounts Payable (AP)", "Backend for Frontend (BFF)", "RTR", "Service Virtualization", "Data Intelligence", "YANG", "Product Life Cycle Management (PLM)", ".NET Framework 4.0", "Cash Management", "ASP.NET Core", "Data Collection and Storage", "IBM Power E980", "Oracle Cloud Financials", "Data Security", "complex join queries", "Java JDK", "SPDD", "Microservices", "ES6", "ETL testing", "Interactive Voice Response (IVR) testing", "Tridion CMS 2011", "Mobile Testing", "Oracle PL/SQL", "Pega Marketing Consultant", "ISO 20000", "Azure Cloud", "Oracle Database (11g, 10g, 9i, 8x)", "HTTP (TLS)", "MuleSoft", "F110 Automatic Payment Program", "Cash Management Integration", "Maven <PERSON>", "Amazon Auto Scaling", "Python", "<PERSON><PERSON><PERSON><PERSON>", "CI/CD", "Azure Functions", "Data Management", "Virtualization", "Red Hat Linux Enterprise 7", "Process Flows", "Certified SAP Functional Consultant", "CRM", "AZ-304", "ADFS (Active Directory Federation Services)", "Smoke Testing", "SAP Cash Management (CM)", ".NET Framework 4.5", "JMS Hermes", "Oracle Cloud Budgetary Control", "Docker Registries", "Azure Pipelines", "Accounts Payable", "Selenium RC", "PL/SQL", "BADIs", "<PERSON><PERSON><PERSON>", "AWS S3", "Brainbench C# 5.0", "IntelliJ IDEA", "Ansible", "AWS Certified Developer - Associate", "Oracle Allocations", "<PERSON><PERSON><PERSON>", "VPC", "Gateway Aggregation", "SQL Server Reporting Services (SSRS)", "Azure APIM", "Tridion CMS", "Prism", "Project Management", "InstallShield", "Bank Reconciliation", "Oracle Export/Import", "Service Lifting Tool Design", "Multi-tier Distributed Applications", "Tridion CMS 8.5", "Component Localization", "JDK 17", ".NET Core", "Senior System Architect (Certification)", "Vendor/Customer Open Items", "ELT", "Object-Oriented Programming (OOP)", "HTML5", "FI-AR", "Code Review", "C# 9.0", "Deep Learning", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Software Development Lifecycle (SDLC)", "Order to Cash (O2C)", "Customer Telephony Integration (CTI) testing", "DDD", "Ka<PERSON><PERSON>", "A<PERSON>os", "Autonomous Data Warehouse (ADW)", "Putty", "Admin Studio", "Exploratory testing", "Amazon EBS", "Device Drivers", "FDMEE", "IBM Infosphere DataStage", "N-tier applications", "Crontab", "Pega 7.3", "Hive", "WCF RESTful", "SeeTest/Experitest", "Business Process Documentation", "Automation Testing", "Business Process Management (BPM)", "<PERSON><PERSON>", "JMS", "Jersey REST", "GD&T", "SAFe", "Software Architecture", "MIS Management", "Lambda Expressions", "GitHub", "Interactive Dashboards", "Order-to-Delivery Process", "Rule Resolution", "Saga Pattern", "OIDC", "Repository Pattern", "Table Storage", "Linux Shell Scripting", "Getting Started with Power BI", "CI/CD Pipeline", "LDAP", "SQL Server", "Enterprise Class Structure", "OKTA", "Adobe Forms", "Google Data Analytics Professional Certificate", "HFM", "Amazon SNS", "Web services testing", "Desktop Application Testing", "ISO27001 Compliance", "AWS Lambda", "Pega 8.4", "IICS", "Predictive Analysis", "HPSM", "Import/Export", "Oracle Transactional Business Intelligence (OTBI)", "OCA - Oracle Database Administrator Certified", "EN ISO 14122", "Visual Studio 2010", "Resource Management", "Big Data", "SQS", "Google Cloud Platform (GCP)", "Pega Product Builder", "Logic Apps", "TKPROF", "TLS", "ios-xe asr 1K router", "CloudFormation", "CDH", "AeroScout tags", "CICS", "Sankey Diagrams", "Angular 10", "AWS Elastic Kubernetes Service (EKS)", "Screen Flows", "SAP Certified Functional Consultant", "Abstract Factory Pattern", "Account Payables", "Visual Studio 2017", "PLA", "Informatica Cloud Services (IICS)", "Peoplesoft FSCM", "SAP ECC", "Amazon Redshift", "NodeJS", "Oracle Cloud Infrastructure (OCI)", "JDK 8", "Salesforce Platform Developer (Certification - In process)", "Ping Identity", "MudBlazor", "MVC", "YAML Pipelines", "OBIEE", "SAP Treasury Modules", "Unit testing", "Bug Reporting", "Oracle Financials Cloud Receivables", "Azure Entra ID", "Enterprise Reporting", "Visual Studio 2003", "BottleRocket", "Fiori Elements", "EF Core", "SVN Subversion", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "Peoplesoft Financials", "IAM", "Oracle Enterprise Manager 12c", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "Perfecto", "API Gateway", "Swagger", "Amazon ECR (Elastic Container Registry)", "Sanity Testing", "Data Pipelining", ".NET Framework 3.5", "IoT Systems", "EZTrieve", "Amazon Route 53", "SAP Script", "@task", "Visual Studio 2008", "Azure Active Directory (Azure AD)", "Linux", "Auto Scaling", "Serverless Architecture", "Speedometer Charts", "Oracle OCI", "Technical Architecture", "Elastic Load Balancers", "Requirements mapping", "Elastic APM", "PFTC", "Microsoft technologies", "SAP UI5", "Inbound/Outbound Proxy", "JR<PERSON>el", ".NET Core 8.0", "Amazon Lake Formation", "MCA", "Karate Framework", "PFTC Roles", "Dynatrace", ".NET 6", "SEO Optimization", "Oracle Enterprise Manager", "<PERSON><PERSON><PERSON>", "SOAP", "AutoCAD", "Amazon Firehose", "Web Application Automation", "Excel"], "skill_frequency": {"Python": 97, "JavaScript": 72, "Java": 80, "C#": 52, "SQL": 93, ".NET 6": 10, "ASP.NET Core": 30, "ASP.NET MVC": 24, "Angular": 73, "Web API": 39, "Azure": 51, "Azure Functions": 40, "Azure Developer": 10, "Azure Logic Apps": 8, "Azure Service Bus": 16, "Azure API Management": 1, "Azure Storage": 19, "Cosmos DB": 17, "Redis Cache": 10, "Azure Active Directory (Azure AD)": 2, "Azure Virtual Network": 1, "Azure Application Insights": 1, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 18, "Azure Container Registry": 10, "Azure Service Fabric": 10, "Azure Data Lake": 16, "YAML Pipelines": 7, "Docker": 57, "Kubernetes": 51, "CI/CD": 62, "Microservices": 38, "Serverless Architecture": 16, "HTML": 63, "CSS": 42, "jQuery": 52, "Event Grid": 10, "Event Hub": 10, "SQL Server": 61, "MySQL": 64, "Snowflake": 27, "T-SQL": 27, "PL/SQL": 31, "Stored Procedures": 32, "Triggers": 15, "Functions (Database)": 1, "Amazon Web Services (AWS)": 56, "Microsoft Azure": 25, "Agile Methodology": 14, "Design Patterns": 23, "Microservices Architecture": 9, "Federated Database Design": 7, "Container-based Architecture": 5, "High-Throughput System Architecture": 3, "Real-time Data Analytics Solution Architecture": 5, "E-commerce Architecture": 7, "Hybrid Solution Architecture": 7, "VPC Design": 4, "Direct Connect": 7, "VPN": 7, "Query Performance Optimization": 8, "Data Modeling": 49, "Microsoft Certified Professional": 2, "WPF": 8, "MVC": 9, "MS Azure": 6, "WCF": 30, "Blob Storage": 8, "Table Storage": 8, "App Services": 8, "Redis": 15, "App Insights": 8, "Azure APIM": 8, "Logic Apps": 16, "AZ900: Microsoft Azure Fundamentals": 8, "Caliburn.Micro": 8, "Prism": 8, "Entity Framework 7.0": 8, "XML Parser": 8, "LINQ": 21, "Stimulsoft": 8, "Angular Reactive Forms": 8, "HttpClient": 8, "NUnit": 16, "Coded UI Testing": 7, "ADO.NET": 31, "SQL Server Reporting Services (SSRS)": 10, "Strapi CMS": 7, "Windows Services": 13, "WCF RESTful": 8, "MS SQL Server 2019": 4, "PostgreSQL": 53, "SQLite": 8, "Oracle (PL/SQL)": 4, "MS Access": 15, "InstallShield": 8, "GitHub": 49, "TFS": 21, "SVN": 40, "IIS": 8, "Apache Tomcat": 8, "DevExpress": 8, "Brainbench C# 5.0": 7, "MVVM": 2, "ASP.NET": 24, ".NET Framework": 16, "Entity Framework": 22, "EF Core": 8, "Razor View Engine": 8, "Bootstrap": 41, "CosmosDB": 8, "ElasticSearch": 22, "Apache Kafka": 27, "ActiveMQ": 8, "Pivotal Cloud Foundry": 8, "Azure App Service": 13, "Node.js": 47, "React": 32, "OAuth2": 7, "Swagger": 15, "OOPS": 8, "SOLID principles": 15, "Team Foundation Server (TFS)": 8, "Git": 63, "Jira": 83, "Azure DevOps": 53, "Moq": 8, "Agile Methodologies": 11, "SCRUM": 31, "Waterfall Methodologies": 8, "Test-Driven Development (TDD)": 13, "C++": 21, "Service Bus": 9, "API Management": 9, "YAML Pipeline": 3, "Azure AD": 8, "Virtual Network": 10, "Application Insights": 9, "Log Analytics": 9, "Key Vault": 9, "Functions": 12, "Software Architecture": 3, "Micro-services": 3, "High Throughput System Architecture": 3, "Microsoft Azure Certified Professional": 3, "MCA": 2, "Open API": 7, "C": 19, ".NET Core": 18, "AJAX": 20, "AngularJS": 7, "ReactJS": 21, "XML": 33, "HTML5": 25, "Sass": 7, "TypeScript": 44, "JSON": 48, "DynamoDB": 21, "OpenSearch": 7, "EC2": 25, "CloudFront": 7, "IAM": 23, "ECS": 7, "SQS": 19, "SNS": 20, "Lambda": 12, "API Gateway": 22, "RDS": 7, "CloudWatch": 25, "Step Functions": 11, "Elastic Cache": 7, "NodeJS": 6, "AGGrid": 5, "txText Control": 7, "ASPX": 7, "SOAP": 45, "RESTful APIs": 5, "Crystal Reports": 14, "Active Reports": 7, "SSRS": 23, "SSIS": 22, "YAML": 5, "Terraform": 21, "DDD": 1, "TDD": 1, "Agile": 72, "NuGet": 6, "Object-Oriented Programming (OOP)": 9, "VB.NET": 7, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 7, "OpenTelemetry": 7, "FullStory": 7, "Google Analytics": 14, "ASP.NET Core 6.0": 3, "ASP.NET Core 8.0": 3, ".NET MAUI": 7, "XAML": 7, "C# 8.0": 3, "C# 9.0": 3, "C# 10.0": 3, "Web Services": 7, "REST": 25, "Angular 7": 3, "Angular 8": 3, "Angular 9": 3, "Angular 10": 3, "Angular 12": 3, "Material Design": 7, ".NET Framework 2.0": 3, ".NET Framework 3.5": 3, ".NET Framework 4.0": 3, ".NET Framework 4.5": 3, ".NET Framework 4.7": 6, "CI/CD Pipeline": 7, "Splunk": 21, "RabbitMQ": 7, "Amazon DynamoDB": 14, "Kendo UI": 14, "Amazon EC2": 30, "AWS Lambda": 23, "Azure App Services": 9, "WebJobs": 1, "Azure Active Directory": 7, "ServiceNow": 18, "HP Service Manager (HPSM)": 3, "Service-Oriented Architecture (SOA)": 3, "OAuth 2.0": 8, "OKTA": 13, "Azure Entra ID": 7, "Bitbucket": 26, "Team Foundation Server": 7, "Subversion (SVN)": 10, "TortoiseSVN": 10, "Visual Studio 2003": 3, "Visual Studio 2005": 3, "Visual Studio 2008": 3, "Visual Studio 2010": 3, "Visual Studio 2012": 3, "Visual Studio 2013": 3, "Visual Studio 2015": 3, "Visual Studio 2017": 3, "Visual Studio 2019": 3, "Visual Studio 2022": 3, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 8, "OpenID Connect": 2, "Ping Identity": 2, "Salesforce APIs": 2, "CQRS": 8, "Saga Pattern": 5, "Choreography Pattern": 4, "Gateway Aggregation": 5, "Circuit Breaker Pattern": 6, "Message Queue": 1, "MuleSoft": 7, "Kafka": 20, "Tibco": 6, "AKS (Azure Kubernetes Service)": 5, "MVC Design Pattern": 6, "Repository Pattern": 5, "Dependency Inversion Principle": 7, "Dependency Injection": 7, "Factory Pattern": 5, "Abstract Factory Pattern": 5, "Tridion CMS 2009": 3, "Tridion CMS 2011": 3, "Tridion CMS 2013": 3, "Tridion CMS 8.5": 3, "Sitecore": 7, "SEO Optimization": 6, "Omniture": 7, "Google Tag Manager": 7, "SQL Server 2000": 3, "SQL Server 2005": 3, "SQL Server 2008": 3, "SQL Server 2012": 3, "SQL Server 2014": 3, "SQL Server 2017": 3, "Azure SQL Server": 3, "Oracle PL/SQL": 16, "Selenium": 7, "Azure Data Factory": 7, "PMP (Project Management Professional)": 4, "Agile (SCRUM)": 5, "Kanban": 13, "AZ-104": 6, "AZ-204": 6, "AZ-304": 6, "Machine Learning": 15, "Deep Learning": 6, "Predictive Analysis": 6, "Artificial Intelligence": 6, "IoT Systems": 1, ".NET": 16, "gRPC": 7, "SSIS (SQL Server Integration Services)": 7, "SSRS (SQL Server Reporting Services)": 3, "LINQ to SQL": 6, "LINQ to Objects": 6, "Lambda Expressions": 7, "S3 (Amazon S3)": 2, "Amazon Elastic Kubernetes Service (EKS)": 5, "Amazon ECR (Elastic Container Registry)": 2, "Elastic Beanstalk": 14, "Application Load Balancer": 5, "NoSQL": 7, "Datadog": 7, "Azure Container Registry (ACR)": 7, "Azure Kubernetes Service (AKS)": 6, "Azure Blob Storage": 14, "Blazor": 7, "MudBlazor": 7, "Telerik": 7, "Redux": 7, "Hangfire": 7, "ADFS (Active Directory Federation Services)": 3, "Tableau": 44, "DB2": 24, "SAP": 14, "IDoc": 7, "Logility": 5, "Blue Yonder": 5, "CloudFormation": 8, "VPC": 8, "Jenkins": 59, "SonarQube": 14, "Antifactory": 6, "AWS Elastic Kubernetes Service (EKS)": 7, "ANT": 26, "Maven": 54, "Shell Scripting": 16, "Ansible": 14, "PowerShell": 14, "Tomcat": 32, "JBoss": 19, "WebLogic": 21, "WebSphere": 19, "Windows Server": 7, "Red Hat Linux": 5, "Unix": 18, "CentOS": 5, "VMware": 7, "Elastic Load Balancers": 6, "Waterfall": 34, "Batch Scripting": 6, "Amazon ECS": 7, "Amazon S3": 29, "Amazon EBS": 7, "Amazon VPC": 7, "Amazon ELB": 7, "Amazon SNS": 8, "Amazon RDS": 7, "Amazon IAM": 7, "Amazon Route 53": 7, "AWS CloudFormation": 10, "AWS Auto Scaling": 4, "Amazon CloudFront": 7, "Amazon CloudWatch": 8, "AWS CLI": 6, "Vault": 7, "Docker Hub": 7, "Docker Registries": 6, "AWS Kops (EKS)": 5, "Groovy": 7, "GitLab": 20, "Apache": 3, "Grafana": 7, "Pivotal Cloud Foundry (PCF)": 7, "Infrastructure as Code (IaC)": 7, "Configuration Management": 13, "Containerization": 7, "Orchestration": 7, "Build/Release Management": 7, "Source Code Management (SCM)": 4, "HTTP (TLS)": 2, "Key Management": 1, "Encryption": 1, "J2EE": 15, "SAFe": 7, "Confluence": 19, "Microsoft Project": 6, "SmartSheet": 7, "DevOps": 7, "Warehouse Management": 7, "CMMI Level 5": 13, "PMP": 8, "PSM": 5, "Agile Project Management": 7, "Scrum Master": 7, "Program Management": 14, "Project Management": 14, "Project Planning": 12, "Risk Management": 14, "Cost Analysis": 13, "Resource Management": 7, "Stakeholder Management": 8, "Delivery Management": 7, "Client Management": 7, "Release Management": 8, "Microsoft Excel": 10, "Azure Cloud": 15, "Cobol": 17, "Ezetrieves": 5, "IBM BMP": 7, "ISO 27001": 6, "DBT": 6, "AWS": 22, "Azure Data Factory (ADF)": 6, "Databricks": 12, "Database Migration Service": 6, "AWS Glue": 17, "Fivetran": 6, "Snow SQL": 6, "Streamset": 6, "Snowpark": 6, "Column Masking": 5, "Data Encryption": 5, "Data Decryption": 5, "Data Masking": 5, "Data Governance": 7, "Hive": 12, "Pig": 6, "Sqoop": 6, "PySpark": 8, "Sigma": 6, "Apache Airflow": 6, "Informatica Power Center": 1, "Talend": 13, "Peoplesoft FSCM": 5, "Peoplesoft HCM": 6, "Oracle": 27, "MS SQL Server": 13, "OLTP": 12, "OLAP": 12, "Data Warehousing": 36, "Data Architecture": 4, "Data Integration": 16, "ELT": 5, "ETL": 29, "Data Quality": 18, "Real-time Data Ingestion": 5, "Snow Pipe": 4, "Confluent Kafka": 5, "Snowsight": 6, "SQR 6.0": 3, "Avro": 5, "Parquet": 5, "CSV": 9, "Index Design": 6, "Query Plan Optimization": 5, "Data Analysis": 11, "Business Intelligence": 14, "Data Management": 5, "ETL Processes": 6, "Excel": 8, "Power BI": 25, "DAX": 13, "Statistical Analysis": 5, "Regression": 5, "Hypothesis Testing": 6, "Predictive Modeling": 6, "Time Series Forecasting": 6, "Classification": 6, "Data Cleaning": 6, "Data Transformation": 15, "Data Automation": 6, "PivotTables": 6, "Power Query": 13, "Pandas": 6, "NumPy": 6, "SQL Server Integration Services (SSIS)": 5, "R": 6, "Google Data Analytics Professional Certificate": 6, "Getting Started with Power BI": 6, "The Complete Python Developer": 6, "ISTQB Certified Tester Foundation Level": 6, "Informatica PowerCenter": 17, "IICS": 2, "IDMC": 2, "IBM Infosphere DataStage": 6, "SAS Data Integration Studio": 6, "Oracle 11g": 11, "Oracle 10g": 11, "Oracle 9i": 5, "Oracle 8x": 5, "Microsoft SQL Server": 6, "Amazon Redshift": 6, "Data Migration": 10, "Data Modernization": 6, "Data Enrichment": 5, "Data Validation": 10, "Data Processing": 5, "Data Pipelining": 6, "Data Visualization": 11, "Enterprise Reporting": 5, "Dashboarding": 3, "AWS Athena": 5, "AWS Lake Formation": 3, "Microsoft Power BI": 6, "OBIEE": 6, "SAS Visual Investigator": 6, "SAS Visual Analytics": 6, "Erwin Data Modeler": 12, "Sparx Enterprise Architect": 6, "RDBMS": 12, "Star Schema": 19, "Snowflake Schema": 19, "Slowly Changing Dimensions (SCD)": 6, "Normalization": 6, "Flat Files": 4, "Predictive Forecasting": 3, "Alert Management": 3, "Regulatory Reporting": 3, "AML Compliance": 2, "Data Intelligence": 4, "Scenario Assessment": 1, "MIS Management": 3, "MS Excel": 4, "Data Security": 5, "Data Wrangling": 6, "Visual Studio": 10, "Mechanical Product Design": 6, "Mechanical Component Design": 2, "System Integration": 6, "Sheet Metal Design": 6, "Machined Parts Design": 6, "Design Standardization": 6, "Component Localization": 6, "Cost Optimization": 6, "Design Calculations": 6, "Cross-functional Collaboration": 5, "Onshore Rigging Calculations": 6, "Service Lifting Tool Design": 5, "Process Management": 6, "UG-NX": 6, "SolidWorks": 6, "CATIA": 6, "AutoCAD": 6, "ANSYS": 6, "Design FMEA": 6, "DFM": 6, "DFA": 6, "GD&T": 6, "Stack Up Analysis": 6, "ASME Y14.5": 6, "2D Drawing Review": 3, "MathCAD": 6, "CE Marking": 6, "DNVGL": 6, "EN-13155": 6, "Machinery Directive 2006/42/EC": 6, "EN ISO 50308": 6, "EN ISO 14122": 6, "Reverse Engineering": 6, "Informatica Cloud Services (IICS)": 6, "Intelligent Data Management Cloud (IDMC)": 6, "Netezza": 6, "Teradata": 8, "Windows": 19, "Spark": 6, "Data Profiling": 6, "Business 360 Console": 5, "Cloud Data Governance": 6, "Cloud Data Catalog": 5, "Data Marts": 3, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 6, "API": 4, "ICS": 4, "ICRT": 4, "Nifi": 6, "Technical Design Documentation": 2, "Technical Architecture Documentation": 2, "Production Support": 8, "Code Review": 9, "Redux Toolkit": 6, "Axios": 6, "SWR": 6, "Formik": 6, "React Router": 6, "CSS3": 12, "ES6": 3, "Material UI": 6, "Tailwind CSS": 6, "PHP": 6, "Amazon Lambda": 7, "Gulp": 6, "Grunt": 6, "Webpack": 6, "GitHub Copilot": 4, "JWT": 6, "RBAC": 5, "Software Development Life Cycle (SDLC)": 1, "Spring Boot": 25, "Struts 2": 6, "Spring IOC": 6, "Spring MVC": 12, "Spring Data": 6, "Spring REST": 6, "Jersey REST": 6, "JSF": 6, "Apache POI": 6, "iText": 6, "Servlets": 12, "JSP": 12, "JDBC": 12, "JAX-WS": 6, "JAX-RS": 6, "Java Mail": 6, "JMS": 12, "JUnits": 4, "IBM MQ": 6, "Amazon EKS": 8, "Cucumber": 13, "Cypress": 12, "Dojo Toolkit": 4, "MongoDB": 18, "Quartz": 6, "Hibernate": 12, "Spring JPA": 6, "Putty": 6, "WinSCP": 6, "Bamboo": 6, "AWS Aurora Postgres": 5, "EJB": 6, "JSTL": 6, "JPA": 6, "Struts": 6, "Spring Framework": 5, "NestJS": 12, "WebLogic 11g": 5, "GlassFish": 12, "Resin": 6, "Oracle 11g/12c": 5, "IBM DB2": 6, "Eclipse": 6, "NetBeans": 6, "JDeveloper": 6, "IntelliJ": 5, "MyEclipse": 6, "VS Code": 6, "Toad": 20, "Visio": 6, "UML": 6, "CVS": 6, "SoapUI": 13, "JMS Hermes": 6, "JUnit": 35, "Log4j": 6, "JRockit Mission Control": 6, "JMeter": 6, "JRebel": 6, "Spiral": 5, "Prototype": 5, "Google Cloud Platform (GCP)": 12, "ITIL Foundation 2011": 1, "AWS Certified Solutions Architect Associate": 5, "AWS Certified Developer Associate": 5, "AWS Certified SysOps Administrator Associate": 5, "Dynatrace": 6, "LDAP": 6, "SiteMinder": 6, "SAML": 6, "Harvest": 6, "Nx Monorepo": 6, "OOAD": 5, "SOA": 9, "Single Page Application (SPA)": 5, "AWS CDK": 14, "@task": 6, "GitLab Pipelines": 4, "Oracle DBA": 4, "Oracle OCI": 4, "Oracle 19c": 6, "Oracle 12c": 6, "Oracle 21c": 6, "Oracle RAC": 4, "Oracle Data Guard": 7, "Oracle Enterprise Manager": 3, "Oracle TDE": 6, "Data Pump": 6, "Oracle Cloud Infrastructure": 3, "RMAN": 6, "Linux Shell Scripting": 6, "Crontab": 6, "AWR": 6, "ADDM": 6, "EXPLAIN PLAN": 6, "SQL*Trace": 3, "TKPROF": 6, "STATSPACK": 6, "WebLogic 14c": 6, "WebLogic 12c": 6, "JDK": 4, "SQL Server 2016": 8, "Veeam Backup and Recovery": 6, "Red Hat Linux 7": 4, "Red Hat Linux 8": 4, "Exadata": 6, "IBM LTO 9": 6, "IBM LTO 8": 6, "OCI IAM": 5, "OCI VCN": 5, "OCI Object Storage": 5, "OCI Load Balancing": 5, "OCI Auto Scaling": 4, "OCI CDN": 5, "OCI WAF": 5, "Autonomous Data Warehouse (ADW)": 5, "Autonomous Transaction Processing (ATP)": 5, "ITIL V3 Foundation": 6, "Prince2": 6, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 4, "Oracle 19c Database Administrator Training": 2, "Teradata Certified Administrator (V2R5)": 6, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 6, "Oracle E-Business Suite R12": 7, "Oracle Cloud Financials": 6, "Oracle Cloud General Ledger": 3, "Oracle Cloud Accounts Payable": 3, "Oracle Cloud Accounts Receivable": 3, "Oracle Cloud Fixed Assets": 3, "Oracle Cloud Cash Management": 3, "Oracle Cloud I-Expenses": 3, "Oracle Cloud Budgetary Control": 7, "Oracle Financial Accounting Hub": 7, "Oracle Transactional Business Intelligence (OTBI)": 6, "Financial Reporting Studio (FRS)": 6, "Smart View": 6, "Data Loader": 7, "Hyperion FRS": 7, "Business Process Management (BPM)": 6, "AIM Methodology": 7, "OUM Methodology": 7, "Sub Ledger Accounting (SLA)": 6, "Windows 2007/2008/2010": 7, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 7, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 7, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 7, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 7, "BIP": 6, "Pega Rules Process Engine": 6, "Pega Group Benefits Insurance Framework": 6, "Pega Product Builder": 3, "Pega 7.2.2": 6, "Pega 7.3": 6, "Pega 7.4": 6, "Pega 8": 6, "Unit testing": 8, "Harness": 6, "Sections": 6, "Flow Actions": 6, "List-View": 6, "Summary-View Reports": 6, "Report Definitions": 6, "Clipboard": 6, "Tracer": 6, "PLA": 6, "Product locking": 3, "Package locking": 3, "Ruleset locking": 3, "SDLC": 9, "E-Commerce": 6, "Insurance": 6, "Agents": 5, "Queue Processors": 5, "Decision Rules": 5, "Declarative Rules": 5, "Application Design": 6, "Case Management": 6, "Process Flows": 6, "Screen Flows": 6, "Data Transforms": 6, "Activities": 6, "Rule Resolution": 6, "Enterprise Class Structure": 5, "Dev Studio": 6, "App Studio": 6, "Admin Studio": 6, "CDH": 6, "Document review": 6, "Pega Marketing Consultant": 2, "Senior System Architect": 2, "System Architect": 2, "Postman": 21, "Selenium IDE": 7, "Selenium RC": 7, "Selenium WebDriver": 21, "Selenium Grid": 7, "TestNG": 21, "QTP": 7, "Gherkin": 7, "Ruby": 13, "Tortoise SVN": 4, "HP Quality Center": 7, "SeeTest (Experitest)": 2, "ACCELQ": 7, "JBehave": 7, "HP ALM": 12, "BrowserStack": 7, "LambdaTest": 7, "Functional Testing": 14, "Smoke Testing": 7, "System Testing": 14, "Integration Testing": 16, "Regression Testing": 14, "User Acceptance Testing (UAT)": 6, "UI Testing": 13, "Mobile Testing": 14, "Automation Testing": 8, "Web Testing": 7, "Compatibility Testing": 7, "Sanity Testing": 7, "Ad hoc Testing": 6, "Test Case Design": 6, "Test Plan Creation": 10, "Test Scripting": 1, "Test Execution": 6, "Defect Tracking": 5, "Bug Reporting": 4, "Test Management": 8, "AI-powered Automation": 5, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 5, "Android Testing": 5, "SSAS": 7, "Power BI Desktop": 7, "Power BI Service": 7, "M Language": 7, "Dimensional Modeling": 7, "Microsoft BI Stack": 7, "Power Pivot": 7, "Data Gateway": 7, "Row-Level Security (RLS)": 6, "Data Flows": 6, "DataMart": 5, "Power Automate": 7, "Visual Studio Code": 7, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 7, "ASAP Methodology": 7, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 7, "Business Process Mapping": 7, "FIT-GAP Analysis": 7, "Financial Reporting": 7, "SAP Solution Design": 7, "SAP Warehouse Management": 7, "Material Master Data Management": 6, "Procurement Processes": 6, "Order-to-Delivery Process": 4, "Demand Forecasting": 7, "Cash Pooling": 7, "Bank Reconciliation": 6, "F110 Automatic Payment Program": 3, "Real-time Cash Visibility System": 7, "Inhouse Cash Management": 7, "SAP Best Practices": 6, "Generally Accepted Accounting Principles (GAAP)": 4, "International Financial Reporting Standards (IFRS)": 5, "Financial Analysis": 7, "Automated Data Entry": 3, "AR Processing & Reporting": 3, "Customer Accounting": 2, "Vendor/Customer Open Items": 2, "SAP Integration": 1, "SAP S/4HANA": 6, "ABAP": 6, "OData": 6, "SAP UI5": 6, "Fiori": 6, "Fiori Elements": 6, "PI/PO": 6, "AIF": 6, "BRF+": 6, "Business Workflow": 6, "CRM": 6, "Web Dynpro ABAP": 6, "RAP": 6, "BTP": 6, "CAPM": 6, "Procure to Pay (PTP)": 6, "Order to Cash Management (OTC)": 6, "Production Planning (PP)": 6, "Quality Management (QM)": 6, "FI-AP": 6, "FI-AR": 6, "FI-GL (FICO)": 6, "RTR": 6, "SCM": 8, "Product Life Cycle Management (PLM)": 6, "Advanced Planner Optimizer (APO)": 6, "Extended Warehouse Management (EWM)": 6, "Data Dictionary (DDIC)": 6, "Module Pool Programming": 6, "Object-Oriented ABAP (OOABAP)": 6, "RFCs": 6, "BADIs": 6, "BDC": 6, "BAPI": 6, "BP Integrations": 6, "Enhancement Points": 6, "User Exits": 6, "Customer Exits": 6, "ALE IDOCs": 6, "Inbound/Outbound Proxy": 2, "SAP NetWeaver Gateway": 6, "Service Registration": 6, "Service Extension": 6, "CDS Views": 6, "AMDP": 6, "SAP Fiori List Report Application": 5, "Web IDE": 5, "BSP": 6, "SAP Fiori Launchpad": 6, "SAP UI5 Framework": 2, "Business Objects (BO)": 6, "ATC": 6, "SPDD": 6, "SPAU": 6, "SAP Security": 6, "PFTC": 5, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 6, "SAP Certified Development Associate - SAP Fiori Application Developer": 6, "Next.js": 6, "REST APIs": 18, "GraphQL APIs": 6, "AWS SAM": 6, "Apache ECharts": 6, "Cognito": 6, "OIDC": 6, "Mantine UI": 6, "Vite": 6, "MySQL Aurora": 6, "AWS API Gateway": 1, "Styled Components": 6, "Sanity": 6, "Amplify": 6, "ShadCN UI": 6, "Salesforce": 6, "CDL": 6, "Cisco Catalyst 9800 Wireless Controller": 6, "Talwar controller": 6, "AireOS controller": 6, "Cisco Access Points": 6, "Talwar Simulator": 6, "WiFi": 6, "802.11": 6, "WLAN": 6, "Ethernet": 6, "IP": 6, "TCP": 6, "UDP": 6, "CAPWAP": 6, "NETCONF": 6, "YANG": 6, "Swift": 6, "ClearCase": 6, "Cisco catalyst 3750 Switch": 6, "ios-xe asr 1K router": 6, "OpenWRT": 6, "Linux": 16, "QMI": 6, "AT interfaces": 6, "Ubus": 5, "Qualcomm SDX hardware": 6, "AT&T Echo controller": 5, "POLARIS": 6, "GDB": 6, "Gre": 6, "RFID": 6, "AeroScout tags": 6, "Cisco Aironet outdoor mesh access points": 5, "Cisco Prime Infrastructure": 6, "Mac filtering": 1, "Bash": 7, "Android App Development": 7, "Flask": 7, "Django": 7, "GraphQL": 7, "Amazon Web Services": 8, "macOS": 7, "Kali Linux": 7, "OAuth": 13, "AWS Certified Solutions Architect - Associate": 8, "Amazon SQS": 1, "Amazon Athena": 3, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 9, "Test Driven Development (TDD)": 7, "Mockito": 12, "Spark SQL": 6, "Server-Side Encryption": 5, "IAM Role Management": 4, "EKS": 5, "BottleRocket": 6, "React.js": 6, "Firebase Cloud Services": 6, "Cassandra": 6, "Android Studio": 6, "Bluetooth": 6, "Java Development": 5, "Advanced Java Development": 5, "Salesforce Platform Administrator": 5, "Salesforce Platform Developer": 5, "Appium": 7, "Perfecto": 7, "SeeTest": 7, "REST Assured": 7, "Karate Framework": 7, "UFT": 7, "LeanFT": 7, "Zephyr": 5, "Quality Center": 5, "Informatica 10.2": 7, "MicroStrategy": 7, "CICS": 7, "JCL": 7, "VSAM": 7, "Sufi": 7, "File-Aid": 7, "CA DevTest": 7, "ATOM": 3, "GCP": 6, "SSO": 6, "Test Strategy": 1, "Test Design": 4, "Test effort estimation": 2, "Requirements mapping": 4, "Risk-based testing": 7, "End-to-End testing": 7, "User Acceptance testing": 7, "Database testing": 7, "API testing": 7, "Web services testing": 7, "Microservices testing": 6, "Browser compatibility testing": 7, "Exploratory testing": 7, "ETL testing": 6, "Data Warehouse testing": 7, "Interactive Voice Response (IVR) testing": 5, "Customer Telephony Integration (CTI) testing": 5, "Mainframes testing": 4, "Service Virtualization": 6, "Continuous Integration and Continuous Deployment (CI/CD)": 2, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 4, "Oracle Financials Cloud Accounts Payable": 4, "Accounts Payable": 4, "Accounts Receivable": 4, "General Ledger": 4, "Fixed Assets": 4, "Cash Management": 4, "I-Expenses": 4, "I-Receivables": 4, "Order Management": 4, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 3, "Order to Cash (O2C)": 3, "Record to Report (R2R)": 3, "Oracle Allocations": 3, "Oracle Cloud": 1, "Intercompany": 5, "SeeTest/Experitest": 5, "Test Case Development": 1, "Test Schedule Creation": 2, "SAP Financial Accounting (FI)": 6, "SAP Controlling (CO)": 6, "SAP Sales and Distribution (SD)": 6, "SAP Materials Management (MM)": 6, "Hyperion Financial Management (HFM)": 6, "Financial Data Management (FDMEE)": 6, "Profit Center Accounting (PCA)": 2, "SAP Accounts Receivable (AR)": 2, "SAP Accounts Payable (AP)": 2, "General Ledger (GL)": 3, "Purchase Order (PO) Management": 1, "Inventory Planning": 5, "Automatic Payment Program (F110)": 4, "Network Security": 3, "Object Oriented Programming": 3, "Operating Systems": 3, "Design and Analysis of Algorithms": 3, "DBMS": 3, "Mainframe Testing": 3, "Performance Testing": 2, "User Interface Testing": 6, "Manual Testing": 1, "Mobile Web Testing": 4, "Desktop Application Testing": 1, "Web Application Testing": 1, "Data Analytics": 5, "Real-time Data Analytics": 2, "NoSQL Databases": 2, "Blueprints": 2, "Test Case Execution": 3, "Custom Visuals (Power BI)": 4, "Drill Down": 4, "Drill Through": 4, "Parameters": 4, "Cascading Filters": 4, "Interactive Dashboards": 2, "Reports": 1, "SAP Profit Center Accounting (PCA)": 1, "Automated Bank Reconciliation": 1, "Pricing Strategies": 3, "SAP MM Functionalities": 3, "Business Process Optimization": 1, "IVR Testing": 1, "CTI Testing": 1, "Continuous Integration": 4, "Continuous Deployment": 3, "High-Performance Architecture Design": 3, "Container-based Architecture Design": 2, "High Throughput System Architecture Design": 1, "Real-Time Data Analytics Solution Architecture Design": 1, "E-commerce Architecture Design": 1, "Microsoft technologies": 5, "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": 1, "Coded UI": 1, "MS SharePoint Server": 3, ".NET Core 6.0": 3, ".NET Core 8.0": 3, "XML Web Services": 5, ".NET Core Apps": 1, "Domain Driven Design (DDD)": 3, "Web Jobs": 6, "Model-View-Controller (MVC)": 1, "Tridion CMS": 4, "Internet of Things (IoT)": 3, "Azure SQL": 4, "Azure Pipelines": 5, "Rally": 5, "Multi-AZ": 1, "High Availability": 4, "Disaster Recovery": 2, "AWS-Kops (EKS)": 2, "HTTP": 5, "TLS": 5, "Windows Application Migration": 1, "OTT": 3, "RACI Matrix": 1, "S3": 6, "Performance Tuning": 3, "Query Optimization": 3, "Informatica Intelligent Cloud Services (IICS)": 4, "Informatica Data Management Center (IDMC)": 2, "Amazon Lake Formation": 2, "Cloud Migration": 4, "Nebula": 5, "Advanced Analytics": 3, "Data Compliance": 2, "Key Performance Indicators (KPIs)": 1, "Service Level Agreement (SLAs)": 1, "Data Flow Architectures": 3, "Data Collection": 2, "Data Storage Strategies": 2, "Agile Transformation": 1, "ISO27001 Compliance": 1, "Mechanical Design": 2, "Service Lifting Tools Design": 1, "2D Drawings Review": 3, "Unix Shell Scripting": 3, "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": 5, "Technical Design": 3, "Technical Architecture": 3, "Big Data": 4, "Real-time Data Integration": 2, "Amazon Web Services (S3, EC2, Lambda)": 2, "Silverlight": 2, "ITIL Foundation": 5, "AWS Certified Developer - Associate": 1, "AWS Certified SysOps Administrator - Associate": 1, "Oracle 19c Data Guard": 2, "Oracle 19c RAC": 2, "Red Hat Linux Enterprise 8": 2, "Red Hat Linux Enterprise 7": 2, "Oracle Enterprise Manager 12c": 4, "Oracle Enterprise Manager Grid Control 11g": 1, "Oracle Export/Import": 1, "Transportable Tablespaces": 2, "SQLTrace": 3, "Oracle Real Application Cluster": 1, "Windows Server 2016": 4, "Fault Tolerance": 2, "Scalability": 2, "Virtualization": 2, "Oracle Autonomous Data Warehouse (ADW)": 1, "Oracle Autonomous Transaction Processing (ATP)": 1, "VCN": 1, "Object Storage": 1, "Load Balancing": 1, "Auto Scaling": 1, "CDN": 1, "WAF": 1, "Exadata X9M-2": 5, "HP": 1, "IBM Power E980": 1, "IBM Power E850": 1, "OCI-Oracle Cloud Infrastructure Foundations Associate": 4, "OCA-Oracle Database Administrator Certified": 2, "Oracle 19c Database Administrator": 1, "ISO/IEC 27001": 2, "ISO 20000": 2, "ISO 27000": 1, "Pega Marketing Consultant (Certification)": 4, "Senior System Architect (Certification)": 4, "System Architect (Certification)": 4, "RICEF": 3, "SAP Script": 3, "Smart Forms": 4, "Adobe Forms": 4, "ALV Reports": 4, "Mac filter configuration": 2, "Athena": 4, "JDK 8": 3, "JDK 17": 3, "Java Development (Certification)": 1, "Advanced Java Development (Certification)": 1, "Salesforce Platform Administrator (Certification - In process)": 1, "Salesforce Platform Developer (Certification - In process)": 1, "C# Programming Certified Professional": 2, "Strapi": 1, "Wix": 2, "AG Grid": 2, "Domain-Driven Design (DDD)": 1, "Pub/Sub": 4, "HPSM": 4, "Subversion": 3, "Saga": 3, "Choreography": 3, "Circuit Breaker": 2, "AKS": 3, "Repository Design Pattern": 2, "Factory Design Pattern": 2, "Abstract Factory Design Pattern": 2, "SEO": 1, "Object-Oriented Programming": 4, "IoT": 2, "Microsoft .NET": 1, "AWS S3": 5, "Amazon Elastic Container Registry (ECR)": 3, "ADFS": 4, "Maven POM": 2, "Build.xml": 2, "AWS Storage Services": 1, "Security Groups": 2, "Multi-AZ VPC": 1, "Amazon CloudFormation": 2, "Amazon Auto Scaling": 3, "Microsoft Project (MPP)": 1, "Strategic Planning": 2, "EZTrieve": 2, "Peoplesoft Financials": 1, "Peoplesoft Supply Chain Management": 1, "Account Payables": 2, "Account Receivables": 2, "GL": 1, "Billing": 2, "Dimension Modeling": 3, "Fact Tables": 6, "Relational Databases": 2, "Data Mining": 1, "DWH": 3, "DM": 3, "Dimension Tables": 2, "Dashboards": 2, "Data Security and Compliance": 1, "Product Validation": 1, "API Development (ICS, ICRT)": 2, "Production Support (L3)": 2, "Test Plan Development": 1, "Test Script Development": 1, "IntelliJ IDEA": 1, "Spiral Model": 1, "Prototype Model": 1, "Oracle Database Administration": 2, "Oracle Cloud Infrastructure (OCI)": 2, "Oracle GoldenGate (implied)": 1, "Java JDK": 2, "Oracle Cloud I-Receivables": 1, "SharePoint": 2, "Microsoft Office": 3, "Ad-hoc Testing": 1, "Test Planning": 1, "SSMS": 2, "SQL Server Data Tools": 2, "SAP Profit Center Accounting (PCBS)": 2, "SAP AR Processing & Reporting": 1, "Cash Discount Management": 2, "Dispute and Deduction Management": 2, "SAP FI-GL Transactions": 1, "SAP AP/AR Transactions": 1, "Vendor/Customer Open Item Management": 2, "BPP Documentation": 1, "Order-to-Delivery Process Optimization": 2, "Certified SAP Functional Consultant": 1, "PFTC Roles": 1, "SAP ECC": 1, "SAP Scripts": 1, "Device Drivers": 2, "LED Manager": 2, "Mesh Networking": 2, "Cisco Aironet": 1, "ATOM (Mainframes/AS400 automation tool)": 1, "Test Automation": 1, "Test Strategy Creation": 3, "Test Coverage": 3, "Requirements Prioritization": 1, "ASP": 1, "N-tier applications": 2, "Client-server applications": 2, "Auto-scaling": 1, "Artifactory": 1, "Physical Database Design": 1, "Database Tuning": 1, "Snowpark API": 1, "PII": 2, "PCI": 2, "Trifacta": 1, "Oracle 8i": 1, "Oracle 11i": 1, "DB2 8.1": 2, "Python Worksheet": 1, "Certified Professional Data Engineer": 1, "ETL Design": 2, "ETL Development": 2, "Python Scripting": 1, "Informatica Data Management Cloud (IDMC)": 2, "Data Mart": 2, "Requirement Gathering": 1, "Solution Architecture": 2, "Dojo": 2, "Spring": 1, "Oracle Grid Control 11g": 3, "SQL*Plus": 2, "OCI AutoScaling": 1, "HP/IBM Power E980": 1, "HP/IBM Power E850": 1, "Exadata CS DBX7": 1, "Defect Management": 2, "Speedometer Charts": 1, "Sankey Diagrams": 1, "Pareto Charts": 1, "Waterfall Charts": 1, "SAP Material Master Data Management": 1, "SAP Procurement Processes": 1, "GAAP (Generally Accepted Accounting Principles)": 2, "IFRS (International Financial Reporting Standards)": 2, "SAP Treasury Modules": 2, "LTE": 1, "5G": 1, "Firehose": 1, "Test Estimation": 2, "Cloud Testing (AWS, GCP, Azure, Microsoft)": 1, "OAuth Testing": 1, "SSO Testing": 1, "KPI Development": 2, "SLA Development": 2, "Microsoft Excel Spreadsheets": 1, "Oracle 8i/11i": 1, "Snowpipe": 1, "Domain Driven Development (DDD)": 2, "Indexing": 2, "Database Performance Tuning": 2, "Database Partitioning": 2, "Error Handling": 1, "Oracle Database (11g, 10g, 9i, 8x)": 1, "Business 360": 1, "Data Pipelines": 2, "Bug Fixing": 1, "Pega 8.4": 1, "Pega 8.4.1": 1, "PCBS (Profit Center Budgeting System)": 1, "FI-GL Transactions": 2, "AP/AR Transactions": 2, "BPPs (Business Process Procedures) Documentation": 1, "Product Assortment Management": 1, "Event-Driven Architecture": 1, "Backend for Frontend (BFF)": 1, "Active Directory": 1, "Continuous Integration/Continuous Deployment (CI/CD)": 1, "Regression Analysis": 1, "Component-Based Architecture": 1, "Multi-tier Distributed Applications": 1, "Oracle Financials Cloud Receivables": 1, "Business Intelligence Publisher (BIP)": 1, "AI": 1, "Chatbot": 1, "AWS Aurora": 1, "Oracle 19c Database Administrator Training from Koenig Database Administration": 1, "Dimensions": 1, "Cloud Data Integration": 1, "Cloud Application Integration": 1, "Materialized Views": 1, "iTech Sharp": 1, "MS Unit Test": 1, "J#": 1, "Oracle Enterprise Manager (OEM)": 1, "WAR file deployment": 1, "OCI - Oracle Cloud Infrastructure Foundations Associate": 1, "Database Migration": 1, "Backup and Recovery": 1, "Import/Export": 1, "AWS Storage": 1, "AWS Active Directory": 1, "Report Development": 1, "RESTful Web Services": 2, "General Accepted Accounting Principles (GAAP)": 1, "Cash Management Integration": 1, "Business Process Documentation": 1, "SAP Certified Functional Consultant": 1, "complex join queries": 1, "Scalable architecture design": 2, "Azure Blueprints": 1, "Android": 1, "iOS": 1, "JUnit (implied)": 1, "Server-Side Encryption (S3)": 1, "Amazon ECR": 2, "Tibco Messaging": 1, "Azure Container Storage": 1, "Azure Tables": 1, "Azure Queues": 1, "Azure Blobs": 1, "RICEF objects": 1, "Analytical Applications": 1, "CDS Annotations": 1, "WebIDE": 1, "UAT (User Acceptance Testing)": 1, "Software Development Lifecycle (SDLC)": 1, "Data Collection and Storage": 1, "Lake Formation": 1, "Fraud Detection": 1, "FinCrime": 1, "Windows Application": 2, "PMO": 1, "Change Management": 1, "Supply Chain Management": 1, "Organizational Change Management (OCM)": 1, "Observable": 1, "Winforms": 1, "Windows Service": 1, "SVN Subversion": 1, "AWS (Amazon Web Services)": 1, "Logic App": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints", "High-Performance Architecture Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "C# Programming Certified Professional", "complex join queries", "Scalable architecture design", "Azure Blueprints", "Logic App", "VPC"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM", "Coded UI", "Oracle PL/SQL", "MS SharePoint Server", "MySQL", "Azure", "SSRS", "<PERSON><PERSON><PERSON>", "Wix", "Git", "Oracle", "PL/SQL", "iTech Sharp", "Postman", "MS Unit Test", "Observable", "MS Excel", "Winforms"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C", "Pivotal Cloud Foundry (PCF)", "MVC", "Single Page Application (SPA)", "OAuth 2.0", "Saga Pattern", "API Gateway", "Circuit Breaker <PERSON>", "Event-Driven Architecture", "CQRS", "Backend for Frontend (BFF)", "Object-Oriented Programming (OOP)", ".NET"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Angular", "React", "XML Web Services", ".NET Core Apps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "T-SQL", "MS SQL Server", "Node.js", "AG Grid", "Domain-Driven Design (DDD)", "Pub/Sub", "ASP", "N-tier applications", "Client-server applications", ".NET", "Auto-scaling", "SQL Server 2016", "Domain Driven Development (DDD)", "RESTful Web Services", "Windows Service", "Test Driven Development (TDD)"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems", "ASP.NET Core", "C#", "Angular", ".NET Framework", "Web Jobs", "Visual Studio", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Tridion CMS", "SQL Server", "PMP", "Internet of Things (IoT)", "HPSM", "SOA", "Subversion", "Saga", "Choreography", "Circuit Breaker", "AKS", "Repository Design Pattern", "Factory Design Pattern", "Abstract Factory Design Pattern", "Azure Cloud", "SEO", "Object-Oriented Programming", "Agile", "SCRUM", "IoT", "Tibco Messaging", "Azure Storage", "Azure Container Storage", "Azure Tables", "Azure Queues", "Azure Blobs", "SVN Subversion"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder", "Azure SQL", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "<PERSON><PERSON>", "Rally", "Microsoft .NET", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "Azure App Services", "AWS S3", "Amazon Elastic Container Registry (ECR)", "ADFS", "Amazon S3", "SSIS", "SSRS", "Microservices", "Amazon ECR", "Amazon EKS", "AKS", "Azure SQL Server", "AWS (Amazon Web Services)"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>", "Linux", "Multi-AZ", "High Availability", "Disaster Recovery", "Maven <PERSON>", "Build.xml", "AWS Storage Services", "Security Groups", "Multi-AZ VPC", "Artifactory", "Active Directory", "AWS Storage", "Virtual Network", "AWS Active Directory"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption", "AWS-Kops (EKS)", "HTTP", "TLS", "Amazon CloudFormation", "Amazon Auto Scaling", "Amazon Lambda", "AWS", "Azure DevOps", "Pivotal Cloud Foundry", "SCM", "Continuous Integration/Continuous Deployment (CI/CD)", "Microservices"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM", "Windows Application Migration", "OTT", "RACI Matrix", "Microsoft Project (MPP)", "Windows Application", "Risk Management", "Stakeholder Management", "PMO", "Program Management", "Project Management", "Release Management", "Change Management", "Supply Chain Management", "Organizational Change Management (OCM)"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001", "Strategic Planning", "EZTrieve", "J#"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization", "S3", "Git", "Informatica PowerCenter", "Business Intelligence", "Data Migration", "Performance Tuning", "Query Optimization", "Peoplesoft Financials", "Peoplesoft Supply Chain Management", "Account Payables", "Account Receivables", "GL", "Billing", "Data Validation", "Dimension Modeling", "Fact Tables", "Physical Database Design", "Database Tuning", "Snowpark API", "PII", "PCI", "Trifacta", "SQL Server", "Oracle 8i", "Oracle 11i", "DB2 8.1", "<PERSON> (GL)", "Python Worksheet", "Certified Professional Data Engineer", "Oracle 8i/11i", "Snowpipe"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "SSIS (SQL Server Integration Services)", "Relational Databases", "Regression Analysis"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "Amazon Web Services (AWS)", "Amazon Athena", "Amazon Lake Formation", "Cloud Migration", "Data Governance", "Neb<PERSON>", "AWS S3", "Data Mining", "DWH", "DM", "Fact Tables", "Dimension Tables", "Dashboards", "Advanced Analytics", "ETL Design", "ETL Development", "Python Scripting", "Informatica Data Management Cloud (IDMC)", "Data Mart", "Project Management", "Requirement Gathering", "Solution Architecture", "Erro<PERSON>", "Oracle Database (11g, 10g, 9i, 8x)", "EC2", "Athena", "Lake Formation", "CloudFormation", "<PERSON>aud Detection", "FinCrime"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile", "Business Intelligence", "Advanced Analytics", "Microsoft Excel", "Data Compliance", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Collection", "Data Storage Strategies", "Agile Transformation", "ISO27001 Compliance", "Data Security and Compliance", "Project Management", "KPI Development", "SLA Development", "Microsoft Excel Spreadsheets", "Relational Databases", "Data Collection and Storage"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering", "Mechanical Design", "Service Lifting Tools Design", "2D Drawings Review", "Product Validation", "Microsoft Office"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review", "Unix Shell Scripting", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Technical Design", "Technical Architecture", "Big Data", "PySpark", "Real-time Data Integration", "API Development (ICS, ICRT)", "Production Support (L3)", "Test Plan Development", "Test Script Development", "Business 360", "Data Pipelines", "Dimensions", "Fact Tables", "Cloud Data Integration", "Cloud Application Integration", "Materialized Views", "Stored Procedures"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)", "Amazon Web Services (S3, EC2, Lambda)", "Silverlight", "Node.js", "Software Development Lifecycle (SDLC)"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres", "JUnit", "Dojo", "PostgreSQL", "AWS Aurora"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines", "ITIL Foundation", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "J2EE", "IntelliJ IDEA", "Spiral Model", "Prototype Model", "Spring", "Component-Based Architecture", "Multi-tier Distributed Applications", "WebLogic", "Oracle", "OOPS"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Oracle 19c Data Guard", "Oracle 19c RAC", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle Export/Import", "Transportable Tablespaces", "SQLTrace", "Oracle Real Application Cluster", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "High Availability", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "IAM", "VCN", "Object Storage", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "Exadata X9M-2", "HP", "IBM Power E980", "IBM Power E850", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "ISO/IEC 27001", "ISO 20000", "ISO 27000", "Oracle Database Administration", "Oracle Cloud Infrastructure (OCI)", "Oracle GoldenGate (implied)", "Java JDK", "Terada<PERSON>", "Oracle Grid Control 11g", "SQL*Plus", "OCI AutoScaling", "HP/IBM Power E980", "HP/IBM Power E850", "Exadata CS DBX7", "Oracle 19c Database Administrator Training from Koenig Database Administration", "Oracle Enterprise Manager (OEM)", "WAR file deployment", "OCI - Oracle Cloud Infrastructure Foundations Associate", "Database Migration", "Backup and Recovery", "Performance Tuning", "AWS", "Azure", "Import/Export"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany", "Oracle Cloud I-Receivables", "Oracle Financials Cloud Receivables", "Business Intelligence Publisher (BIP)"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)", "Pega 8.4", "Pega 8.4.1"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN", "SharePoint", "Microsoft Office", "Continuous Integration", "SCRUM"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation", "Unit testing", "Test Case Execution", "Mobile Web Testing", "SDLC", "Performance Testing", "Ad-hoc Testing", "Test Planning", "Defect Management", "UAT (User Acceptance Testing)"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "Excel", "SSMS", "SQL Server Data Tools", "Speedometer Charts", "Sankey Diagrams", "Pareto Charts", "Waterfall Charts", "Stored Procedures", "Functions", "Triggers", "Indexing", "Database Performance Tuning", "Query Optimization", "Database Partitioning", "Data Mart", "Report Development"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)", "SAP Profit Center Accounting (PCA)", "Automated Bank Reconciliation", "Pricing Strategies", "SAP MM Functionalities", "Business Process Optimization", "Agile Methodologies", "SAP Profit Center Accounting (PCBS)", "SAP AR Processing & Reporting", "Cash Discount Management", "Dispute and Deduction Management", "SAP FI-GL Transactions", "SAP AP/AR Transactions", "Vendor/Customer Open Item Management", "BPP Documentation", "Order-to-Delivery Process Optimization", "Certified SAP Functional Consultant", "SAP Material Master Data Management", "SAP Procurement Processes", "GAAP (Generally Accepted Accounting Principles)", "IFRS (International Financial Reporting Standards)", "SAP Treasury Modules", "PCBS (Profit Center Budgeting System)", "FI-GL Transactions", "AP/AR Transactions", "BPPs (Business Process Procedures) Documentation", "Product Assortment Management", "General Accepted Accounting Principles (GAAP)", "Cash Management Integration", "Business Process Documentation", "SAP Certified Functional Consultant"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "RICEF", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "PFTC Roles", "SAP ECC", "Agile", "Waterfall", "SAP Scripts", "RICEF objects", "Analytical Applications", "CDS Annotations", "WebIDE"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL", "API Gateway", "Amazon Web Services", "MySQL", "Microservices Architecture", "Lambda", "AI", "<PERSON><PERSON><PERSON>"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering", "Mac filter configuration", "Device Drivers", "LED Manager", "Mesh Networking", "Cisco Aironet", "Unit testing", "Integration Testing", "LTE", "5G", "<PERSON><PERSON>xing"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket", "Amazon Web Services (AWS)", "Lambda", "EC2", "SQS", "S3", "SNS", "CI/CD", "CloudWatch", "Step Functions", "AWS Glue", "Athena", "JDK 8", "JDK 17", "AWS Athena", "IAM", "Firehose", "Server-Side Encryption (S3)"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C", "IVR Testing", "CTI Testing", "Continuous Integration", "Continuous Deployment", "ATOM (Mainframes/AS400 automation tool)", "Test Automation", "Test Strategy Creation", "Test Coverage", "Requirements Prioritization", "ETL", "Test Estimation", "Cloud Testing (AWS, GCP, Azure, Microsoft)", "OAuth Testing", "SSO Testing", "Android", "iOS", "<PERSON><PERSON><PERSON><PERSON> (implied)"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Chary"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "PunniyaKodi V updated resume", "Chary"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV", "Vidwaan_vidwan_resume"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Chary", "<PERSON>"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "<PERSON><PERSON>"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "PunniyaKodi V updated resume"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead", "Chandra_Resume"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>_DotNET"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON><PERSON>"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chary", "pavani_resume"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Lambda": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON>il .Net lead"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Step Functions": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>_DotNET"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "Chary", "Uday"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary", "PunniyaKodi V updated resume"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure App Services": ["Chary", "<PERSON><PERSON>_DotNET"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Saga Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary", "<PERSON><PERSON><PERSON>il .Net lead"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary", "<PERSON><PERSON>_DotNET"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary", "<PERSON><PERSON>_DotNET"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>il .Net lead"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET", "Chary"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Laxman_Gite"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>il .Net lead"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>", "Chandra_Resume"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet", "Chary"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Puneet"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Puneet"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Puneet"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Puneet"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Puneet"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Chary"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Chandra_Resume"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON>", "DA manager <PERSON><PERSON>"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON>_DotNET"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>", "Chary"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>", "<PERSON><PERSON>_DotNET"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume", "Chary"], "Single Page Application (SPA)": ["Chandra_Resume", "<PERSON><PERSON><PERSON>il .Net lead"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA", "PunniyaKodi V updated resume"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV", "A<PERSON><PERSON>_resume"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday", "<PERSON><PERSON><PERSON>"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)", "Chandra_Resume"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume", "PunniyaKodi V updated resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"], "Test Case Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Custom Visuals (Power BI)": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Through": ["<PERSON><PERSON><PERSON><PERSON>"], "Parameters": ["<PERSON><PERSON><PERSON><PERSON>"], "Cascading Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Interactive Dashboards": ["<PERSON><PERSON><PERSON><PERSON>"], "Reports": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "Pricing Strategies": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM Functionalities": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "IVR Testing": ["SivakumarDega_CV"], "CTI Testing": ["SivakumarDega_CV"], "Continuous Integration": ["SivakumarDega_CV", "pavani_resume"], "Continuous Deployment": ["SivakumarDega_CV"], "High-Performance Architecture Design": ["Laxman_Gite"], "Container-based Architecture Design": ["Laxman_Gite"], "High Throughput System Architecture Design": ["Laxman_Gite"], "Real-Time Data Analytics Solution Architecture Design": ["Laxman_Gite"], "E-commerce Architecture Design": ["Laxman_Gite"], "Microsoft technologies": ["Laxman_Gite"], "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": ["Laxman_Gite"], "Coded UI": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SharePoint Server": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], ".NET Core 6.0": ["PunniyaKodi V updated resume"], ".NET Core 8.0": ["PunniyaKodi V updated resume"], "XML Web Services": ["PunniyaKodi V updated resume"], ".NET Core Apps": ["PunniyaKodi V updated resume"], "Domain Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Web Jobs": ["Chary"], "Model-View-Controller (MVC)": ["Chary"], "Tridion CMS": ["Chary"], "Internet of Things (IoT)": ["Chary"], "Azure SQL": ["<PERSON><PERSON>_DotNET"], "Azure Pipelines": ["<PERSON><PERSON>_DotNET"], "Rally": ["<PERSON><PERSON>_DotNET"], "Multi-AZ": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "High Availability": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Disaster Recovery": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS-Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "HTTP": ["<PERSON><PERSON><PERSON>"], "TLS": ["<PERSON><PERSON><PERSON>"], "Windows Application Migration": ["Puneet"], "OTT": ["Puneet"], "RACI Matrix": ["Puneet"], "S3": ["<PERSON>", "Vidwaan_vidwan_resume"], "Performance Tuning": ["<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Query Optimization": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Informatica Intelligent Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Center (IDMC)": ["<PERSON><PERSON><PERSON>"], "Amazon Lake Formation": ["<PERSON><PERSON><PERSON>"], "Cloud Migration": ["<PERSON><PERSON><PERSON>"], "Nebula": ["<PERSON><PERSON><PERSON>"], "Advanced Analytics": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Data Compliance": ["DA manager <PERSON><PERSON>"], "Key Performance Indicators (KPIs)": ["DA manager <PERSON><PERSON>"], "Service Level Agreement (SLAs)": ["DA manager <PERSON><PERSON>"], "Data Flow Architectures": ["DA manager <PERSON><PERSON>"], "Data Collection": ["DA manager <PERSON><PERSON>"], "Data Storage Strategies": ["DA manager <PERSON><PERSON>"], "Agile Transformation": ["DA manager <PERSON><PERSON>"], "ISO27001 Compliance": ["DA manager <PERSON><PERSON>"], "Mechanical Design": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tools Design": ["<PERSON><PERSON><PERSON>"], "2D Drawings Review": ["<PERSON><PERSON><PERSON>"], "Unix Shell Scripting": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": ["<PERSON><PERSON><PERSON>"], "Technical Design": ["<PERSON><PERSON><PERSON>"], "Technical Architecture": ["<PERSON><PERSON><PERSON>"], "Big Data": ["<PERSON><PERSON><PERSON>"], "Real-time Data Integration": ["<PERSON><PERSON><PERSON>"], "Amazon Web Services (S3, EC2, Lambda)": ["<PERSON><PERSON><PERSON>"], "Silverlight": ["<PERSON><PERSON><PERSON>"], "ITIL Foundation": ["Chandra_Resume"], "AWS Certified Developer - Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator - Associate": ["Chandra_Resume"], "Oracle 19c Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Transportable Tablespaces": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQLTrace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Real Application Cluster": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Fault Tolerance": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Scalability": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Virtualization": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA-Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO/IEC 27001": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 20000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 27000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Pega Marketing Consultant (Certification)": ["<PERSON><PERSON><PERSON>"], "Senior System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "RICEF": ["Uday"], "SAP Script": ["Uday"], "Smart Forms": ["Uday"], "Adobe Forms": ["Uday"], "ALV Reports": ["Uday"], "Mac filter configuration": ["A<PERSON><PERSON>_resume"], "Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "JDK 8": ["Vidwaan_vidwan_resume"], "JDK 17": ["Vidwaan_vidwan_resume"], "Java Development (Certification)": ["Soham_Resume_Java"], "Advanced Java Development (Certification)": ["Soham_Resume_Java"], "Salesforce Platform Administrator (Certification - In process)": ["Soham_Resume_Java"], "Salesforce Platform Developer (Certification - In process)": ["Soham_Resume_Java"], "C# Programming Certified Professional": ["Laxman_Gite"], "Strapi": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Wix": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AG Grid": ["PunniyaKodi V updated resume"], "Domain-Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Pub/Sub": ["PunniyaKodi V updated resume"], "HPSM": ["Chary"], "Subversion": ["Chary"], "Saga": ["Chary"], "Choreography": ["Chary"], "Circuit Breaker": ["Chary"], "AKS": ["Chary", "<PERSON><PERSON>_DotNET"], "Repository Design Pattern": ["Chary"], "Factory Design Pattern": ["Chary"], "Abstract Factory Design Pattern": ["Chary"], "SEO": ["Chary"], "Object-Oriented Programming": ["Chary"], "IoT": ["Chary"], "Microsoft .NET": ["<PERSON><PERSON>_DotNET"], "AWS S3": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Amazon Elastic Container Registry (ECR)": ["<PERSON><PERSON>_DotNET"], "ADFS": ["<PERSON><PERSON>_DotNET"], "Maven POM": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Build.xml": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Storage Services": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Security Groups": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Multi-AZ VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon CloudFormation": ["<PERSON><PERSON><PERSON>"], "Amazon Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Microsoft Project (MPP)": ["Puneet"], "Strategic Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "EZTrieve": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Peoplesoft Financials": ["<PERSON>"], "Peoplesoft Supply Chain Management": ["<PERSON>"], "Account Payables": ["<PERSON>"], "Account Receivables": ["<PERSON>"], "GL": ["<PERSON>"], "Billing": ["<PERSON>"], "Dimension Modeling": ["<PERSON>"], "Fact Tables": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Relational Databases": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Data Mining": ["<PERSON><PERSON><PERSON>"], "DWH": ["<PERSON><PERSON><PERSON>"], "DM": ["<PERSON><PERSON><PERSON>"], "Dimension Tables": ["<PERSON><PERSON><PERSON>"], "Dashboards": ["<PERSON><PERSON><PERSON>"], "Data Security and Compliance": ["DA manager <PERSON><PERSON>"], "Product Validation": ["<PERSON><PERSON><PERSON>"], "API Development (ICS, ICRT)": ["<PERSON><PERSON><PERSON>"], "Production Support (L3)": ["<PERSON><PERSON><PERSON>"], "Test Plan Development": ["<PERSON><PERSON><PERSON>"], "Test Script Development": ["<PERSON><PERSON><PERSON>"], "IntelliJ IDEA": ["Chandra_Resume"], "Spiral Model": ["Chandra_Resume"], "Prototype Model": ["Chandra_Resume"], "Oracle Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure (OCI)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle GoldenGate (implied)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Java JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SharePoint": ["pavani_resume"], "Microsoft Office": ["pavani_resume", "<PERSON><PERSON><PERSON>"], "Ad-hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Planning": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SSMS": ["<PERSON><PERSON><PERSON><PERSON>"], "SQL Server Data Tools": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCBS)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Discount Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Dispute and Deduction Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Item Management": ["<PERSON><PERSON><PERSON><PERSON>"], "BPP Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "Certified SAP Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "PFTC Roles": ["Uday"], "SAP ECC": ["Uday"], "SAP Scripts": ["Uday"], "Device Drivers": ["A<PERSON><PERSON>_resume"], "LED Manager": ["A<PERSON><PERSON>_resume"], "Mesh Networking": ["A<PERSON><PERSON>_resume"], "Cisco Aironet": ["A<PERSON><PERSON>_resume"], "ATOM (Mainframes/AS400 automation tool)": ["SivakumarDega_CV"], "Test Automation": ["SivakumarDega_CV"], "Test Strategy Creation": ["SivakumarDega_CV"], "Test Coverage": ["SivakumarDega_CV"], "Requirements Prioritization": ["SivakumarDega_CV"], "ASP": ["PunniyaKodi V updated resume"], "N-tier applications": ["PunniyaKodi V updated resume"], "Client-server applications": ["PunniyaKodi V updated resume"], "Auto-scaling": ["PunniyaKodi V updated resume"], "Artifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Physical Database Design": ["<PERSON>"], "Database Tuning": ["<PERSON>"], "Snowpark API": ["<PERSON>"], "PII": ["<PERSON>"], "PCI": ["<PERSON>"], "Trifacta": ["<PERSON>"], "Oracle 8i": ["<PERSON>"], "Oracle 11i": ["<PERSON>"], "DB2 8.1": ["<PERSON>"], "Python Worksheet": ["<PERSON>"], "Certified Professional Data Engineer": ["<PERSON>"], "ETL Design": ["<PERSON><PERSON><PERSON>"], "ETL Development": ["<PERSON><PERSON><PERSON>"], "Python Scripting": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Data Mart": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Requirement Gathering": ["<PERSON><PERSON><PERSON>"], "Solution Architecture": ["<PERSON><PERSON><PERSON>"], "Dojo": ["<PERSON><PERSON>"], "Spring": ["Chandra_Resume"], "Oracle Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Plus": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI AutoScaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS DBX7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Defect Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Speedometer Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Sankey Diagrams": ["<PERSON><PERSON><PERSON><PERSON>"], "Pareto Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Waterfall Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "GAAP (Generally Accepted Accounting Principles)": ["<PERSON><PERSON><PERSON><PERSON>"], "IFRS (International Financial Reporting Standards)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Treasury Modules": ["<PERSON><PERSON><PERSON><PERSON>"], "LTE": ["A<PERSON><PERSON>_resume"], "5G": ["A<PERSON><PERSON>_resume"], "Firehose": ["Vidwaan_vidwan_resume"], "Test Estimation": ["SivakumarDega_CV"], "Cloud Testing (AWS, GCP, Azure, Microsoft)": ["SivakumarDega_CV"], "OAuth Testing": ["SivakumarDega_CV"], "SSO Testing": ["SivakumarDega_CV"], "KPI Development": ["DA manager <PERSON><PERSON>"], "SLA Development": ["DA manager <PERSON><PERSON>"], "Microsoft Excel Spreadsheets": ["DA manager <PERSON><PERSON>"], "Oracle 8i/11i": ["<PERSON>"], "Snowpipe": ["<PERSON>"], "Domain Driven Development (DDD)": ["PunniyaKodi V updated resume"], "Indexing": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Performance Tuning": ["<PERSON><PERSON><PERSON><PERSON>"], "Database Partitioning": ["<PERSON><PERSON><PERSON><PERSON>"], "Error Handling": ["<PERSON><PERSON><PERSON>"], "Oracle Database (11g, 10g, 9i, 8x)": ["<PERSON><PERSON><PERSON>"], "Business 360": ["<PERSON><PERSON><PERSON>"], "Data Pipelines": ["<PERSON><PERSON><PERSON>"], "Bug Fixing": ["A<PERSON><PERSON>_resume"], "Pega 8.4": ["<PERSON><PERSON><PERSON>"], "Pega 8.4.1": ["<PERSON><PERSON><PERSON>"], "PCBS (Profit Center Budgeting System)": ["<PERSON><PERSON><PERSON><PERSON>"], "FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "BPPs (Business Process Procedures) Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Product Assortment Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Event-Driven Architecture": ["<PERSON><PERSON><PERSON>il .Net lead"], "Backend for Frontend (BFF)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Active Directory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Continuous Integration/Continuous Deployment (CI/CD)": ["<PERSON><PERSON><PERSON>"], "Regression Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Component-Based Architecture": ["Chandra_Resume"], "Multi-tier Distributed Applications": ["Chandra_Resume"], "Oracle Financials Cloud Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Intelligence Publisher (BIP)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Chatbot": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS Aurora": ["<PERSON><PERSON>"], "Oracle 19c Database Administrator Training from Koenig Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Dimensions": ["<PERSON><PERSON><PERSON>"], "Cloud Data Integration": ["<PERSON><PERSON><PERSON>"], "Cloud Application Integration": ["<PERSON><PERSON><PERSON>"], "Materialized Views": ["<PERSON><PERSON><PERSON>"], "iTech Sharp": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Unit Test": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "J#": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Oracle Enterprise Manager (OEM)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAR file deployment": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI - Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Database Migration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Import/Export": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWS Storage": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Active Directory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Report Development": ["<PERSON><PERSON><PERSON><PERSON>"], "RESTful Web Services": ["PunniyaKodi V updated resume"], "General Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Management Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Certified Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "complex join queries": ["Laxman_Gite"], "Scalable architecture design": ["Laxman_Gite"], "Azure Blueprints": ["Laxman_Gite"], "Android": ["SivakumarDega_CV"], "iOS": ["SivakumarDega_CV"], "JUnit (implied)": ["SivakumarDega_CV"], "Server-Side Encryption (S3)": ["Vidwaan_vidwan_resume"], "Amazon ECR": ["<PERSON><PERSON>_DotNET"], "Tibco Messaging": ["Chary"], "Azure Container Storage": ["Chary"], "Azure Tables": ["Chary"], "Azure Queues": ["Chary"], "Azure Blobs": ["Chary"], "RICEF objects": ["Uday"], "Analytical Applications": ["Uday"], "CDS Annotations": ["Uday"], "WebIDE": ["Uday"], "UAT (User Acceptance Testing)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Software Development Lifecycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Data Collection and Storage": ["DA manager <PERSON><PERSON>"], "Lake Formation": ["<PERSON><PERSON><PERSON>"], "Fraud Detection": ["<PERSON><PERSON><PERSON>"], "FinCrime": ["<PERSON><PERSON><PERSON>"], "Windows Application": ["Puneet"], "PMO": ["Puneet"], "Change Management": ["Puneet"], "Supply Chain Management": ["Puneet"], "Organizational Change Management (OCM)": ["Puneet"], "Observable": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Winforms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Service": ["PunniyaKodi V updated resume"], "SVN Subversion": ["Chary"], "AWS (Amazon Web Services)": ["<PERSON><PERSON>_DotNET"], "Logic App": ["Laxman_Gite"]}, "skill_categories": {}, "skill_metadata": {}}