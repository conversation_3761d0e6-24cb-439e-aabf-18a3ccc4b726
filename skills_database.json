{"all_skills": ["IBM Power E980", "OpenID Connect", "CSV", "MongoDB", "OCA-Oracle Database Administrator Certified", "CAPWAP", "AWR", "Putty", "<PERSON><PERSON><PERSON>", "Pega 7.4", "ATOM (Mainframes/AS400 automation tool)", "Red Hat Linux 7", "ETL testing", "Event Grid", "EZTrieve", "Drill Through", "SSO", "Amazon Athena", "SCRUM", "Google Tag Manager", "Moq", ".NET Core", "Relational Databases", "Continuous Deployment", "IVR Testing", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "AI-powered Automation", "Data Governance", "Git", "Mainframes testing", "Team Foundation Server (TFS)", "Row-Level Security (RLS)", "OCI Auto Scaling", "Dashboards", "Service Lifting Tool Design", "Performance Testing", "Google Cloud Platform (GCP)", "ISO 27000", "<PERSON><PERSON>", "Amazon EBS", "TFS", "Database Tuning", "Peoplesoft Supply Chain Management", "SAP AR Processing & Reporting", "Cisco Aironet outdoor mesh access points", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "MS SharePoint Server", "Index Design", "Process Flows", "AKS", ".NET MAUI", "Cisco Prime Infrastructure", "WRICEF Documentation", "LambdaTest", "Project Planning", "<PERSON><PERSON>", "Redis", "C# 8.0", "Customer Accounting", "WebSphere", "Data Flows", "TypeScript", "Power Query", "Cash Discount Management", "JSF", "Desktop Application Testing", "Product Validation", "Billing", "Sufi", "Data Structures", "WAF", "SQS", "JAX-RS", "LINQ to Objects", "DynamoDB", "Oracle 19c Data Guard", "Project Management", "Customer Telephony Integration (CTI) testing", "Tridion CMS 2009", "SDLC", "Informatica 10.2", "Product Life Cycle Management (PLM)", "SAP SD", "Oracle 11i", "Data Enrichment", "Pub/Sub", "Red Hat Linux Enterprise 7", "Virtual Network", "Azure SQL Database", "Salesforce APIs", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "JSON", "WebJobs", "Activities", "Test Design", "BAPI", "DB2 8.1", "Microsoft technologies", "MySQL Aurora", "Agile Project Management", "PL/SQL", "Agile Methodology", "Intelligent Data Management Cloud (IDMC)", "EKS", "Warehouse Management", "Test Schedule Creation", "Cisco Catalyst 9800 Wireless Controller", "Microsoft Excel", "SQLite", "Oracle 19c Database Administrator", "Application Design", "Adobe Forms", "Amazon SNS", "CA DevTest", "Pa<PERSON><PERSON>", "Snowf<PERSON>a", "Clipboard", "Business Process Optimization", "Ruleset locking", "Data Migration", "Network Security", "C++", "OAuth 2.0", "SAP S/4HANA", "Spring", "<PERSON><PERSON>", "Azure App Services", "Azure Log Analytics", "Azure", "JPA", "ASME Y14.5", "HP/IBM Power E980", "SAP ECC", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "Confluent <PERSON><PERSON><PERSON>", "Data Modernization", "UDP", "System Architect (Certification)", "Data Analytics", "AWS", "C# 10.0", "Exadata CS DBX7", "ASP.NET", "Telerik", "Alert Management", "Visual Studio 2010", "Data Warehousing", "Amazon SQS", "Data Intelligence", "Azure AD", "<PERSON><PERSON><PERSON><PERSON>", "Document review", "Visual Studio Code", "Saga", "Oracle Enterprise Manager 12c", "AWS Athena", "ASP.NET Core", "Financial Analysis", "Oracle 11g/12c", "Coded UI Testing", "Amazon Web Services (S3, EC2, Lambda)", "Spring Boot", "IntelliJ IDEA", "AutoCAD", "Query Performance Optimization", "FDMEE", "OpenWRT", "Oracle Export/Import", "Application Insights", "Data Transformation", "Intercompany", "Business Process Mapping", "OUM Methodology", "PFTC", "Package locking", "SAP Fiori List Report Application", "Artificial Intelligence", "SQL*Trace", "MCA", "NetBeans", "AWS Certified Developer Associate", "Azure Entra ID", "JMeter", "802.11", "Risk Management", "Android App Development", "GDB", "Infrastructure as Code (IaC)", "Cash Pooling", "Requirements Prioritization", "Tridion CMS 2011", "Event Hub", "Data Transforms", "Process Management", "Windows Server 2016", "Vendor/Customer Open Item Management", "AT interfaces", "OCI WAF", "Real-time Data Analytics", "Web API", "Build/Release Management", "Azure Storage", "Production Planning (PP)", "High-Throughput System Architecture", "Test Planning", "EF Core", "RICEF", "Design Patterns", "Linux", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "HTML", "Java JDK", "Oracle 11g", "Certified Professional Data Engineer", "VS Code", ".NET Framework 4.7", "Security Groups", "SQR 6.0", "Ethernet", "VPC Design", "Real-time Data Analytics Solution Architecture", "Exadata X9M-2", "MathCAD", "gRPC", "Test Case Design", "M Language", "Dojo", "Logility", "AG Grid", "Power BI", "CloudFront", "Time Series Forecasting", "Operating Systems", "Oracle Data Guard", "SQL Server 2008", "BADIs", "POLARIS", "Big Data", "Salesforce Platform Administrator (Certification - In process)", "Windows Services", "User Interface Testing", "AWS-Kops (EKS)", "Microsoft Certified Professional", "DevOps", "WinSCP", "AWS Storage Services", "BDC", "<PERSON><PERSON><PERSON>", "VPN", "JBehave", "DB2", "Azure SQL Server", "iText", "Log Analytics", "SAP Material Master Data Management", "Statistical Analysis", "Pega Product Builder", "Reports", "Cloud Migration", "MuleSoft", "Cisco catalyst 3750 Switch", "Groovy", "Crystal Reports", "SAP Accounts Receivable (AR)", "Flask", "Resource Management", "ActiveMQ", "Software Architecture", "<PERSON><PERSON><PERSON>", "CSS", "AWS CloudFormation", "RFCs", "Strategic Planning", "Azure Virtual Network", "Snowflake", "Unit testing", "Shell Scripting", "C# 9.0", "HP", "Oracle RAC", "Amazon Web Services (AWS)", "Pega 7.3", "SeeTest/Experitest", "MS SQL Server 2019", "Orchestration", "SWR", "Agile (SCRUM)", "Informatica Intelligent Cloud Services (IICS)", "Appium", "AT&T Echo controller", "Circuit Breaker", "MVC", "<PERSON><PERSON><PERSON>", "Micro-services", "API Management", "SSRS (SQL Server Reporting Services)", "SAML", "Confluence", "Oracle 8i", "MS Access", "SeeTest", "SAP Profit Center Accounting (PCBS)", "ES6", "Unix Shell Scripting", "Stored Procedures", "<PERSON>adata", "SOAP", "ICS", "AML Compliance", "Kali Linux", "Auto Scaling", "Sparx Enterprise Architect", "Customer Exits", "Bug Reporting", "ADFS", "SAP Sales and Distribution (SD)", "V<PERSON>am Backup and Recovery", "Amazon S3", "WebLogic 11g", "Oracle PL/SQL", "Unix", "JMS", "IDoc", "AWS S3", "Automated Bank Reconciliation", "Agile Transformation", "IoT Systems", "<PERSON> Data Modeler", "Maven <PERSON>", "Amazon IAM", "Requirements mapping", "LINQ to SQL", "Mesh Networking", "Data Wrangling", "Red Hat Linux Enterprise 8", "Oracle Cloud", "FI-AP", "LDAP", "Real-time Data Integration", "FI-AR", "Data Security and Compliance", "International Financial Reporting Standards (IFRS)", "PySpark", "Oracle Autonomous Data Warehouse (ADW)", "Angular Reactive Forms", "PFTC Roles", "NumPy", "Sass", "Financial Reporting Studio (FRS)", "Amazon ECR (Elastic Container Registry)", "El<PERSON>", "Accounts Receivable", "NodeJS", "Risk-based testing", "N-tier applications", "Oracle Database Administration", "Financial Reporting", "Drill Down", "Data Masking", "R", "Test Script Development", "Senior System Architect", "Sanity", "<PERSON><PERSON>", "AWS Auto Scaling", "CE Marking", "Oracle Allocations", "Enterprise Class Structure", "SAP Materials Management (MM)", "Blazor", "Redux", "RTR", "Service Bus", "Test Management", "Fiori Elements", "Bluetooth", "Data Marts", "Test Estimation", "AireOS controller", "Oracle TDE", "Fault Tolerance", "Database testing", "JSTL", "High Throughput System Architecture", "Defect Tracking", "Microsoft .NET", "LED Manager", "OCI Load Balancing", "IBM Infosphere DataStage", "E-commerce Architecture Design", "SAP", "HTTP (TLS)", "IICS", "Blob Storage", "Admin Studio", "Functional Testing", "Informatica Power Center", "EN-13155", "Hypothesis Testing", "Spring Data", "WebLogic 14c", "Mac filtering", "TortoiseSVN", "Talwar controller", "<PERSON><PERSON>", "Data Pump", "Enhancement Points", "Key Management", "Mechanical Product Design", "Test effort estimation", "DBT", "MyEclipse", "IAM Role Management", "<PERSON><PERSON><PERSON>", "SAP UI5", "Microsoft Azure Certified Professional", "<PERSON><PERSON>", "T-SQL", "Message Queue", "RDBMS", "GCP", "Elastic APM", "SAFe", "A<PERSON>os", "Program Management", "Data Decryption", "RDS", "Container-based Architecture", "Selenium", "Purchase Order (PO) Management", "Quality Management (QM)", "Encryption", "MVC Design Pattern", "Swagger", "JMS Hermes", "Pega Group Benefits Insurance Framework", "TDD", "Account Receivables", "Cognito", "Functions (Database)", "Profit Center Accounting (PCA)", "Agile Methodologies", "Power BI Desktop", "Harvest", "Selenium Grid", "YAML", "Web services testing", "SAP FI-GL Transactions", "Getting Started with Power BI", "Cash Management", "BIP", "CI/CD", "NoSQL", "Compatibility Testing", "SAP Financial Accounting (FI)", "Azure Application Insights", "JDBC", "FIT-GAP Analysis", "Requirement Gathering", "Red Hat Linux", "CI/CD Pipeline", "SAP Profit Center Accounting (PCA)", "Dimension Tables", "ANSYS", "AWS Glue", "ADO.NET", "XAML", "AWS Step Functions", "Dynatrace", "Visio", "Database Migration Service", "txText Control", "OAuth", "Blueprints", "PSM", "Module Pool Programming", "CICS", "Smoke Testing", "Streamset", "j<PERSON><PERSON><PERSON>", "Test-Driven Development (TDD)", "JR<PERSON>el", "Advanced Java Development (Certification)", "Advanced Java Development", "Machined Parts Design", "Microsoft Project", "HttpClient", "Sanity Testing", "Pega 7.2.2", "Data Cleaning", "Lambda Expressions", "Quartz", "Oracle Fusion Financials", "DFA", "SAP NetWeaver Gateway", "Screen Flows", "Selenium RC", "Linux Shell Scripting", "Oracle 19c Database Administrator Training", "MIS Management", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Windows", "SAP Cash Management (CM)", "GL", "OCI CDN", "CentOS", "JDK 8", "Ad hoc Testing", "Autonomous Transaction Processing (ATP)", "OpenSearch", "OLAP", "Oracle Cloud Accounts Payable", "MicroStrategy", "S3 (Amazon S3)", "DevExpress", "Python", "AWS Aurora Postgres", "TCP", "OCA - Oracle Database Administrator Certified", "Cost Optimization", "Vendor/Customer Open Items", "Spark", "Oracle DBA", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "IoT", "YANG", "Object Oriented Programming", "Toad", "SmartView", "Vite", "Webpack", "<PERSON><PERSON>", "C", "macOS", "ISTQB Certified Tester Foundation Level", "RMAN", "Step Functions", "Disaster Recovery", "Mac filter configuration", "Repository Pattern", "Design Calculations", "Peoplesoft FSCM", "Oracle OCI", "CDH", "Tridion CMS 2013", "Test Coverage", "Redux Toolkit", "Microservices", "Order Management", "Cloud Data Governance", "Salesforce Platform Developer (Certification - In process)", "Data Loader", "FRS", "Data Validation", "Data Security", "Release Management", "Report Definitions", "Snowsight", "<PERSON><PERSON>", "API Development (ICS, ICRT)", "IFRS (International Financial Reporting Standards)", "Oracle (PL/SQL)", "Angular 9", "Data Mining", "UFT", "Kafka", "Azure API Management", "A<PERSON> (EKS)", "Queue Processors", "SQL Server 2005", "Amazon Elastic Kubernetes Service (EKS)", "Spring JPA", "Query Optimization", "JDK 17", "Spiral Model", "Amazon CloudFront", "AWS Lake Formation", "VMware", "AWS Certified SysOps Administrator Associate", "Qualcomm SDX hardware", "LeanFT", "Scenario Assessment", "RFID", "Amazon Elastic Container Registry (ECR)", "Internet of Things (IoT)", ".NET Framework", "Demand Forecasting", "HP Service Manager (HPSM)", "Resin", "Servlets", "Predictive Forecasting", "UML", "High Availability", "Google Analytics", "I-Expenses", "Transportable Tablespaces", "Client-server applications", "Salesforce Platform Administrator", "Advanced Analytics", "Pega Marketing Consultant (Certification)", "Django", "ASP", ".NET Core Apps", "Account Payables", "Senior System Architect (Certification)", "SAS Visual Analytics", "SharePoint", "AZ-304", "SAP Script", "SQL Server Reporting Services (SSRS)", "Case Management", "SAP Accounts Payable (AP)", "PLA", "MVVM", "Visual Studio 2015", "GraphQL APIs", "SAP Security", "Business Process Management (BPM)", "Snowpark API", "Test Plan Creation", "Java Development (Certification)", "Oracle 12c", "AWS Lambda", "Order to Cash Management (OTC)", "Amazon ELB", "Virtualization", "Artifactory", "Oracle Financials Cloud General Ledger", "Snow Pipe", "Oracle Cloud Fixed Assets", "Oracle Database Administrator Certified", "Tridion CMS 8.5", "Elastic Beanstalk", "Azure Data Factory (ADF)", "SPAU", "Swift", "Gulp", "ITIL Foundation 2011", "Oracle 19c", "IntelliJ", "C#", "OCI VCN", "Technical Architecture Documentation", "<PERSON><PERSON>", "Azure Cloud", "Java Development", "JSP", "ANT", "GitLab Pipelines", "Apache Kafka", "System Integration", "Angular 7", "Oracle Cloud Infrastructure", "Azure Container Registry (ACR)", "ACCELQ", "SSIS (SQL Server Integration Services)", "Business 360 Console", "ServiceNow", "ETL", "Oracle Cloud Accounts Receivable", "<PERSON><PERSON> (Project Management Professional)", "User Acceptance Testing (UAT)", "OCI-Oracle Cloud Infrastructure Foundations Associate", "Federated Database Design", "Oracle Cloud I-Expenses", "Fivetran", "Trifacta", "RabbitMQ", "Direct Connect", "OpenTelemetry", "Cascading Filters", "Azure Developer", "S3", "SAP Scripts", "Exploratory testing", "HFM", "SQL Server 2000", "Domain Driven Design", "IDMC", "Tridion CMS", "SSAS", "Perfecto", "Pig", "DFM", "Python Scripting", "PCI", "ADFS (Active Directory Federation Services)", "Circuit Breaker <PERSON>", "OKTA", "Azure Blob Storage", "Red Hat Linux 8", "Dependency Injection", "OData", "Hybrid Solution Architecture", "SoapUI", "CVS", "OLTP", "Amazon Firehose", "NUnit", "Cisco Access Points", "CloudWatch", "<PERSON> (GL)", "ADDM", "Oracle 19c RAC", "SAP Best Practices", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "Elastic Load Balancers", "ShadCN UI", "Performance Tuning", "LINQ", "YAML Pipeline", "AWS Elastic Kubernetes Service (EKS)", "Interactive Dashboards", "AZ900: Microsoft Azure Fundamentals", "Oracle Cloud Infrastructure (OCI)", "JDK", "BRF+", "FullStory", "Open API", "Teradata Certified Administrator (V2R5)", "Microsoft SQL Server", "Snow SQL", "Real-Time Data Analytics Solution Architecture Design", "Microsoft BI Stack", "Fixed Assets", "Salesforce", "Agents", "Choreography Pattern", "Predictive Modeling", "NestJS", "Informatica Data Management Center (IDMC)", "Java Mail", "Record to Report (R2R)", "SAP UI5 Framework", ".NET Framework 4.5", "Next.js", "Visual Studio 2008", "SAP Fiori Launchpad", "Star Schema", "Software Development Life Cycle (SDLC)", "CTI Testing", "TLS", "Google Data Analytics Professional Certificate", "Python Worksheet", "Server-Side Encryption", "Test Execution", "Technical Architecture", "Informatica PowerCenter", "Flat Files", "Web Testing", "IBM BMP", "Visual Studio 2019", "Triggers", "JRockit Mission Control", "Visual Studio 2013", "API Gateway", "AKS (Azure Kubernetes Service)", "Test Automation", "Test Strategy", "<PERSON>er", "Design Standardization", "<PERSON>", "ETL Design", "Angular", "SAP Certified Development Associate - SAP Fiori Application Developer", "<PERSON><PERSON><PERSON>", "DBMS", "Web Services", "Oracle 21c", "SSMS", "AJAX", "Rule Resolution", "Oracle Grid Control 11g", "PCBS", "Dispute and Deduction Management", "Repository Design Pattern", "Excel", "Multi-AZ VPC", "Microservices testing", "Summary-View Reports", "TestNG", "Web Jobs", "ClearCase", "List-View", "ETL Development", "ECS", "WCF", "Test Case Execution", "React.js", "Key Performance Indicators (KPIs)", "Azure Kubernetes Service (AKS)", "JCL", "SAS Visual Investigator", "Hyperion Financial Management (HFM)", "AWS Certified Developer - Associate", "Real-time Data Ingestion", "Oracle 10g", "Ubus", "Strapi CMS", "Business Intelligence", "Procurement Processes", "JUnits", "VB.NET", "RBAC", "JavaScript", "Azure Cloud Architectures", "BrowserStack", "Crontab", "OCI Object Storage", "Amazon VPC", "Custom Visuals (Power BI)", "Windows 2007/2008/2010", "Apache POI", "Mantine UI", "JDeveloper", "ETL Processes", "Gre", "MS Azure", "VCN", "SQL Server 2017", "Smart View", "Kendo UI", ".NET Core 8.0", "AWS Certified Solutions Architect Associate", "Onshore Rigging Calculations", "HP/IBM Power E850", "EXPLAIN PLAN", "Prism", "Single Page Application (SPA)", "CDL", "HTTP", "Rally", "5G", "Container-based Architecture Design", "Data Automation", "GraphQL", "Waterfall Methodologies", "Pega Marketing Consultant", "OCI IAM", "Oracle Financial Accounting Hub", "Speedometer Charts", "Peoplesoft Financials", "Reverse Engineering", "Amazon Lambda", "Physical Database Design", "Scalability", "Data Storage Strategies", "Factory Pattern", "Interactive Voice Response (IVR) testing", "Ping Identity", "SiteMinder", "EC2", "Regression Testing", "Oracle Real Application Cluster", "ASP.NET Core 6.0", "Terraform", "Spark SQL", "Browser compatibility testing", "Blue Yonder", "ios-xe asr 1K router", "Service Extension", "SAP CO", "AR Processing & Reporting", "Data Gateway", "CosmosDB", "Extended Warehouse Management (EWM)", "NuGet", "Visual Studio 2003", "Brainbench C# 5.0", "SAP Integration", "Node.js", "ReactJS", "Dimension Modeling", "Scrum Master", "Logic Apps", "Smart Forms", "SolidWorks", "EJB", "SAS Data Integration Studio", "Integration Testing", "VSAM", "Cloud Data Catalog", "FI-GL (FICO)", "App Services", "ISO27001 Compliance", "XML", "Data Mart", "AWS CLI", "Databricks", "Test Strategy Creation", "PMP", "DM", "BP Integrations", "SQL Server Integration Services (SSIS)", "STATSPACK", "Hibernate", "QTP", "SAP AP/AR Transactions", "SSIS", "Java", "<PERSON><PERSON><PERSON>", "Automated Data Entry", "Oracle Cloud Budgetary Control", "AIM Methodology", "API testing", "Amazon Glue", "AMDP", "Oracle E-Business Suite R12", "SAP Procurement Processes", "PHP", "User Exits", "<PERSON><PERSON>", "Data Pipelining", "RACI Matrix", "Visual Studio 2012", "WiFi", "AGGrid", "WCF RESTful", "Business Workflow", "Amplify", "Oracle Cloud Financials", "CloudFormation", "Data Collection", "Sitecore", "PostgreSQL", "Fact Tables", "Athena", "GitHub Copilot", "Spring MVC", "Azure Active Directory", "HP ALM", "IIS", "WebLogic 12c", "AngularJS", "Azure Storage Services", "Order to Cash (O2C)", "Windows Server", "Oracle", "Data Visualization", "File-Aid", "IAM", "PivotTables", "Android Testing", "Caliburn.Micro", "Sigma", "ITIL Foundation", "Table Storage", ".NET", "OTT", "OCI AutoScaling", "Mobile Web Testing", "Styled Components", "Kubernetes", "OTBI", ".NET Framework 4.0", "Sheet Metal Design", "Azure Functions", "ATOM", "Design and Analysis of Algorithms", "Device Drivers", "GAAP (Generally Accepted Accounting Principles)", "Material UI", "Microsoft Azure", "HP Quality Center", "Pareto Charts", "Hangfire", "Cobol", "Snowpark", "<PERSON><PERSON>", "Data Dictionary (DDIC)", "WebLogic", "Data Encryption", "Dev Studio", "Service-Oriented Architecture (SOA)", "Neb<PERSON>", "Power BI Service", "Column <PERSON>", "WLAN", "OAuth2", "Peoplesoft HCM", "Dimensional Modeling", "Service Level Agreement (SLAs)", "ISO/IEC 27001", "Oracle Financials Cloud Accounts Payable", "Spring IOC", "RESTful APIs", "Struts 2", "BTP", "IBM MQ", "GitHub", "Data Profiling", "Order-to-Delivery Process", "Ansible", "Windows Application Migration", "Test Plan Development", "Data Compliance", "ABAP", "Parameters", "HTML5", "SVN", "ELT", "OBIEE", "SAP Controlling (CO)", "J2EE", "Machinery Directive 2006/42/EC", "Test Driven Development (TDD)", "Web IDE", "SAP FI", "SOA", "Terada<PERSON>", "API", "Amazon Auto Scaling", "Quality Center", "Tomcat", "End-to-End testing", "DWH", "Sub Ledger Accounting (SLA)", "Oracle Cloud General Ledger", "Factory Design Pattern", "MySQL", "Declarative Rules", "Oracle Transactional Business Intelligence (OTBI)", "Apache", "Sections", "Coded UI", "<PERSON><PERSON><PERSON>", "Production Support", "F110 Automatic Payment Program", ".NET Core 6.0", "Saga Pattern", "Cross-functional Collaboration", "Deep Learning", "ISO 20000", "Oracle Enterprise Manager", "Abstract Factory Design Pattern", "<PERSON><PERSON><PERSON>ber", "Agile", "Visual Studio 2022", "Tracer", "DAX", "Salesforce Platform Developer", "Gateway Aggregation", "Accounts Payable", "C# Programming Certified Professional", "Product locking", "Amazon EKS", "Query Plan Optimization", "Oracle 8x", "Regulatory Reporting", "Informatica Data Management Cloud (IDMC)", "VPC", "Android Studio", "LTE", "Apache Airflow", "SQL Server 2016", "Oracle GoldenGate (implied)", "Talwar Simulator", "Data Warehouse testing", "The Complete Python Developer", "Containerization", "Automation Testing", "Avro", "IOS Testing", "Normalization", "Mainframe Testing", "React", "AWS CDK", "TKPROF", "Component Localization", "Dependency Inversion Principle", "AWS Certified SysOps Administrator - Associate", "Service Registration", "Procure to Pay (PTP)", "Visual Studio 2005", "CSS3", "XML Parser", "Slowly Changing Dimensions (SCD)", "Stakeholder Management", "Selenium WebDriver", "Entity Framework", "Subversion", "Firebase Cloud Services", "Prince2", "Azure Logic Apps", "Mechanical Component Design", "REST Assured", "<PERSON><PERSON>", "Postman", "Antifactory", "SCM", "Lambda", "Classification", "Microsoft Project (MPP)", "Production Support (L3)", "SQL Server 2012", "SAP Warehouse Management", "Business Objects (BO)", "DNVGL", ".NET Framework 2.0", "High Throughput System Architecture Design", "ATC", "Machine Learning", "NETCONF", "Inhouse Cash Management", "Cisco Aironet", "SEO", "Cost Analysis", "OOPS", "OIDC", "SonarQube", "ASP.NET MVC", "Automatic Payment Program (F110)", "Selenium IDE", "ISO 27001", "Abstract Factory Pattern", "Amazon Lake Formation", "Azure Key Vault", "Power Automate", "MS SQL Server", "Team Foundation Server", "Autonomous Data Warehouse (ADW)", "BPP Documentation", "Defect Management", "EN ISO 50308", "SEO Optimization", "E-Commerce", "Azure Data Lake", "Amazon Web Services", "BottleRocket", "Apache Tomcat", "SSRS", "Auto-scaling", "Manual Testing", "Microservices Architecture", "Mechanical Design", "Active Reports", "Wix", "Design FMEA", "Spring Framework", "Log4j", ".NET Framework 3.5", "Data Processing", "User Acceptance testing", "Bamboo", "SQL*Plus", "Visual Studio", "Apache ECharts", "Bitbucket", "Service Virtualization", "Choreography", "Financial Data Management (FDMEE)", "AZ-204", "Amazon CloudFormation", "Solution Architecture", "CRM", "Sankey Diagrams", "Code Review", "Nx Monorepo", "Insurance", "Subversion (SVN)", "MudBlazor", "REST", "ICRT", "Ka<PERSON><PERSON>", "SmartSheet", "Web Dynpro ABAP", "SAP Solution Design", "GlassFish", "AIF", "Oracle Cloud Cash Management", "Prototype", "IBM LTO 8", "Material Master Data Management", "REST APIs", "AWS API Gateway", "Data Modeling", "IP", "Model-View-Controller (MVC)", "Angular 12", "JUnit", "Source Code Management (SCM)", "Amazon Route 53", "CMMI Level 5", "Hive", "PI/PO", "CATIA", "<PERSON><PERSON><PERSON><PERSON>", "Test Driven Development", "Domain Driven Design (DDD)", "Delivery Management", "Serverless Architecture", "Decision Rules", "Continuous Integration and Continuous Deployment (CI/CD)", "Azure App Service", "SQLTrace", "Oracle 9i", "<PERSON>", "Amazon ECS", "<PERSON><PERSON>", "Netezza", "Data Quality", "Multi-AZ", "ALV Reports", "System Testing", "Pivotal Cloud Foundry", "Inbound/Outbound Proxy", "Waterfall Charts", "IBM Power E850", "CDS Views", "Amazon EC2", "Generally Accepted Accounting Principles (GAAP)", "App Insights", ".NET 6", "MS Excel", "Azure SQL", "HPSM", "Omniture", "Waterfall", "@task", "QMI", "Data Analysis", "Web Application Automation", "Talend", "PII", "Application Load Balancer", "Procure to Pay (P2P)", "2D Drawing Review", "YAML Pipelines", "SeeTest (Experitest)", "Prototype Model", "System Architect", "Oracle Cloud I-Receivables", "Angular 8", "<PERSON><PERSON>", "AWS Certified Solutions Architect - Associate", "EN ISO 14122", "ITIL V3 Foundation", "Web Application Testing", "Azure DevOps", "Object-Oriented ABAP (OOABAP)", "Tortoise SVN", "<PERSON><PERSON><PERSON>", "2D Drawings Review", "WPF", "Amazon Redshift", "Mobile Application Automation", "JAX-WS", "Azure Service Fabric", "SQL Server Data Tools", "I-Receivables", "Docker Registries", "Azure Active Directory (Azure AD)", "DDD", "OOAD", "Cosmos DB", "CQRS", "Ad-hoc Testing", "High-Performance Architecture Design", "Data Integration", "Predictive Analysis", "Hyperion FRS", "<PERSON><PERSON>", "BSP", "Material Design", "ElasticSearch", "Tibco", "AWS SAM", "Razor View Engine", "Configuration Management", "Object-Oriented Programming (OOP)", "Power Pivot", "ASPX", "Data Management", "Object-Oriented Programming", "SAP MM Functionalities", "Microsoft Power BI", "Test Scripting", "Flow Actions", "Spring REST", "GitLab", "Stimulsoft", "RAP", "Azure Service Bus", "Oracle Fusion Applications", "Continuous Integration", "Object Storage", "Pricing Strategies", "IBM DB2", "E-commerce Architecture", "Order-to-Delivery Process Optimization", "Mobile Testing", "Bootstrap", "Advanced Planner Optimizer (APO)", "Dashboarding", "Azure Container Registry", "Visual Studio 2017", "Informatica Cloud Services (IICS)", "SQL", "React Router", "CAPM", "DataMart", "ASAP Methodology", "Inventory Planning", "Azure APIM", "Eclipse", "ASP.NET Core 8.0", "Pivotal Cloud Foundry (PCF)", "Firehose", "SOLID principles", "Oracle Autonomous Transaction Processing (ATP)", "SQL Server 2014", "Amazon DynamoDB", "Certified SAP Functional Consultant", "Pega 8", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "NoSQL Databases", "Oracle Enterprise Manager Grid Control 11g", "Service Lifting Tools Design", "GD&T", "IBM LTO 9", "App Studio", "Technical Design Documentation", "Amazon CloudWatch", "Build.xml", "Functions", "<PERSON>", "SPDD", "Regression", "<PERSON>", "Data Architecture", "Technical Design", "Jersey REST", "Azure Pipelines", "ALE IDOCs", "Data Capture (CDC)", "General <PERSON><PERSON>", "Domain-Driven Design (DDD)", "Cypress", "SAP Treasury Modules", "Enterprise Reporting", "SAP MM", "AZ-104", "Azure Data Factory", "SNS", "Ezetrieves", "Karate Framework", "Data Flow Architectures", "XML Web Services", "SQL Server", "Client Management", "Silverlight", "Stack Up Analysis", "Azure Monitor", "Real-time Cash Visibility System", "Entity Framework 7.0", "AeroScout tags", "Test Case Development", "JWT", "Angular 10", "Pega Rules Process Engine", "PowerShell", "InstallShield", "UG-NX", "Microsoft Office", "Datadog", "Tailwind CSS", "UI Testing", "CDN", "Bank Reconciliation", "Amazon RDS"], "skill_frequency": {"Python": 64, "JavaScript": 47, "Java": 53, "C#": 31, "SQL": 62, ".NET 6": 6, "ASP.NET Core": 18, "ASP.NET MVC": 14, "Angular": 45, "Web API": 24, "Azure": 33, "Azure Functions": 24, "Azure Developer": 6, "Azure Logic Apps": 5, "Azure Service Bus": 11, "Azure API Management": 1, "Azure Storage": 11, "Cosmos DB": 10, "Redis Cache": 6, "Azure Active Directory (Azure AD)": 2, "Azure Virtual Network": 1, "Azure Application Insights": 1, "Azure Log Analytics": 1, "Azure Key Vault": 1, "Azure Monitor": 11, "Azure Container Registry": 6, "Azure Service Fabric": 6, "Azure Data Lake": 9, "YAML Pipelines": 4, "Docker": 35, "Kubernetes": 31, "CI/CD": 41, "Microservices": 22, "Serverless Architecture": 10, "HTML": 42, "CSS": 27, "jQuery": 33, "Event Grid": 6, "Event Hub": 6, "SQL Server": 39, "MySQL": 42, "Snowflake": 17, "T-SQL": 17, "PL/SQL": 19, "Stored Procedures": 18, "Triggers": 8, "Functions (Database)": 1, "Amazon Web Services (AWS)": 35, "Microsoft Azure": 17, "Agile Methodology": 11, "Design Patterns": 14, "Microservices Architecture": 6, "Federated Database Design": 4, "Container-based Architecture": 3, "High-Throughput System Architecture": 2, "Real-time Data Analytics Solution Architecture": 3, "E-commerce Architecture": 4, "Hybrid Solution Architecture": 4, "VPC Design": 3, "Direct Connect": 4, "VPN": 4, "Query Performance Optimization": 5, "Data Modeling": 32, "Microsoft Certified Professional": 2, "WPF": 5, "MVC": 6, "MS Azure": 4, "WCF": 18, "Blob Storage": 5, "Table Storage": 5, "App Services": 5, "Redis": 10, "App Insights": 5, "Azure APIM": 5, "Logic Apps": 10, "AZ900: Microsoft Azure Fundamentals": 5, "Caliburn.Micro": 5, "Prism": 5, "Entity Framework 7.0": 5, "XML Parser": 5, "LINQ": 12, "Stimulsoft": 5, "Angular Reactive Forms": 5, "HttpClient": 5, "NUnit": 10, "Coded UI Testing": 4, "ADO.NET": 19, "SQL Server Reporting Services (SSRS)": 6, "Strapi CMS": 4, "Windows Services": 9, "WCF RESTful": 5, "MS SQL Server 2019": 3, "PostgreSQL": 34, "SQLite": 5, "Oracle (PL/SQL)": 3, "MS Access": 10, "InstallShield": 5, "GitHub": 31, "TFS": 13, "SVN": 26, "IIS": 5, "Apache Tomcat": 5, "DevExpress": 5, "Brainbench C# 5.0": 4, "MVVM": 2, "ASP.NET": 14, ".NET Framework": 10, "Entity Framework": 13, "EF Core": 5, "Razor View Engine": 5, "Bootstrap": 26, "CosmosDB": 5, "ElasticSearch": 13, "Apache Kafka": 18, "ActiveMQ": 5, "Pivotal Cloud Foundry": 5, "Azure App Service": 8, "Node.js": 30, "React": 20, "OAuth2": 5, "Swagger": 10, "OOPS": 5, "SOLID principles": 9, "Team Foundation Server (TFS)": 5, "Git": 41, "Jira": 53, "Azure DevOps": 33, "Moq": 5, "Agile Methodologies": 6, "SCRUM": 19, "Waterfall Methodologies": 5, "Test-Driven Development (TDD)": 8, "C++": 14, "Service Bus": 5, "API Management": 5, "YAML Pipeline": 2, "Azure AD": 4, "Virtual Network": 5, "Application Insights": 5, "Log Analytics": 5, "Key Vault": 5, "Functions": 6, "Software Architecture": 2, "Micro-services": 1, "High Throughput System Architecture": 1, "Microsoft Azure Certified Professional": 2, "MCA": 2, "Open API": 4, "C": 15, ".NET Core": 12, "AJAX": 13, "AngularJS": 4, "ReactJS": 13, "XML": 22, "HTML5": 16, "Sass": 4, "TypeScript": 28, "JSON": 32, "DynamoDB": 13, "OpenSearch": 4, "EC2": 14, "CloudFront": 4, "IAM": 15, "ECS": 4, "SQS": 12, "SNS": 12, "Lambda": 6, "API Gateway": 13, "RDS": 4, "CloudWatch": 15, "Step Functions": 6, "Elastic Cache": 4, "NodeJS": 4, "AGGrid": 3, "txText Control": 4, "ASPX": 4, "SOAP": 29, "RESTful APIs": 4, "Crystal Reports": 9, "Active Reports": 4, "SSRS": 14, "SSIS": 13, "YAML": 4, "Terraform": 13, "DDD": 1, "TDD": 1, "Agile": 48, "NuGet": 3, "Object-Oriented Programming (OOP)": 6, "VB.NET": 4, "Domain Driven Design": 1, "Test Driven Development": 1, "Elastic APM": 4, "OpenTelemetry": 4, "FullStory": 4, "Google Analytics": 8, "ASP.NET Core 6.0": 2, "ASP.NET Core 8.0": 2, ".NET MAUI": 4, "XAML": 4, "C# 8.0": 2, "C# 9.0": 2, "C# 10.0": 2, "Web Services": 4, "REST": 16, "Angular 7": 2, "Angular 8": 2, "Angular 9": 2, "Angular 10": 2, "Angular 12": 2, "Material Design": 4, ".NET Framework 2.0": 2, ".NET Framework 3.5": 2, ".NET Framework 4.0": 2, ".NET Framework 4.5": 2, ".NET Framework 4.7": 3, "CI/CD Pipeline": 4, "Splunk": 12, "RabbitMQ": 4, "Amazon DynamoDB": 8, "Kendo UI": 8, "Amazon EC2": 19, "AWS Lambda": 15, "Azure App Services": 5, "WebJobs": 1, "Azure Active Directory": 4, "ServiceNow": 11, "HP Service Manager (HPSM)": 3, "Service-Oriented Architecture (SOA)": 3, "OAuth 2.0": 4, "OKTA": 8, "Azure Entra ID": 4, "Bitbucket": 17, "Team Foundation Server": 4, "Subversion (SVN)": 7, "TortoiseSVN": 6, "Visual Studio 2003": 2, "Visual Studio 2005": 2, "Visual Studio 2008": 2, "Visual Studio 2010": 2, "Visual Studio 2012": 2, "Visual Studio 2013": 2, "Visual Studio 2015": 2, "Visual Studio 2017": 2, "Visual Studio 2019": 2, "Visual Studio 2022": 2, "Azure Cloud Architectures": 1, "Azure Storage Services": 1, "Azure SQL Database": 5, "OpenID Connect": 1, "Ping Identity": 1, "Salesforce APIs": 1, "CQRS": 4, "Saga Pattern": 3, "Choreography Pattern": 3, "Gateway Aggregation": 3, "Circuit Breaker Pattern": 3, "Message Queue": 1, "MuleSoft": 4, "Kafka": 12, "Tibco": 4, "AKS (Azure Kubernetes Service)": 2, "MVC Design Pattern": 3, "Repository Pattern": 3, "Dependency Inversion Principle": 4, "Dependency Injection": 4, "Factory Pattern": 3, "Abstract Factory Pattern": 3, "Tridion CMS 2009": 2, "Tridion CMS 2011": 2, "Tridion CMS 2013": 2, "Tridion CMS 8.5": 2, "Sitecore": 4, "SEO Optimization": 3, "Omniture": 4, "Google Tag Manager": 4, "SQL Server 2000": 2, "SQL Server 2005": 2, "SQL Server 2008": 2, "SQL Server 2012": 2, "SQL Server 2014": 2, "SQL Server 2017": 2, "Azure SQL Server": 1, "Oracle PL/SQL": 10, "Selenium": 4, "Azure Data Factory": 4, "PMP (Project Management Professional)": 2, "Agile (SCRUM)": 3, "Kanban": 8, "AZ-104": 4, "AZ-204": 4, "AZ-304": 4, "Machine Learning": 10, "Deep Learning": 4, "Predictive Analysis": 4, "Artificial Intelligence": 4, "IoT Systems": 1, ".NET": 8, "gRPC": 4, "SSIS (SQL Server Integration Services)": 4, "SSRS (SQL Server Reporting Services)": 2, "LINQ to SQL": 4, "LINQ to Objects": 4, "Lambda Expressions": 4, "S3 (Amazon S3)": 2, "Amazon Elastic Kubernetes Service (EKS)": 4, "Amazon ECR (Elastic Container Registry)": 2, "Elastic Beanstalk": 8, "Application Load Balancer": 3, "NoSQL": 4, "Datadog": 4, "Azure Container Registry (ACR)": 4, "Azure Kubernetes Service (AKS)": 5, "Azure Blob Storage": 9, "Blazor": 4, "MudBlazor": 4, "Telerik": 4, "Redux": 4, "Hangfire": 4, "ADFS (Active Directory Federation Services)": 2, "Tableau": 29, "DB2": 16, "SAP": 8, "IDoc": 4, "Logility": 3, "Blue Yonder": 3, "CloudFormation": 4, "VPC": 4, "Jenkins": 38, "SonarQube": 8, "Antifactory": 3, "AWS Elastic Kubernetes Service (EKS)": 4, "ANT": 16, "Maven": 36, "Shell Scripting": 10, "Ansible": 8, "PowerShell": 9, "Tomcat": 20, "JBoss": 12, "WebLogic": 12, "WebSphere": 12, "Windows Server": 4, "Red Hat Linux": 3, "Unix": 12, "CentOS": 3, "VMware": 4, "Elastic Load Balancers": 3, "Waterfall": 23, "Batch Scripting": 3, "Amazon ECS": 4, "Amazon S3": 17, "Amazon EBS": 4, "Amazon VPC": 4, "Amazon ELB": 4, "Amazon SNS": 5, "Amazon RDS": 4, "Amazon IAM": 4, "Amazon Route 53": 4, "AWS CloudFormation": 7, "AWS Auto Scaling": 3, "Amazon CloudFront": 4, "Amazon CloudWatch": 5, "AWS CLI": 3, "Vault": 4, "Docker Hub": 4, "Docker Registries": 3, "AWS Kops (EKS)": 2, "Groovy": 4, "GitLab": 13, "Apache": 1, "Grafana": 4, "Pivotal Cloud Foundry (PCF)": 4, "Infrastructure as Code (IaC)": 4, "Configuration Management": 8, "Containerization": 4, "Orchestration": 4, "Build/Release Management": 4, "Source Code Management (SCM)": 3, "HTTP (TLS)": 1, "Key Management": 1, "Encryption": 1, "J2EE": 10, "SAFe": 4, "Confluence": 12, "Microsoft Project": 3, "SmartSheet": 4, "DevOps": 4, "Warehouse Management": 4, "CMMI Level 5": 8, "PMP": 5, "PSM": 3, "Agile Project Management": 4, "Scrum Master": 4, "Program Management": 8, "Project Management": 8, "Project Planning": 8, "Risk Management": 8, "Cost Analysis": 8, "Resource Management": 4, "Stakeholder Management": 4, "Delivery Management": 4, "Client Management": 4, "Release Management": 4, "Microsoft Excel": 5, "Azure Cloud": 10, "Cobol": 12, "Ezetrieves": 3, "IBM BMP": 4, "ISO 27001": 4, "DBT": 4, "AWS": 14, "Azure Data Factory (ADF)": 4, "Databricks": 8, "Database Migration Service": 4, "AWS Glue": 11, "Fivetran": 4, "Snow SQL": 4, "Streamset": 4, "Snowpark": 4, "Column Masking": 3, "Data Encryption": 4, "Data Decryption": 4, "Data Masking": 4, "Data Governance": 5, "Hive": 8, "Pig": 4, "Sqoop": 4, "PySpark": 5, "Sigma": 4, "Apache Airflow": 4, "Informatica Power Center": 1, "Talend": 9, "Peoplesoft FSCM": 3, "Peoplesoft HCM": 4, "Oracle": 18, "MS SQL Server": 9, "OLTP": 8, "OLAP": 8, "Data Warehousing": 24, "Data Architecture": 3, "Data Integration": 11, "ELT": 3, "ETL": 20, "Data Quality": 12, "Real-time Data Ingestion": 3, "Snow Pipe": 3, "Confluent Kafka": 3, "Snowsight": 4, "SQR 6.0": 2, "Avro": 4, "Parquet": 4, "CSV": 7, "Index Design": 4, "Query Plan Optimization": 3, "Data Analysis": 8, "Business Intelligence": 11, "Data Management": 4, "ETL Processes": 4, "Excel": 5, "Power BI": 17, "DAX": 9, "Statistical Analysis": 4, "Regression": 4, "Hypothesis Testing": 4, "Predictive Modeling": 4, "Time Series Forecasting": 4, "Classification": 4, "Data Cleaning": 4, "Data Transformation": 9, "Data Automation": 4, "PivotTables": 4, "Power Query": 9, "Pandas": 4, "NumPy": 4, "SQL Server Integration Services (SSIS)": 4, "R": 4, "Google Data Analytics Professional Certificate": 4, "Getting Started with Power BI": 4, "The Complete Python Developer": 4, "ISTQB Certified Tester Foundation Level": 4, "Informatica PowerCenter": 11, "IICS": 1, "IDMC": 1, "IBM Infosphere DataStage": 4, "SAS Data Integration Studio": 4, "Oracle 11g": 8, "Oracle 10g": 8, "Oracle 9i": 4, "Oracle 8x": 4, "Microsoft SQL Server": 4, "Amazon Redshift": 4, "Data Migration": 8, "Data Modernization": 4, "Data Enrichment": 4, "Data Validation": 6, "Data Processing": 3, "Data Pipelining": 4, "Data Visualization": 8, "Enterprise Reporting": 3, "Dashboarding": 1, "AWS Athena": 4, "AWS Lake Formation": 3, "Microsoft Power BI": 4, "OBIEE": 4, "SAS Visual Investigator": 4, "SAS Visual Analytics": 4, "Erwin Data Modeler": 8, "Sparx Enterprise Architect": 4, "RDBMS": 8, "Star Schema": 13, "Snowflake Schema": 13, "Slowly Changing Dimensions (SCD)": 4, "Normalization": 4, "Flat Files": 3, "Predictive Forecasting": 3, "Alert Management": 3, "Regulatory Reporting": 3, "AML Compliance": 1, "Data Intelligence": 2, "Scenario Assessment": 1, "MIS Management": 2, "MS Excel": 3, "Data Security": 3, "Data Wrangling": 4, "Visual Studio": 6, "Mechanical Product Design": 4, "Mechanical Component Design": 1, "System Integration": 4, "Sheet Metal Design": 4, "Machined Parts Design": 4, "Design Standardization": 4, "Component Localization": 4, "Cost Optimization": 4, "Design Calculations": 4, "Cross-functional Collaboration": 3, "Onshore Rigging Calculations": 4, "Service Lifting Tool Design": 3, "Process Management": 4, "UG-NX": 4, "SolidWorks": 4, "CATIA": 4, "AutoCAD": 4, "ANSYS": 4, "Design FMEA": 4, "DFM": 4, "DFA": 4, "GD&T": 4, "Stack Up Analysis": 4, "ASME Y14.5": 4, "2D Drawing Review": 2, "MathCAD": 4, "CE Marking": 4, "DNVGL": 4, "EN-13155": 4, "Machinery Directive 2006/42/EC": 4, "EN ISO 50308": 4, "EN ISO 14122": 4, "Reverse Engineering": 4, "Informatica Cloud Services (IICS)": 4, "Intelligent Data Management Cloud (IDMC)": 4, "Netezza": 4, "Teradata": 5, "Windows": 13, "Spark": 4, "Data Profiling": 4, "Business 360 Console": 4, "Cloud Data Governance": 4, "Cloud Data Catalog": 4, "Data Marts": 2, "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": 1, "Data Capture (CDC)": 4, "API": 2, "ICS": 2, "ICRT": 2, "Nifi": 4, "Technical Design Documentation": 2, "Technical Architecture Documentation": 2, "Production Support": 7, "Code Review": 7, "Redux Toolkit": 4, "Axios": 4, "SWR": 4, "Formik": 4, "React Router": 4, "CSS3": 8, "ES6": 2, "Material UI": 4, "Tailwind CSS": 4, "PHP": 4, "Amazon Lambda": 4, "Gulp": 4, "Grunt": 4, "Webpack": 4, "GitHub Copilot": 3, "JWT": 4, "RBAC": 3, "Software Development Life Cycle (SDLC)": 1, "Spring Boot": 17, "Struts 2": 4, "Spring IOC": 4, "Spring MVC": 8, "Spring Data": 4, "Spring REST": 4, "Jersey REST": 4, "JSF": 4, "Apache POI": 4, "iText": 4, "Servlets": 8, "JSP": 8, "JDBC": 8, "JAX-WS": 4, "JAX-RS": 4, "Java Mail": 4, "JMS": 8, "JUnits": 3, "IBM MQ": 4, "Amazon EKS": 4, "Cucumber": 9, "Cypress": 8, "Dojo Toolkit": 3, "MongoDB": 12, "Quartz": 4, "Hibernate": 8, "Spring JPA": 4, "Putty": 4, "WinSCP": 4, "Bamboo": 4, "AWS Aurora Postgres": 4, "EJB": 4, "JSTL": 4, "JPA": 4, "Struts": 4, "Spring Framework": 3, "NestJS": 8, "WebLogic 11g": 4, "GlassFish": 8, "Resin": 4, "Oracle 11g/12c": 4, "IBM DB2": 4, "Eclipse": 4, "NetBeans": 4, "JDeveloper": 4, "IntelliJ": 3, "MyEclipse": 4, "VS Code": 4, "Toad": 14, "Visio": 4, "UML": 4, "CVS": 4, "SoapUI": 9, "JMS Hermes": 4, "JUnit": 24, "Log4j": 4, "JRockit Mission Control": 4, "JMeter": 4, "JRebel": 4, "Spiral": 3, "Prototype": 3, "Google Cloud Platform (GCP)": 8, "ITIL Foundation 2011": 1, "AWS Certified Solutions Architect Associate": 3, "AWS Certified Developer Associate": 3, "AWS Certified SysOps Administrator Associate": 3, "Dynatrace": 4, "LDAP": 4, "SiteMinder": 4, "SAML": 4, "Harvest": 4, "Nx Monorepo": 4, "OOAD": 3, "SOA": 4, "Single Page Application (SPA)": 3, "AWS CDK": 9, "@task": 4, "GitLab Pipelines": 3, "Oracle DBA": 3, "Oracle OCI": 3, "Oracle 19c": 4, "Oracle 12c": 4, "Oracle 21c": 4, "Oracle RAC": 3, "Oracle Data Guard": 4, "Oracle Enterprise Manager": 3, "Oracle TDE": 4, "Data Pump": 4, "Oracle Cloud Infrastructure": 2, "RMAN": 4, "Linux Shell Scripting": 4, "Crontab": 4, "AWR": 4, "ADDM": 4, "EXPLAIN PLAN": 4, "SQL*Trace": 2, "TKPROF": 4, "STATSPACK": 4, "WebLogic 14c": 4, "WebLogic 12c": 4, "JDK": 2, "SQL Server 2016": 4, "Veeam Backup and Recovery": 4, "Red Hat Linux 7": 3, "Red Hat Linux 8": 3, "Exadata": 4, "IBM LTO 9": 4, "IBM LTO 8": 4, "OCI IAM": 3, "OCI VCN": 3, "OCI Object Storage": 3, "OCI Load Balancing": 3, "OCI Auto Scaling": 2, "OCI CDN": 3, "OCI WAF": 3, "Autonomous Data Warehouse (ADW)": 3, "Autonomous Transaction Processing (ATP)": 3, "ITIL V3 Foundation": 4, "Prince2": 4, "Oracle Database Administrator Certified": 1, "OCA - Oracle Database Administrator Certified": 2, "Oracle 19c Database Administrator Training": 2, "Teradata Certified Administrator (V2R5)": 4, "OCI-Oracle Cloud Infrastructure Foundations Associate certified": 1, "Oracle Fusion Applications": 4, "Oracle E-Business Suite R12": 5, "Oracle Cloud Financials": 4, "Oracle Cloud General Ledger": 2, "Oracle Cloud Accounts Payable": 2, "Oracle Cloud Accounts Receivable": 2, "Oracle Cloud Fixed Assets": 2, "Oracle Cloud Cash Management": 2, "Oracle Cloud I-Expenses": 2, "Oracle Cloud Budgetary Control": 5, "Oracle Financial Accounting Hub": 5, "Oracle Transactional Business Intelligence (OTBI)": 4, "Financial Reporting Studio (FRS)": 4, "Smart View": 4, "Data Loader": 5, "Hyperion FRS": 5, "Business Process Management (BPM)": 4, "AIM Methodology": 5, "OUM Methodology": 5, "Sub Ledger Accounting (SLA)": 5, "Windows 2007/2008/2010": 5, "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": 5, "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": 5, "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": 5, "1Z0-517 - Oracle EBS R12.1 Payables Essentials": 5, "BIP": 5, "Pega Rules Process Engine": 4, "Pega Group Benefits Insurance Framework": 4, "Pega Product Builder": 2, "Pega 7.2.2": 4, "Pega 7.3": 4, "Pega 7.4": 4, "Pega 8": 4, "Unit testing": 3, "Harness": 4, "Sections": 4, "Flow Actions": 4, "List-View": 4, "Summary-View Reports": 4, "Report Definitions": 4, "Clipboard": 4, "Tracer": 4, "PLA": 4, "Product locking": 1, "Package locking": 1, "Ruleset locking": 1, "SDLC": 7, "E-Commerce": 4, "Insurance": 4, "Agents": 4, "Queue Processors": 4, "Decision Rules": 4, "Declarative Rules": 4, "Application Design": 4, "Case Management": 4, "Process Flows": 4, "Screen Flows": 4, "Data Transforms": 4, "Activities": 4, "Rule Resolution": 4, "Enterprise Class Structure": 4, "Dev Studio": 4, "App Studio": 4, "Admin Studio": 4, "CDH": 4, "Document review": 4, "Pega Marketing Consultant": 1, "Senior System Architect": 1, "System Architect": 1, "Postman": 13, "Selenium IDE": 5, "Selenium RC": 5, "Selenium WebDriver": 15, "Selenium Grid": 5, "TestNG": 15, "QTP": 5, "Gherkin": 5, "Ruby": 9, "Tortoise SVN": 3, "HP Quality Center": 5, "SeeTest (Experitest)": 2, "ACCELQ": 5, "JBehave": 5, "HP ALM": 9, "BrowserStack": 5, "LambdaTest": 5, "Functional Testing": 10, "Smoke Testing": 5, "System Testing": 10, "Integration Testing": 11, "Regression Testing": 10, "User Acceptance Testing (UAT)": 5, "UI Testing": 9, "Mobile Testing": 10, "Automation Testing": 6, "Web Testing": 5, "Compatibility Testing": 5, "Sanity Testing": 5, "Ad hoc Testing": 4, "Test Case Design": 4, "Test Plan Creation": 7, "Test Scripting": 1, "Test Execution": 5, "Defect Tracking": 4, "Bug Reporting": 2, "Test Management": 6, "AI-powered Automation": 3, "Mobile Application Automation": 1, "Web Application Automation": 1, "IOS Testing": 4, "Android Testing": 4, "SSAS": 5, "Power BI Desktop": 5, "Power BI Service": 5, "M Language": 5, "Dimensional Modeling": 5, "Microsoft BI Stack": 5, "Power Pivot": 5, "Data Gateway": 5, "Row-Level Security (RLS)": 4, "Data Flows": 4, "DataMart": 4, "Power Automate": 5, "Visual Studio Code": 5, "SAP FI": 1, "SAP CO": 1, "SAP SD": 1, "SAP MM": 1, "SAP Cash Management (CM)": 5, "ASAP Methodology": 5, "HFM": 1, "FDMEE": 1, "PCBS": 1, "WRICEF Documentation": 5, "Business Process Mapping": 5, "FIT-GAP Analysis": 5, "Financial Reporting": 5, "SAP Solution Design": 5, "SAP Warehouse Management": 5, "Material Master Data Management": 4, "Procurement Processes": 4, "Order-to-Delivery Process": 3, "Demand Forecasting": 5, "Cash Pooling": 5, "Bank Reconciliation": 4, "F110 Automatic Payment Program": 2, "Real-time Cash Visibility System": 5, "Inhouse Cash Management": 5, "SAP Best Practices": 4, "Generally Accepted Accounting Principles (GAAP)": 4, "International Financial Reporting Standards (IFRS)": 4, "Financial Analysis": 5, "Automated Data Entry": 1, "AR Processing & Reporting": 1, "Customer Accounting": 1, "Vendor/Customer Open Items": 1, "SAP Integration": 1, "SAP S/4HANA": 4, "ABAP": 4, "OData": 4, "SAP UI5": 4, "Fiori": 4, "Fiori Elements": 4, "PI/PO": 4, "AIF": 4, "BRF+": 4, "Business Workflow": 4, "CRM": 4, "Web Dynpro ABAP": 4, "RAP": 4, "BTP": 4, "CAPM": 4, "Procure to Pay (PTP)": 4, "Order to Cash Management (OTC)": 4, "Production Planning (PP)": 4, "Quality Management (QM)": 4, "FI-AP": 4, "FI-AR": 4, "FI-GL (FICO)": 4, "RTR": 4, "SCM": 5, "Product Life Cycle Management (PLM)": 4, "Advanced Planner Optimizer (APO)": 4, "Extended Warehouse Management (EWM)": 4, "Data Dictionary (DDIC)": 4, "Module Pool Programming": 4, "Object-Oriented ABAP (OOABAP)": 4, "RFCs": 4, "BADIs": 4, "BDC": 4, "BAPI": 4, "BP Integrations": 4, "Enhancement Points": 4, "User Exits": 4, "Customer Exits": 4, "ALE IDOCs": 4, "Inbound/Outbound Proxy": 1, "SAP NetWeaver Gateway": 4, "Service Registration": 4, "Service Extension": 4, "CDS Views": 4, "AMDP": 4, "SAP Fiori List Report Application": 4, "Web IDE": 4, "BSP": 4, "SAP Fiori Launchpad": 4, "SAP UI5 Framework": 1, "Business Objects (BO)": 4, "ATC": 4, "SPDD": 4, "SPAU": 4, "SAP Security": 4, "PFTC": 3, "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": 4, "SAP Certified Development Associate - SAP Fiori Application Developer": 4, "Next.js": 4, "REST APIs": 12, "GraphQL APIs": 4, "AWS SAM": 4, "Apache ECharts": 4, "Cognito": 4, "OIDC": 4, "Mantine UI": 4, "Vite": 4, "MySQL Aurora": 4, "AWS API Gateway": 1, "Styled Components": 4, "Sanity": 4, "Amplify": 4, "ShadCN UI": 4, "Salesforce": 4, "CDL": 4, "Cisco Catalyst 9800 Wireless Controller": 4, "Talwar controller": 4, "AireOS controller": 4, "Cisco Access Points": 4, "Talwar Simulator": 4, "WiFi": 4, "802.11": 4, "WLAN": 4, "Ethernet": 4, "IP": 4, "TCP": 4, "UDP": 4, "CAPWAP": 4, "NETCONF": 4, "YANG": 4, "Swift": 4, "ClearCase": 4, "Cisco catalyst 3750 Switch": 4, "ios-xe asr 1K router": 4, "OpenWRT": 4, "Linux": 11, "QMI": 4, "AT interfaces": 4, "Ubus": 3, "Qualcomm SDX hardware": 4, "AT&T Echo controller": 3, "POLARIS": 4, "GDB": 4, "Gre": 4, "RFID": 4, "AeroScout tags": 4, "Cisco Aironet outdoor mesh access points": 3, "Cisco Prime Infrastructure": 4, "Mac filtering": 1, "Bash": 5, "Android App Development": 5, "Flask": 5, "Django": 5, "GraphQL": 5, "Amazon Web Services": 5, "macOS": 5, "Kali Linux": 5, "OAuth": 10, "AWS Certified Solutions Architect - Associate": 6, "Amazon SQS": 1, "Amazon Athena": 2, "Amazon Glue": 1, "Amazon Firehose": 1, "AWS Step Functions": 1, "Data Structures": 6, "Test Driven Development (TDD)": 4, "Mockito": 8, "Spark SQL": 4, "Server-Side Encryption": 4, "IAM Role Management": 2, "EKS": 4, "BottleRocket": 4, "React.js": 4, "Firebase Cloud Services": 4, "Cassandra": 4, "Android Studio": 4, "Bluetooth": 4, "Java Development": 3, "Advanced Java Development": 3, "Salesforce Platform Administrator": 3, "Salesforce Platform Developer": 3, "Appium": 5, "Perfecto": 5, "SeeTest": 5, "REST Assured": 5, "Karate Framework": 5, "UFT": 5, "LeanFT": 5, "Zephyr": 4, "Quality Center": 4, "Informatica 10.2": 5, "MicroStrategy": 5, "CICS": 5, "JCL": 5, "VSAM": 5, "Sufi": 5, "File-Aid": 5, "CA DevTest": 5, "ATOM": 2, "GCP": 5, "SSO": 5, "Test Strategy": 1, "Test Design": 3, "Test effort estimation": 2, "Requirements mapping": 3, "Risk-based testing": 5, "End-to-End testing": 5, "User Acceptance testing": 5, "Database testing": 5, "API testing": 5, "Web services testing": 5, "Microservices testing": 4, "Browser compatibility testing": 5, "Exploratory testing": 5, "ETL testing": 4, "Data Warehouse testing": 5, "Interactive Voice Response (IVR) testing": 3, "Customer Telephony Integration (CTI) testing": 3, "Mainframes testing": 3, "Service Virtualization": 4, "Continuous Integration and Continuous Deployment (CI/CD)": 1, "Oracle Fusion Financials": 1, "Oracle Financials Cloud General Ledger": 3, "Oracle Financials Cloud Accounts Payable": 3, "Accounts Payable": 3, "Accounts Receivable": 3, "General Ledger": 3, "Fixed Assets": 3, "Cash Management": 3, "I-Expenses": 3, "I-Receivables": 3, "Order Management": 3, "OTBI": 1, "FRS": 1, "SmartView": 1, "Procure to Pay (P2P)": 2, "Order to Cash (O2C)": 2, "Record to Report (R2R)": 2, "Oracle Allocations": 2, "Oracle Cloud": 1, "Intercompany": 3, "SeeTest/Experitest": 3, "Test Case Development": 1, "Test Schedule Creation": 1, "SAP Financial Accounting (FI)": 4, "SAP Controlling (CO)": 4, "SAP Sales and Distribution (SD)": 4, "SAP Materials Management (MM)": 4, "Hyperion Financial Management (HFM)": 4, "Financial Data Management (FDMEE)": 4, "Profit Center Accounting (PCA)": 1, "SAP Accounts Receivable (AR)": 2, "SAP Accounts Payable (AP)": 2, "General Ledger (GL)": 3, "Purchase Order (PO) Management": 1, "Inventory Planning": 3, "Automatic Payment Program (F110)": 3, "Network Security": 2, "Object Oriented Programming": 2, "Operating Systems": 2, "Design and Analysis of Algorithms": 2, "DBMS": 2, "Mainframe Testing": 2, "Performance Testing": 2, "User Interface Testing": 4, "Manual Testing": 1, "Mobile Web Testing": 3, "Desktop Application Testing": 1, "Web Application Testing": 1, "Data Analytics": 3, "Real-time Data Analytics": 1, "NoSQL Databases": 1, "Blueprints": 2, "Test Case Execution": 2, "Custom Visuals (Power BI)": 2, "Drill Down": 2, "Drill Through": 2, "Parameters": 2, "Cascading Filters": 2, "Interactive Dashboards": 1, "Reports": 1, "SAP Profit Center Accounting (PCA)": 1, "Automated Bank Reconciliation": 1, "Pricing Strategies": 1, "SAP MM Functionalities": 1, "Business Process Optimization": 1, "IVR Testing": 1, "CTI Testing": 1, "Continuous Integration": 3, "Continuous Deployment": 2, "High-Performance Architecture Design": 1, "Container-based Architecture Design": 1, "High Throughput System Architecture Design": 1, "Real-Time Data Analytics Solution Architecture Design": 1, "E-commerce Architecture Design": 1, "Microsoft technologies": 2, "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": 1, "Coded UI": 1, "MS SharePoint Server": 2, ".NET Core 6.0": 1, ".NET Core 8.0": 1, "XML Web Services": 2, ".NET Core Apps": 1, "Domain Driven Design (DDD)": 2, "Web Jobs": 3, "Model-View-Controller (MVC)": 1, "Tridion CMS": 2, "Internet of Things (IoT)": 2, "Azure SQL": 3, "Azure Pipelines": 3, "Rally": 2, "Multi-AZ": 1, "High Availability": 2, "Disaster Recovery": 1, "AWS-Kops (EKS)": 2, "HTTP": 3, "TLS": 3, "Windows Application Migration": 1, "OTT": 1, "RACI Matrix": 1, "S3": 4, "Performance Tuning": 2, "Query Optimization": 1, "Informatica Intelligent Cloud Services (IICS)": 3, "Informatica Data Management Center (IDMC)": 2, "Amazon Lake Formation": 1, "Cloud Migration": 3, "Nebula": 3, "Advanced Analytics": 2, "Data Compliance": 1, "Key Performance Indicators (KPIs)": 1, "Service Level Agreement (SLAs)": 1, "Data Flow Architectures": 1, "Data Collection": 1, "Data Storage Strategies": 1, "Agile Transformation": 1, "ISO27001 Compliance": 1, "Mechanical Design": 1, "Service Lifting Tools Design": 1, "2D Drawings Review": 2, "Unix Shell Scripting": 2, "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": 3, "Technical Design": 2, "Technical Architecture": 2, "Big Data": 3, "Real-time Data Integration": 1, "Amazon Web Services (S3, EC2, Lambda)": 2, "Silverlight": 1, "ITIL Foundation": 3, "AWS Certified Developer - Associate": 1, "AWS Certified SysOps Administrator - Associate": 1, "Oracle 19c Data Guard": 1, "Oracle 19c RAC": 1, "Red Hat Linux Enterprise 8": 1, "Red Hat Linux Enterprise 7": 1, "Oracle Enterprise Manager 12c": 2, "Oracle Enterprise Manager Grid Control 11g": 1, "Oracle Export/Import": 1, "Transportable Tablespaces": 1, "SQLTrace": 2, "Oracle Real Application Cluster": 1, "Windows Server 2016": 3, "Fault Tolerance": 1, "Scalability": 1, "Virtualization": 1, "Oracle Autonomous Data Warehouse (ADW)": 1, "Oracle Autonomous Transaction Processing (ATP)": 1, "VCN": 1, "Object Storage": 1, "Load Balancing": 1, "Auto Scaling": 1, "CDN": 1, "WAF": 1, "Exadata X9M-2": 3, "HP": 1, "IBM Power E980": 1, "IBM Power E850": 1, "OCI-Oracle Cloud Infrastructure Foundations Associate": 3, "OCA-Oracle Database Administrator Certified": 2, "Oracle 19c Database Administrator": 1, "ISO/IEC 27001": 1, "ISO 20000": 1, "ISO 27000": 1, "Pega Marketing Consultant (Certification)": 3, "Senior System Architect (Certification)": 3, "System Architect (Certification)": 3, "RICEF": 2, "SAP Script": 2, "Smart Forms": 3, "Adobe Forms": 3, "ALV Reports": 3, "Mac filter configuration": 1, "Athena": 2, "JDK 8": 1, "JDK 17": 1, "Java Development (Certification)": 1, "Advanced Java Development (Certification)": 1, "Salesforce Platform Administrator (Certification - In process)": 1, "Salesforce Platform Developer (Certification - In process)": 1, "C# Programming Certified Professional": 1, "Strapi": 1, "Wix": 1, "AG Grid": 1, "Domain-Driven Design (DDD)": 1, "Pub/Sub": 2, "HPSM": 1, "Subversion": 1, "Saga": 1, "Choreography": 1, "Circuit Breaker": 1, "AKS": 1, "Repository Design Pattern": 1, "Factory Design Pattern": 1, "Abstract Factory Design Pattern": 1, "SEO": 1, "Object-Oriented Programming": 1, "IoT": 1, "Microsoft .NET": 1, "AWS S3": 3, "Amazon Elastic Container Registry (ECR)": 2, "ADFS": 2, "Maven POM": 1, "Build.xml": 1, "AWS Storage Services": 1, "Security Groups": 1, "Multi-AZ VPC": 1, "Amazon CloudFormation": 1, "Amazon Auto Scaling": 1, "Microsoft Project (MPP)": 1, "Strategic Planning": 1, "EZTrieve": 1, "Peoplesoft Financials": 1, "Peoplesoft Supply Chain Management": 1, "Account Payables": 2, "Account Receivables": 2, "GL": 1, "Billing": 2, "Dimension Modeling": 2, "Fact Tables": 3, "Relational Databases": 1, "Data Mining": 1, "DWH": 1, "DM": 1, "Dimension Tables": 1, "Dashboards": 2, "Data Security and Compliance": 1, "Product Validation": 1, "API Development (ICS, ICRT)": 2, "Production Support (L3)": 2, "Test Plan Development": 1, "Test Script Development": 1, "IntelliJ IDEA": 1, "Spiral Model": 1, "Prototype Model": 1, "Oracle Database Administration": 1, "Oracle Cloud Infrastructure (OCI)": 1, "Oracle GoldenGate (implied)": 1, "Java JDK": 2, "Oracle Cloud I-Receivables": 1, "SharePoint": 1, "Microsoft Office": 2, "Ad-hoc Testing": 1, "Test Planning": 1, "SSMS": 1, "SQL Server Data Tools": 1, "SAP Profit Center Accounting (PCBS)": 2, "SAP AR Processing & Reporting": 1, "Cash Discount Management": 1, "Dispute and Deduction Management": 1, "SAP FI-GL Transactions": 1, "SAP AP/AR Transactions": 1, "Vendor/Customer Open Item Management": 1, "BPP Documentation": 1, "Order-to-Delivery Process Optimization": 1, "Certified SAP Functional Consultant": 1, "PFTC Roles": 1, "SAP ECC": 1, "SAP Scripts": 1, "Device Drivers": 1, "LED Manager": 1, "Mesh Networking": 1, "Cisco Aironet": 1, "ATOM (Mainframes/AS400 automation tool)": 1, "Test Automation": 1, "Test Strategy Creation": 2, "Test Coverage": 2, "Requirements Prioritization": 1, "ASP": 1, "N-tier applications": 1, "Client-server applications": 1, "Auto-scaling": 1, "Artifactory": 1, "Physical Database Design": 1, "Database Tuning": 1, "Snowpark API": 1, "PII": 1, "PCI": 1, "Trifacta": 1, "Oracle 8i": 1, "Oracle 11i": 1, "DB2 8.1": 1, "Python Worksheet": 1, "Certified Professional Data Engineer": 1, "ETL Design": 1, "ETL Development": 1, "Python Scripting": 1, "Informatica Data Management Cloud (IDMC)": 1, "Data Mart": 1, "Requirement Gathering": 1, "Solution Architecture": 1, "Dojo": 1, "Spring": 1, "Oracle Grid Control 11g": 1, "SQL*Plus": 1, "OCI AutoScaling": 1, "HP/IBM Power E980": 1, "HP/IBM Power E850": 1, "Exadata CS DBX7": 1, "Defect Management": 1, "Speedometer Charts": 1, "Sankey Diagrams": 1, "Pareto Charts": 1, "Waterfall Charts": 1, "SAP Material Master Data Management": 1, "SAP Procurement Processes": 1, "GAAP (Generally Accepted Accounting Principles)": 1, "IFRS (International Financial Reporting Standards)": 1, "SAP Treasury Modules": 1, "LTE": 1, "5G": 1, "Firehose": 1, "Test Estimation": 1}, "skill_by_consultant": {"Laxman_Gite": ["C#", ".NET 6", "ASP.NET Core", "ASP.NET MVC", "Angular", "Web API", "Azure", "Azure Functions", "Azure Developer", "Azure Logic Apps", "Azure Service Bus", "Azure API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "Azure Active Directory (Azure AD)", "Azure Virtual Network", "Azure Application Insights", "Azure Log Analytics", "Azure Key Vault", "Azure Monitor", "Azure Container Registry", "Azure Service Fabric", "Azure Data Lake", "YAML Pipelines", "<PERSON>er", "Kubernetes", "CI/CD", "Microservices", "Serverless Architecture", "HTML", "CSS", "j<PERSON><PERSON><PERSON>", "Event Grid", "Event Hub", "SQL Server", "MySQL", "Snowflake", "T-SQL", "PL/SQL", "Stored Procedures", "Triggers", "Functions (Database)", "Amazon Web Services (AWS)", "Microsoft Azure", "Agile Methodology", "Design Patterns", "Microservices Architecture", "Federated Database Design", "Container-based Architecture", "High-Throughput System Architecture", "Real-time Data Analytics Solution Architecture", "E-commerce Architecture", "Hybrid Solution Architecture", "VPC Design", "Direct Connect", "VPN", "Query Performance Optimization", "Data Modeling", "Microsoft Certified Professional", "Logic Apps", "Service Bus", "API Management", "YAML Pipeline", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "Functions", "Software Architecture", "Micro-services", "High Throughput System Architecture", "Microsoft Azure Certified Professional", "MCA", "Data Analytics", "Real-time Data Analytics", "NoSQL Databases", "Blueprints", "High-Performance Architecture Design", "Container-based Architecture Design", "High Throughput System Architecture Design", "Real-Time Data Analytics Solution Architecture Design", "E-commerce Architecture Design", "Microsoft technologies", "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional", "C# Programming Certified Professional"], "Zeeshan_Farooqui_Dot_Net_Full_Stack_Developer": ["C#", "ASP.NET Core", "Web API", "WPF", "MVC", "MS Azure", "WCF", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "AZ900: Microsoft Azure Fundamentals", "Caliburn.Micro", "Prism", "Entity Framework 7.0", "XML Parser", "LINQ", "Stimulsoft", "Angular", "Angular Reactive Forms", "HttpClient", "NUnit", "Coded UI Testing", "SQL Server", "T-SQL", "ADO.NET", "SQL Server Reporting Services (SSRS)", "Strapi CMS", "Windows Services", "WCF RESTful", "MS SQL Server 2019", "PostgreSQL", "SQLite", "Oracle (PL/SQL)", "MS Access", "InstallShield", "GitHub", "TFS", "SVN", "IIS", "Apache Tomcat", "DevExpress", "Brainbench C# 5.0", "MVVM", "Coded UI", "Oracle PL/SQL", "MS SharePoint Server", "MySQL", "Azure", "SSRS", "<PERSON><PERSON><PERSON>", "Wix", "Git"], "Vivek Anil .Net lead": ["ASP.NET", "ASP.NET Core", ".NET Framework", "C#", "ADO.NET", "Entity Framework", "EF Core", "Razor View Engine", "Bootstrap", "SQL Server", "CosmosDB", "ElasticSearch", "JavaScript", "j<PERSON><PERSON><PERSON>", "Angular", "Microservices", "Azure", "Apache Kafka", "ActiveMQ", "Pivotal Cloud Foundry", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Node.js", "React", "Web API", "OAuth2", "Swagger", "OOPS", "SOLID principles", "Design Patterns", "Team Foundation Server (TFS)", "Git", "SVN", "<PERSON><PERSON>", "Azure DevOps", "GitHub", "Azure Service Bus", "NUnit", "Moq", "Agile Methodologies", "SCRUM", "Waterfall Methodologies", "Test-Driven Development (TDD)", "CI/CD", "C++", "Python", "HTML", "WCF", "Open API", "C", "Pivotal Cloud Foundry (PCF)", "MVC"], "PunniyaKodi V updated resume": ["C#", ".NET Framework", ".NET Core", "ASP.NET MVC", "ASP.NET", "Web API", "Windows Services", "WCF", "j<PERSON><PERSON><PERSON>", "AJAX", "AngularJS", "ReactJS", "SQL", "PL/SQL", "LINQ", "ADO.NET", "XML", "HTML", "HTML5", "CSS", "Sass", "Bootstrap", "JavaScript", "TypeScript", "JSON", "SQL Server", "PostgreSQL", "DynamoDB", "OpenSearch", "Amazon Web Services (AWS)", "EC2", "CloudFront", "IAM", "ECS", "SQS", "SNS", "Lambda", "API Gateway", "RDS", "CloudWatch", "Step Functions", "ElasticSearch", "El<PERSON>", "Entity Framework", "NodeJS", "AGGrid", "txText Control", "ASPX", "SOAP", "RESTful APIs", "Crystal Reports", "Active Reports", "SSRS", "SSIS", "TFS", "Azure DevOps", "CI/CD", "YAML", "Terraform", "DDD", "TDD", "Agile", "SCRUM", "NuGet", "Object-Oriented Programming (OOP)", "VB.NET", "Domain Driven Design", "Test Driven Development", "Elastic APM", "OpenTelemetry", "FullStory", "Google Analytics", ".NET Framework 4.7", ".NET Core 6.0", ".NET Core 8.0", "Angular", "React", "XML Web Services", ".NET Core Apps", "Domain Driven Design (DDD)", "Test-Driven Development (TDD)", "T-SQL", "MS SQL Server", "Node.js", "AG Grid", "Domain-Driven Design (DDD)", "Pub/Sub", "ASP", "N-tier applications", "Client-server applications", ".NET", "Auto-scaling"], "Chary": ["ASP.NET Core 6.0", "ASP.NET Core 8.0", "ASP.NET MVC", "ASP.NET", ".NET MAUI", "XAML", "C# 8.0", "C# 9.0", "C# 10.0", "Java", "SOLID principles", "WCF", "Web API", "Web Services", "Microservices", "REST", "SOAP", "Angular 7", "Angular 8", "Angular 9", "Angular 10", "Angular 12", "Material Design", "Bootstrap", "ReactJS", "TypeScript", "JavaScript", "j<PERSON><PERSON><PERSON>", ".NET Framework 2.0", ".NET Framework 3.5", ".NET Framework 4.0", ".NET Framework 4.5", ".NET Framework 4.7", "Azure DevOps", "CI/CD Pipeline", "<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON><PERSON>", "Azure Logic Apps", "RabbitMQ", "Amazon DynamoDB", "Kendo UI", "Amazon EC2", "AWS Lambda", "Azure App Services", "Azure Functions", "WebJobs", "Azure Active Directory", "ServiceNow", "HP Service Manager (HPSM)", "Service-Oriented Architecture (SOA)", "OAuth 2.0", "OKTA", "Azure Entra ID", "Bitbucket", "Team Foundation Server", "Subversion (SVN)", "TortoiseSVN", "Visual Studio 2003", "Visual Studio 2005", "Visual Studio 2008", "Visual Studio 2010", "Visual Studio 2012", "Visual Studio 2013", "Visual Studio 2015", "Visual Studio 2017", "Visual Studio 2019", "Visual Studio 2022", "Azure Cloud Architectures", "Azure Storage Services", "Azure SQL Database", "OpenID Connect", "Ping Identity", "Salesforce APIs", "CQRS", "Saga Pattern", "Choreography Pattern", "API Gateway", "Gateway Aggregation", "Circuit Breaker <PERSON>", "Message Queue", "MuleSoft", "Kafka", "Tibco", "AKS (Azure Kubernetes Service)", "MVC Design Pattern", "Repository Pattern", "Dependency Inversion Principle", "Dependency Injection", "Factory Pattern", "Abstract Factory Pattern", "Tridion CMS 2009", "Tridion CMS 2011", "Tridion CMS 2013", "Tridion CMS 8.5", "Sitecore", "SEO Optimization", "Omniture", "Google Analytics", "Google Tag Manager", "SQL Server 2000", "SQL Server 2005", "SQL Server 2008", "SQL Server 2012", "SQL Server 2014", "SQL Server 2017", "Azure SQL Server", "SSIS", "SSRS", "Oracle PL/SQL", "Stored Procedures", "Data Modeling", "Object-Oriented Programming (OOP)", "Design Patterns", "Python", "Selenium", "Azure Data Lake", "Azure Data Factory", "<PERSON><PERSON> (Project Management Professional)", "Agile (SCRUM)", "Ka<PERSON><PERSON>", "AZ-104", "AZ-204", "AZ-304", "Machine Learning", "Deep Learning", "Predictive Analysis", "Artificial Intelligence", "IoT Systems", "ASP.NET Core", "C#", "Angular", ".NET Framework", "Web Jobs", "Visual Studio", "Azure Kubernetes Service (AKS)", "Model-View-Controller (MVC)", "Tridion CMS", "SQL Server", "PMP", "Internet of Things (IoT)", "HPSM", "SOA", "Subversion", "Saga", "Choreography", "Circuit Breaker", "AKS", "Repository Design Pattern", "Factory Design Pattern", "Abstract Factory Design Pattern", "Azure Cloud", "SEO", "Object-Oriented Programming", "Agile", "SCRUM", "IoT"], "Donish Devasahayam_DotNET": ["C#", ".NET", ".NET Core", "ASP.NET", "gRPC", "Angular", "Azure", "SQL Server", "SSIS (SQL Server Integration Services)", "SSRS (SQL Server Reporting Services)", "ADO.NET", "Entity Framework", "LINQ", "LINQ to SQL", "LINQ to Objects", "Lambda Expressions", "Python", "<PERSON>er", "Kubernetes", "Amazon Web Services (AWS)", "S3 (Amazon S3)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon ECR (Elastic Container Registry)", "Elastic Beanstalk", "Application Load Balancer", "NoSQL", "Datadog", "Azure Container Registry (ACR)", "Azure Kubernetes Service (AKS)", "Azure App Service", "Azure Blob Storage", "Azure Functions", "Cosmos DB", "Azure SQL Database", "Kafka", "Blazor", "MudBlazor", "Telerik", "Kendo UI", "React", "Redux", "Hangfire", "ADFS (Active Directory Federation Services)", "<PERSON><PERSON>", "DB2", "SAP", "IDoc", "Logility", "Blue Yonder", "Azure SQL", "AKS (Azure Kubernetes Service)", "Azure Pipelines", "<PERSON><PERSON>", "Rally", "Microsoft .NET", "SQL Server Integration Services (SSIS)", "SQL Server Reporting Services (SSRS)", "Azure App Services", "AWS S3", "Amazon Elastic Container Registry (ECR)", "ADFS", "Amazon S3"], "Kondaru_04_Manjunath_Resume": ["Amazon Web Services (AWS)", "CloudFormation", "VPC", "IAM", "<PERSON>", "SonarQube", "Antifactory", "Kubernetes", "Terraform", "AWS Elastic Kubernetes Service (EKS)", "ANT", "<PERSON><PERSON>", "Shell Scripting", "<PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CloudWatch", "GitHub", "Ansible", "CI/CD", "Git", "PowerShell", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Windows Server", "Red Hat Linux", "Unix", "CentOS", "VMware", "Elastic Load Balancers", "EC2", "Agile", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON>", "Linux", "Multi-AZ", "High Availability", "Disaster Recovery", "Maven <PERSON>", "Build.xml", "AWS Storage Services", "Security Groups", "Multi-AZ VPC", "Artifactory"], "Jhansi P": ["Amazon Web Services (AWS)", "Azure", "Amazon EC2", "Amazon ECS", "Elastic Beanstalk", "Amazon S3", "Amazon EBS", "Amazon VPC", "Amazon ELB", "Amazon SNS", "Amazon RDS", "Amazon IAM", "Amazon Route 53", "AWS CloudFormation", "AWS Auto Scaling", "Amazon CloudFront", "Amazon CloudWatch", "Amazon DynamoDB", "AWS Lambda", "Python", "Java", "AWS CLI", "MySQL", "<PERSON><PERSON>", "Ansible", "<PERSON>er", "<PERSON><PERSON>", "Docker Registries", "Kubernetes", "A<PERSON> (EKS)", "<PERSON>", "ANT", "<PERSON><PERSON>", "Groovy", "Subversion (SVN)", "Git", "GitHub", "GitLab", "Tomcat", "WebLogic", "Apache", "ElasticSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pivotal Cloud Foundry (PCF)", "Infrastructure as Code (IaC)", "Configuration Management", "CI/CD", "Containerization", "Orchestration", "Build/Release Management", "Source Code Management (SCM)", "HTTP (TLS)", "Key Management", "Encryption", "AWS-Kops (EKS)", "HTTP", "TLS", "Amazon CloudFormation", "Amazon Auto Scaling", "Amazon Lambda", "AWS", "Azure DevOps", "Pivotal Cloud Foundry", "SCM"], "Puneet": ["J2EE", "Java", "Agile", "SCRUM", "SAFe", "Ka<PERSON><PERSON>", "<PERSON><PERSON>", "Confluence", "Microsoft Project", "SmartSheet", "<PERSON>", "SonarQube", "CI/CD", "DevOps", "SAP", "Warehouse Management", "CMMI Level 5", "PMP", "PSM", "Windows Application Migration", "OTT", "RACI Matrix", "Microsoft Project (MPP)"], "Pradeep_Project_manager_Nithin1": ["Agile Project Management", "Scrum Master", "Program Management", "Project Management", "Project Planning", "Risk Management", "Cost Analysis", "Resource Management", "Stakeholder Management", "Delivery Management", "Client Management", "Release Management", "<PERSON><PERSON>", "Confluence", "Azure DevOps", "ServiceNow", "Microsoft Excel", ".NET", "Angular", "Node.js", "SQL", "Azure Cloud", "Cobol", "Ezetrieves", "C#", "IBM BMP", "CMMI Level 5", "ISO 27001", "Strategic Planning", "EZTrieve"], "Kamal": ["Snowflake", "DBT", "AWS", "Azure Data Factory (ADF)", "Databricks", "Database Migration Service", "Amazon S3", "AWS Glue", "API Gateway", "CloudWatch", "SNS", "SQS", "IAM", "EC2", "Fivetran", "Snow SQL", "Streamset", "Snowpark", "Python", "SQL", "Stored Procedures", "Column <PERSON>", "Data Encryption", "Data Decryption", "Data Masking", "Data Governance", "GitHub", "Hive", "Pig", "<PERSON><PERSON><PERSON>", "PySpark", "Kafka", "<PERSON><PERSON>", "Sigma", "Apache Airflow", "Informatica Power Center", "Talend", "Peoplesoft FSCM", "Peoplesoft HCM", "JSON", "XML", "Oracle", "DB2", "MS SQL Server", "OLTP", "OLAP", "Data Warehousing", "Data Architecture", "Data Integration", "Data Modeling", "ELT", "ETL", "Data Quality", "Real-time Data Ingestion", "Snow Pipe", "Confluent <PERSON><PERSON><PERSON>", "Snowsight", "SQR 6.0", "Avro", "Pa<PERSON><PERSON>", "CSV", "Index Design", "Query Plan Optimization", "S3", "Git", "Informatica PowerCenter", "Business Intelligence", "Data Migration", "Performance Tuning", "Query Optimization", "Peoplesoft Financials", "Peoplesoft Supply Chain Management", "Account Payables", "Account Receivables", "GL", "Billing", "Data Validation", "Dimension Modeling", "Fact Tables", "Physical Database Design", "Database Tuning", "Snowpark API", "PII", "PCI", "Trifacta", "SQL Server", "Oracle 8i", "Oracle 11i", "DB2 8.1", "<PERSON> (GL)", "Python Worksheet", "Certified Professional Data Engineer"], "Aiswarya Sukumaran Data analyst": ["Data Analysis", "Business Intelligence", "Data Management", "ETL Processes", "SQL", "Python", "Excel", "Data Modeling", "MySQL", "PostgreSQL", "Data Warehousing", "Power BI", "<PERSON><PERSON>", "DAX", "Statistical Analysis", "Regression", "Hypothesis Testing", "Predictive Modeling", "Time Series Forecasting", "Classification", "Data Cleaning", "Data Transformation", "Data Automation", "PivotTables", "Power Query", "<PERSON><PERSON>", "NumPy", "Agile", "<PERSON><PERSON>", "SQL Server Integration Services (SSIS)", "R", "Google Data Analytics Professional Certificate", "Getting Started with Power BI", "The Complete Python Developer", "ISTQB Certified Tester Foundation Level", "SSIS (SQL Server Integration Services)", "Relational Databases"], "Himanshu": ["Python", "SQL", "Oracle PL/SQL", "Informatica PowerCenter", "IICS", "IDMC", "AWS Glue", "IBM Infosphere DataStage", "SAS Data Integration Studio", "Oracle 11g", "Oracle 10g", "Oracle 9i", "Oracle 8x", "Microsoft SQL Server", "Amazon Redshift", "PostgreSQL", "Stored Procedures", "Functions", "Triggers", "Data Warehousing", "Data Modeling", "ETL", "Data Integration", "Data Migration", "Data Modernization", "Data Enrichment", "Data Quality", "Data Validation", "Data Processing", "Data Transformation", "Data Pipelining", "Data Visualization", "Enterprise Reporting", "Dashboarding", "Business Intelligence", "Amazon S3", "Amazon EC2", "AWS Lambda", "AWS Athena", "AWS Lake Formation", "AWS CloudFormation", "Microsoft Azure", "Snowflake", "Microsoft Power BI", "<PERSON><PERSON>", "OBIEE", "SAS Visual Investigator", "SAS Visual Analytics", "<PERSON> Data Modeler", "Sparx Enterprise Architect", "Agile", "RDBMS", "OLAP", "OLTP", "Star Schema", "Snowf<PERSON>a", "Slowly Changing Dimensions (SCD)", "Normalization", "Flat Files", "CSV", "JSON", "XML", "Predictive Forecasting", "Alert Management", "Regulatory Reporting", "AML Compliance", "Data Intelligence", "Scenario Assessment", "MIS Management", "Informatica Intelligent Cloud Services (IICS)", "Informatica Data Management Center (IDMC)", "Amazon Web Services (AWS)", "Amazon Athena", "Amazon Lake Formation", "Cloud Migration", "Data Governance", "Neb<PERSON>", "AWS S3", "Data Mining", "DWH", "DM", "Fact Tables", "Dimension Tables", "Dashboards", "Advanced Analytics", "ETL Design", "ETL Development", "Python Scripting", "Informatica Data Management Cloud (IDMC)", "Data Mart", "Project Management", "Requirement Gathering", "Solution Architecture"], "DA manager Nithin": ["MS Excel", "SQL", "Python", "<PERSON><PERSON>", "Power BI", "Data Analysis", "Data Visualization", "Data Security", "Data Warehousing", "Data Modeling", "Data Wrangling", "ETL", "Azure Cloud", "Visual Studio", "<PERSON><PERSON>", "Cost Analysis", "Risk Management", "Program Management", "Project Planning", "Agile", "Business Intelligence", "Advanced Analytics", "Microsoft Excel", "Data Compliance", "Key Performance Indicators (KPIs)", "Service Level Agreement (SLAs)", "Data Flow Architectures", "Data Transformation", "Data Collection", "Data Storage Strategies", "Agile Transformation", "ISO27001 Compliance", "Data Security and Compliance", "Project Management"], "Raghu": ["Mechanical Product Design", "Mechanical Component Design", "System Integration", "Sheet Metal Design", "Machined Parts Design", "Design Standardization", "Component Localization", "Cost Optimization", "Design Calculations", "Cross-functional Collaboration", "Onshore Rigging Calculations", "Service Lifting Tool Design", "Configuration Management", "Process Management", "UG-NX", "SolidWorks", "CATIA", "AutoCAD", "ANSYS", "Design FMEA", "DFM", "DFA", "GD&T", "Stack Up Analysis", "ASME Y14.5", "2D Drawing Review", "MathCAD", "CE Marking", "DNVGL", "EN-13155", "Machinery Directive 2006/42/EC", "EN ISO 50308", "EN ISO 14122", "Reverse Engineering", "Mechanical Design", "Service Lifting Tools Design", "2D Drawings Review", "Product Validation", "Microsoft Office"], "Karnati": ["Informatica PowerCenter", "Informatica Cloud Services (IICS)", "Intelligent Data Management Cloud (IDMC)", "DB2", "Oracle", "Netezza", "Terada<PERSON>", "Snowflake", "Hive", "Unix", "Windows", "Python", "Databricks", "Spark", "SQL", "Shell Scripting", "Data Warehousing", "ETL", "Data Integration", "Data Profiling", "Data Quality", "Business 360 Console", "Cloud Data Governance", "Cloud Data Catalog", "Star Schema", "Snowf<PERSON>a", "Data Marts", "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)", "Data Capture (CDC)", "JSON", "API", "ICS", "ICRT", "<PERSON><PERSON>", "AWS", "Data Modeling", "Technical Design Documentation", "Technical Architecture Documentation", "Data Migration", "Production Support", "Code Review", "Unix Shell Scripting", "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)", "Technical Design", "Technical Architecture", "Big Data", "PySpark", "Real-time Data Integration", "API Development (ICS, ICRT)", "Production Support (L3)", "Test Plan Development", "Test Script Development"], "Shashindra": ["React", "ReactJS", "Redux Toolkit", "A<PERSON>os", "SWR", "<PERSON><PERSON>", "React Router", "HTML5", "CSS3", "TypeScript", "JavaScript", "ES6", "j<PERSON><PERSON><PERSON>", "Material UI", "Bootstrap", "Tailwind CSS", "NodeJS", "PHP", "MySQL", "Amazon S3", "Amazon EC2", "Amazon Lambda", "Azure", "SOAP", "REST", "JSON", "ServiceNow", "Gulp", "<PERSON><PERSON><PERSON>", "Webpack", "SVN", "GitHub", "GitHub Copilot", "JWT", "RBAC", "Agile", "SCRUM", "Software Development Life Cycle (SDLC)", "Amazon Web Services (S3, EC2, Lambda)", "Silverlight", "Node.js"], "Upendra": ["Java", "J2EE", "Spring Boot", "Struts 2", "Spring IOC", "Spring MVC", "Spring Data", "Spring REST", "Jersey REST", "JSF", "Apache POI", "iText", "Servlets", "JSP", "JDBC", "JAX-WS", "JAX-RS", "Java Mail", "JMS", "JUnits", "ANT", "<PERSON><PERSON>", "IBM MQ", "Apache Kafka", "Amazon S3", "Amazon EKS", "Amazon EC2", "Angular", "Node.js", "<PERSON><PERSON><PERSON>ber", "Cypress", "JavaScript", "AJAX", "<PERSON><PERSON>", "HTML", "CSS", "SVN", "Bitbucket", "Git", "MongoDB", "SQL", "Quartz", "Hibernate", "Spring JPA", "Tomcat", "<PERSON><PERSON><PERSON>", "WebLogic", "WebSphere", "Putty", "WinSCP", "Bamboo", "<PERSON>", "RDBMS", "AWS Aurora Postgres", "JUnit", "Dojo"], "Chandra_Resume": ["Java", "JavaScript", "Python", "SQL", "Servlets", "JSP", "EJB", "JDBC", "JSTL", "JMS", "SOAP", "REST", "JPA", "AJAX", "<PERSON><PERSON><PERSON>", "Spring Framework", "Angular", "NestJS", "Node.js", "Cypress", "Tomcat", "WebLogic 11g", "<PERSON><PERSON><PERSON>", "GlassFish", "Resin", "Oracle 11g/12c", "MySQL", "PostgreSQL", "IBM DB2", "DynamoDB", "MongoDB", "Eclipse", "NetBeans", "JDeveloper", "IntelliJ", "MyEclipse", "VS Code", "Toad", "<PERSON> Data Modeler", "Visio", "UML", "CVS", "SVN", "Git", "SoapUI", "JMS Hermes", "JUnit", "Log4j", "ANT", "<PERSON><PERSON>", "JRockit Mission Control", "JMeter", "JR<PERSON>el", "Agile", "Waterfall", "<PERSON><PERSON><PERSON>", "Prototype", "Amazon Web Services (AWS)", "Google Cloud Platform (GCP)", "ITIL Foundation 2011", "AWS Certified Solutions Architect Associate", "AWS Certified Developer Associate", "AWS Certified SysOps Administrator Associate", "TypeScript", "Dynatrace", "GitLab", "LDAP", "SiteMinder", "SAML", "<PERSON>", "Harvest", "Bitbucket", "Nx Monorepo", "OOAD", "SOA", "Single Page Application (SPA)", "AWS CDK", "@task", "<PERSON><PERSON>", "TFS", "CI/CD", "GitLab Pipelines", "ITIL Foundation", "AWS Certified Solutions Architect - Associate", "AWS Certified Developer - Associate", "AWS Certified SysOps Administrator - Associate", "J2EE", "IntelliJ IDEA", "Spiral Model", "Prototype Model", "Spring"], "KRISHNA_KANT_NIRALA_Oracle_DBA": ["Oracle DBA", "Oracle OCI", "Oracle 19c", "Oracle 12c", "Oracle 11g", "Oracle 10g", "Oracle 21c", "Oracle RAC", "Oracle Data Guard", "Oracle Enterprise Manager", "Oracle TDE", "Data Pump", "Oracle Cloud Infrastructure", "RMAN", "SQL", "PL/SQL", "Linux Shell Scripting", "Crontab", "AWR", "ADDM", "EXPLAIN PLAN", "SQL*Trace", "TKPROF", "STATSPACK", "WebLogic 14c", "WebLogic 12c", "Tomcat", "GlassFish", "JDK", "SQL Server 2016", "V<PERSON>am Backup and Recovery", "Red Hat Linux 7", "Red Hat Linux 8", "<PERSON>adata", "IBM LTO 9", "IBM LTO 8", "OCI IAM", "OCI VCN", "OCI Object Storage", "OCI Load Balancing", "OCI Auto Scaling", "OCI CDN", "OCI WAF", "Autonomous Data Warehouse (ADW)", "Autonomous Transaction Processing (ATP)", "ITIL V3 Foundation", "Prince2", "Oracle Database Administrator Certified", "OCA - Oracle Database Administrator Certified", "Oracle 19c Database Administrator Training", "Teradata Certified Administrator (V2R5)", "OCI-Oracle Cloud Infrastructure Foundations Associate certified", "Oracle 19c Data Guard", "Oracle 19c RAC", "Red Hat Linux Enterprise 8", "Red Hat Linux Enterprise 7", "Oracle Enterprise Manager 12c", "Oracle Enterprise Manager Grid Control 11g", "Oracle Export/Import", "Transportable Tablespaces", "SQLTrace", "Oracle Real Application Cluster", "Windows Server 2016", "Amazon Web Services (AWS)", "Microsoft Azure", "High Availability", "Fault Tolerance", "Scalability", "Virtualization", "Oracle Autonomous Data Warehouse (ADW)", "Oracle Autonomous Transaction Processing (ATP)", "IAM", "VCN", "Object Storage", "<PERSON><PERSON>", "Auto Scaling", "CDN", "WAF", "Exadata X9M-2", "HP", "IBM Power E980", "IBM Power E850", "OCI-Oracle Cloud Infrastructure Foundations Associate", "OCA-Oracle Database Administrator Certified", "Oracle 19c Database Administrator", "ISO/IEC 27001", "ISO 20000", "ISO 27000", "Oracle Database Administration", "Oracle Cloud Infrastructure (OCI)", "Oracle GoldenGate (implied)", "Java JDK", "Terada<PERSON>", "Oracle Grid Control 11g", "SQL*Plus", "OCI AutoScaling", "HP/IBM Power E980", "HP/IBM Power E850", "Exadata CS DBX7"], "Sudhakara Rao Illuri-Fusion Financial Cloud": ["Oracle Fusion Applications", "Oracle E-Business Suite R12", "Oracle Cloud Financials", "Oracle Cloud General Ledger", "Oracle Cloud Accounts Payable", "Oracle Cloud Accounts Receivable", "Oracle Cloud Fixed Assets", "Oracle Cloud Cash Management", "Oracle Cloud I-Expenses", "Oracle Cloud Budgetary Control", "Oracle Financial Accounting Hub", "Oracle Transactional Business Intelligence (OTBI)", "Financial Reporting Studio (FRS)", "Smart View", "SQL", "Toad", "Data Loader", "Hyperion FRS", "Business Process Management (BPM)", "AIM Methodology", "OUM Methodology", "Sub Ledger Accounting (SLA)", "Windows 2007/2008/2010", "Unix", "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional", "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials", "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials", "1Z0-517 - Oracle EBS R12.1 Payables Essentials", "BIP", "Oracle Fusion Financials", "Oracle Financials Cloud General Ledger", "Oracle Financials Cloud Accounts Payable", "Accounts Payable", "Accounts Receivable", "General <PERSON><PERSON>", "Fixed Assets", "Cash Management", "I-Expenses", "I-Receivables", "Order Management", "OTBI", "FRS", "SmartView", "Procure to Pay (P2P)", "Order to Cash (O2C)", "Record to Report (R2R)", "Oracle Allocations", "Oracle Cloud", "Intercompany", "Oracle Cloud I-Receivables"], "Akhila D": ["Pega Rules Process Engine", "Pega Group Benefits Insurance Framework", "Pega Product Builder", "Pega 7.2.2", "Pega 7.3", "Pega 7.4", "Pega 8", "CSS", "Java", "JavaScript", "REST", "SOAP", "Agile Methodology", "SCRUM", "Unit testing", "PostgreSQL", "MS SQL Server", "<PERSON><PERSON><PERSON>", "Sections", "Flow Actions", "List-View", "Summary-View Reports", "Report Definitions", "Clipboard", "Tracer", "PLA", "Product locking", "Package locking", "Ruleset locking", "Waterfall", "SDLC", "E-Commerce", "Insurance", "Agents", "Queue Processors", "Decision Rules", "Declarative Rules", "Application Design", "Case Management", "Data Modeling", "Process Flows", "Screen Flows", "Data Transforms", "Activities", "Rule Resolution", "Enterprise Class Structure", "Dev Studio", "App Studio", "Admin Studio", "CDH", "Code Review", "Document review", "WebSphere", "XML", "Pega Marketing Consultant", "Senior System Architect", "System Architect", "Postman", "HTML", "Pega Marketing Consultant (Certification)", "Senior System Architect (Certification)", "System Architect (Certification)"], "pavani_resume": ["Selenium IDE", "Selenium RC", "Selenium WebDriver", "Selenium Grid", "TestNG", "<PERSON>", "QTP", "<PERSON><PERSON><PERSON>", "HTML", "JavaScript", "Python", "Java", "SQL", "<PERSON>", "Oracle", "SQL Server", "MS Access", "Toad", "<PERSON><PERSON>", "Tortoise SVN", "HP Quality Center", "<PERSON><PERSON>", "SoapUI", "Agile", "Waterfall", "TortoiseSVN", "SharePoint", "Microsoft Office", "Continuous Integration"], "Saisree Kondamareddy_ QA Consultant (1)": ["Java", "Selenium WebDriver", "SeeTest (Experitest)", "ACCELQ", "TestNG", "JUnit", "JBehave", "<PERSON><PERSON>", "Git", "GitHub", "<PERSON><PERSON>", "Azure DevOps", "HP ALM", "PostgreSQL", "BrowserStack", "LambdaTest", "Agile", "Waterfall", "Functional Testing", "Smoke Testing", "System Testing", "Integration Testing", "Regression Testing", "User Acceptance Testing (UAT)", "UI Testing", "Mobile Testing", "Automation Testing", "Web Testing", "Compatibility Testing", "Sanity Testing", "Ad hoc Testing", "Test Case Design", "Test Plan Creation", "Test Scripting", "Test Execution", "Defect Tracking", "Bug Reporting", "Test Management", "Production Support", "AI-powered Automation", "Mobile Application Automation", "Web Application Automation", "IOS Testing", "Android Testing", "Windows", "SeeTest/Experitest", "Test Case Development", "Test Schedule Creation", "Unit testing", "Test Case Execution", "Mobile Web Testing", "SDLC", "Performance Testing", "Ad-hoc Testing", "Test Planning", "Defect Management"], "Sowmya": ["SQL", "Power BI", "SSIS", "SSAS", "SSRS", "T-SQL", "PL/SQL", "Power BI Desktop", "Power BI Service", "Power Query", "DAX", "M Language", "Data Warehousing", "ETL", "Dimensional Modeling", "Star Schema", "Snowf<PERSON>a", "Microsoft BI Stack", "SQL Server", "Oracle", "<PERSON><PERSON>", "MySQL", "Python", "Power Pivot", "Data Gateway", "Row-Level Security (RLS)", "Data Flows", "DataMart", "Talend", "Azure Blob Storage", "Power Automate", "Visual Studio Code", "HTML", "Custom Visuals (Power BI)", "Drill Down", "Drill Through", "Parameters", "Cascading Filters", "Interactive Dashboards", "Reports", "Excel", "SSMS", "SQL Server Data Tools", "Speedometer Charts", "Sankey Diagrams", "Pareto Charts", "Waterfall Charts"], "Varshika": ["SAP FI", "SAP CO", "SAP SD", "SAP MM", "SAP Cash Management (CM)", "ASAP Methodology", "Agile Methodology", "HFM", "FDMEE", "PCBS", "WRICEF Documentation", "Business Process Mapping", "FIT-GAP Analysis", "Financial Reporting", "SAP Solution Design", "SAP Warehouse Management", "Material Master Data Management", "Procurement Processes", "Order-to-Delivery Process", "Demand Forecasting", "Cash Pooling", "Bank Reconciliation", "F110 Automatic Payment Program", "Real-time Cash Visibility System", "Inhouse Cash Management", "SAP Best Practices", "Generally Accepted Accounting Principles (GAAP)", "International Financial Reporting Standards (IFRS)", "Financial Analysis", "Automated Data Entry", "AR Processing & Reporting", "Customer Accounting", "Vendor/Customer Open Items", "SAP Integration", "SAP Financial Accounting (FI)", "SAP Controlling (CO)", "SAP Sales and Distribution (SD)", "SAP Materials Management (MM)", "Hyperion Financial Management (HFM)", "Financial Data Management (FDMEE)", "Profit Center Accounting (PCA)", "SAP Accounts Receivable (AR)", "SAP Accounts Payable (AP)", "<PERSON> (GL)", "Purchase Order (PO) Management", "Inventory Planning", "Automatic Payment Program (F110)", "SAP Profit Center Accounting (PCA)", "Automated Bank Reconciliation", "Pricing Strategies", "SAP MM Functionalities", "Business Process Optimization", "Agile Methodologies", "SAP Profit Center Accounting (PCBS)", "SAP AR Processing & Reporting", "Cash Discount Management", "Dispute and Deduction Management", "SAP FI-GL Transactions", "SAP AP/AR Transactions", "Vendor/Customer Open Item Management", "BPP Documentation", "Order-to-Delivery Process Optimization", "Certified SAP Functional Consultant", "SAP Material Master Data Management", "SAP Procurement Processes", "GAAP (Generally Accepted Accounting Principles)", "IFRS (International Financial Reporting Standards)", "SAP Treasury Modules"], "Uday": ["SAP S/4HANA", "ABAP", "OData", "SAP UI5", "<PERSON><PERSON>", "Fiori Elements", "PI/PO", "AIF", "BRF+", "Business Workflow", "CRM", "Web Dynpro ABAP", "RAP", "BTP", "CAPM", "Procure to Pay (PTP)", "Order to Cash Management (OTC)", "Production Planning (PP)", "Quality Management (QM)", "FI-AP", "FI-AR", "FI-GL (FICO)", "RTR", "SCM", "Product Life Cycle Management (PLM)", "Advanced Planner Optimizer (APO)", "Extended Warehouse Management (EWM)", "JavaScript", "XML", "HTML5", "JSON", "Data Dictionary (DDIC)", "Module Pool Programming", "Object-Oriented ABAP (OOABAP)", "RFCs", "BADIs", "BDC", "BAPI", "BP Integrations", "Enhancement Points", "User Exits", "Customer Exits", "ALE IDOCs", "Inbound/Outbound Proxy", "SAP NetWeaver Gateway", "Service Registration", "Service Extension", "CDS Views", "AMDP", "SAP Fiori List Report Application", "Web IDE", "BSP", "SAP Fiori Launchpad", "SAP UI5 Framework", "GitHub", "Business Objects (BO)", "<PERSON><PERSON>", "ATC", "SPDD", "SPAU", "SAP Security", "PFTC", "SAP Certified Development Specialist - ABAP for SAP HANA 2.0", "SAP Certified Development Associate - SAP Fiori Application Developer", "RICEF", "SAP Script", "Smart Forms", "Adobe Forms", "ALV Reports", "PFTC Roles", "SAP ECC", "Agile", "Waterfall", "SAP Scripts"], "Updated_CV_-_Tauqeer_Ahmad1_1": ["TypeScript", "React", "Next.js", "Angular", "Node.js", "NestJS", "REST APIs", "GraphQL APIs", "AWS SAM", "AWS CDK", "CI/CD", "Apache ECharts", "Cognito", "OKTA", "OIDC", "Mantine UI", "Vite", "MySQL Aurora", "AWS Lambda", "Serverless Architecture", "AWS API Gateway", "Microservices", "Styled Components", "Sanity", "Amplify", "ShadCN UI", "Salesforce", "CDL", "API Gateway", "Amazon Web Services", "MySQL", "Microservices Architecture"], "Ajeesh_resume": ["Cisco Catalyst 9800 Wireless Controller", "Talwar controller", "AireOS controller", "Cisco Access Points", "Talwar Simulator", "WiFi", "802.11", "WLAN", "Ethernet", "IP", "TCP", "UDP", "CAPWAP", "NETCONF", "YANG", "Swift", "ClearCase", "SVN", "Git", "Cisco catalyst 3750 Switch", "ios-xe asr 1K router", "C", "C++", "OpenWRT", "Linux", "QMI", "AT interfaces", "Ubus", "Shell Scripting", "Qualcomm SDX hardware", "AT&T Echo controller", "POLARIS", "XML", "GDB", "Gre", "RFID", "AeroScout tags", "Cisco Aironet outdoor mesh access points", "Cisco Prime Infrastructure", "Mac filtering", "Mac filter configuration", "Device Drivers", "LED Manager", "Mesh Networking", "Cisco Aironet", "Unit testing", "Integration Testing", "LTE", "5G"], "Maanvi Resume (3)": ["Python", "Java", "<PERSON><PERSON>", "PowerShell", "C", "C++", "Android App Development", "Spring Boot", "Flask", "Django", "Terraform", "<PERSON><PERSON>", "Node.js", "JUnit", "HTML", "CSS", "Apache Kafka", "JSON", "j<PERSON><PERSON><PERSON>", "Bootstrap", "GraphQL", "MySQL", "Kubernetes", "Redis", "Amazon Web Services", "Azure", "<PERSON>er", "Linux", "macOS", "Kali Linux", "Windows", "SQL Server", ".NET Core", "OAuth", "Azure DevOps", "AWS Certified Solutions Architect - Associate", "Amazon Web Services (AWS)", "Project Management", "Network Security", "Machine Learning", "Data Structures", "Object Oriented Programming", "Operating Systems", "Design and Analysis of Algorithms", "DBMS"], "Vidwaan_vidwan_resume": ["Java", "Python", "<PERSON>", "TypeScript", "JavaScript", "HTML", "CSS", "SQL", "Spring Boot", "Spring MVC", "REST APIs", "Microservices", "ReactJS", "MySQL", "PostgreSQL", "DynamoDB", "Amazon S3", "Amazon SQS", "Amazon SNS", "Amazon EC2", "Amazon Lambda", "Amazon CloudWatch", "Amazon Athena", "Amazon Glue", "Amazon Firehose", "AWS CDK", "AWS Step Functions", "Kubernetes", "<PERSON>er", "<PERSON>", "Git", "Agile", "Design Patterns", "Data Structures", "Machine Learning", "Postman", "Test Driven Development (TDD)", "JUnit", "<PERSON><PERSON><PERSON>", "Spark SQL", "Server-Side Encryption", "IAM Role Management", "EKS", "BottleRocket", "Amazon Web Services (AWS)", "Lambda", "EC2", "SQS", "S3", "SNS", "CI/CD", "CloudWatch", "Step Functions", "AWS Glue", "Athena", "JDK 8", "JDK 17", "AWS Athena", "IAM", "Firehose"], "Soham_Resume_Java": ["Java", "Spring Boot", "Hibernate", "JUnit", "<PERSON><PERSON><PERSON>", "Python", "JavaScript", "TypeScript", "SQL", "React.js", "Angular", "HTML5", "CSS3", "j<PERSON><PERSON><PERSON>", "Bootstrap", "MySQL", "Firebase Cloud Services", "MongoDB", "<PERSON>", "Apache Kafka", "Azure", "Google Cloud Platform (GCP)", "<PERSON><PERSON>", "Git", "<PERSON>er", "<PERSON>", "CI/CD", "SOAP", "Microservices Architecture", "REST APIs", "<PERSON><PERSON>", "Power BI", "Android Studio", "JSON", "Bluetooth", "Java Development", "Advanced Java Development", "Salesforce Platform Administrator", "Salesforce Platform Developer", "Java Development (Certification)", "Advanced Java Development (Certification)", "Salesforce Platform Administrator (Certification - In process)", "Salesforce Platform Developer (Certification - In process)"], "SivakumarDega_CV": ["Selenium WebDriver", "Java", "<PERSON><PERSON><PERSON>ber", "<PERSON><PERSON>", "TestNG", "Appium", "Perfecto", "SeeTest", "REST Assured", "Karate Framework", "UFT", "LeanFT", "<PERSON>", "GitLab", "Bitbucket", "Azure DevOps", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HP ALM", "Confluence", "Quality Center", "Swagger", "SOAP", "Postman", "Informatica 10.2", "MicroStrategy", "Crystal Reports", "CICS", "JCL", "VSAM", "Cobol", "Sufi", "DB2", "File-Aid", "CA DevTest", "ATOM", "Azure", "AWS", "GCP", "Microsoft Azure", "OAuth", "SSO", "Agile", "Test Plan Creation", "Test Strategy", "Test Design", "Test Execution", "Test effort estimation", "Requirements mapping", "Risk-based testing", "End-to-End testing", "User Acceptance testing", "Functional Testing", "Regression Testing", "Integration Testing", "System Testing", "UI Testing", "Database testing", "API testing", "Web services testing", "Microservices testing", "Mobile Testing", "Browser compatibility testing", "Exploratory testing", "ETL testing", "Data Warehouse testing", "Business Intelligence", "Interactive Voice Response (IVR) testing", "Customer Telephony Integration (CTI) testing", "Mainframes testing", "Service Virtualization", "Continuous Integration and Continuous Deployment (CI/CD)", "JUnit", "CI/CD", "Test Management", "Mainframe Testing", "Performance Testing", "User Interface Testing", "Automation Testing", "Manual Testing", "Mobile Web Testing", "Desktop Application Testing", "Web Application Testing", "IOS Testing", "Android Testing", "C", "IVR Testing", "CTI Testing", "Continuous Integration", "Continuous Deployment", "ATOM (Mainframes/AS400 automation tool)", "Test Automation", "Test Strategy Creation", "Test Coverage", "Requirements Prioritization", "ETL", "Test Estimation"]}, "consultants_by_skill": {"C#": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "Chary"], ".NET 6": ["Laxman_Gite"], "ASP.NET Core": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "ASP.NET MVC": ["Laxman_Gite", "PunniyaKodi V updated resume", "Chary"], "Angular": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "PunniyaKodi V updated resume", "Chary"], "Web API": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET"], "Azure Developer": ["Laxman_Gite"], "Azure Logic Apps": ["Laxman_Gite", "Chary"], "Azure Service Bus": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure API Management": ["Laxman_Gite"], "Azure Storage": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Cosmos DB": ["Laxman_Gite", "<PERSON><PERSON>_DotNET"], "Redis Cache": ["Laxman_Gite"], "Azure Active Directory (Azure AD)": ["Laxman_Gite"], "Azure Virtual Network": ["Laxman_Gite"], "Azure Application Insights": ["Laxman_Gite"], "Azure Log Analytics": ["Laxman_Gite"], "Azure Key Vault": ["Laxman_Gite"], "Azure Monitor": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead"], "Azure Container Registry": ["Laxman_Gite"], "Azure Service Fabric": ["Laxman_Gite"], "Azure Data Lake": ["Laxman_Gite", "Chary"], "YAML Pipelines": ["Laxman_Gite"], "Docker": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Kubernetes": ["Laxman_Gite", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CI/CD": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Soham_Resume_Java", "SivakumarDega_CV", "Vidwaan_vidwan_resume"], "Microservices": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "Serverless Architecture": ["Laxman_Gite", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "HTML": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "CSS": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume"], "jQuery": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "Event Grid": ["Laxman_Gite"], "Event Hub": ["Laxman_Gite"], "SQL Server": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Chary", "<PERSON>"], "MySQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Snowflake": ["Laxman_Gite", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "T-SQL": ["Laxman_Gite", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "PL/SQL": ["Laxman_Gite", "PunniyaKodi V updated resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON><PERSON>"], "Stored Procedures": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON>"], "Triggers": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Functions (Database)": ["Laxman_Gite"], "Amazon Web Services (AWS)": ["Laxman_Gite", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "Microsoft Azure": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Agile Methodology": ["Laxman_Gite", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Design Patterns": ["Laxman_Gite", "<PERSON><PERSON><PERSON>il .Net lead", "Chary", "Vidwaan_vidwan_resume"], "Microservices Architecture": ["Laxman_Gite", "Soham_Resume_Java", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Federated Database Design": ["Laxman_Gite"], "Container-based Architecture": ["Laxman_Gite"], "High-Throughput System Architecture": ["Laxman_Gite"], "Real-time Data Analytics Solution Architecture": ["Laxman_Gite"], "E-commerce Architecture": ["Laxman_Gite"], "Hybrid Solution Architecture": ["Laxman_Gite"], "VPC Design": ["Laxman_Gite"], "Direct Connect": ["Laxman_Gite"], "VPN": ["Laxman_Gite"], "Query Performance Optimization": ["Laxman_Gite"], "Data Modeling": ["Laxman_Gite", "Chary", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Microsoft Certified Professional": ["Laxman_Gite"], "WPF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVC": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "MS Azure": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "WCF": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Blob Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Table Storage": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "App Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Redis": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON> (3)"], "App Insights": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Azure APIM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Logic Apps": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "Laxman_Gite"], "AZ900: Microsoft Azure Fundamentals": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Caliburn.Micro": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Prism": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Entity Framework 7.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "XML Parser": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "LINQ": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "Stimulsoft": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Angular Reactive Forms": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "HttpClient": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "NUnit": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead"], "Coded UI Testing": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ADO.NET": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "SQL Server Reporting Services (SSRS)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON>_DotNET"], "Strapi CMS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Windows Services": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume"], "WCF RESTful": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SQL Server 2019": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "PostgreSQL": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume"], "SQLite": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Oracle (PL/SQL)": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS Access": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "pavani_resume"], "InstallShield": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "GitHub": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "TFS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "PunniyaKodi V updated resume", "Chandra_Resume"], "SVN": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer", "<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "A<PERSON><PERSON>_resume"], "IIS": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Apache Tomcat": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "DevExpress": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Brainbench C# 5.0": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MVVM": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "ASP.NET": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON>_DotNET"], ".NET Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary"], "Entity Framework": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET"], "EF Core": ["<PERSON><PERSON><PERSON>il .Net lead"], "Razor View Engine": ["<PERSON><PERSON><PERSON>il .Net lead"], "Bootstrap": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "CosmosDB": ["<PERSON><PERSON><PERSON>il .Net lead"], "ElasticSearch": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "JavaScript": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "Uday", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Apache Kafka": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "ActiveMQ": ["<PERSON><PERSON><PERSON>il .Net lead"], "Pivotal Cloud Foundry": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>"], "Azure App Service": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET"], "Node.js": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "<PERSON><PERSON><PERSON> (3)", "PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "React": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "PunniyaKodi V updated resume"], "OAuth2": ["<PERSON><PERSON><PERSON>il .Net lead"], "Swagger": ["<PERSON><PERSON><PERSON>il .Net lead", "SivakumarDega_CV"], "OOPS": ["<PERSON><PERSON><PERSON>il .Net lead"], "SOLID principles": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary"], "Team Foundation Server (TFS)": ["<PERSON><PERSON><PERSON>il .Net lead"], "Git": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "<PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Jira": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>_DotNET"], "Azure DevOps": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Moq": ["<PERSON><PERSON><PERSON>il .Net lead"], "Agile Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead", "<PERSON><PERSON><PERSON><PERSON>"], "SCRUM": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume", "Puneet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chary"], "Waterfall Methodologies": ["<PERSON><PERSON><PERSON>il .Net lead"], "Test-Driven Development (TDD)": ["<PERSON><PERSON><PERSON>il .Net lead", "PunniyaKodi V updated resume"], "C++": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)"], "Python": ["<PERSON><PERSON><PERSON>il .Net lead", "Chary", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Service Bus": ["Laxman_Gite"], "API Management": ["Laxman_Gite"], "YAML Pipeline": ["Laxman_Gite"], "Azure AD": ["Laxman_Gite"], "Virtual Network": ["Laxman_Gite"], "Application Insights": ["Laxman_Gite"], "Log Analytics": ["Laxman_Gite"], "Key Vault": ["Laxman_Gite"], "Functions": ["Laxman_Gite", "<PERSON><PERSON><PERSON>"], "Software Architecture": ["Laxman_Gite"], "Micro-services": ["Laxman_Gite"], "High Throughput System Architecture": ["Laxman_Gite"], "Microsoft Azure Certified Professional": ["Laxman_Gite"], "MCA": ["Laxman_Gite"], "Open API": ["<PERSON><PERSON><PERSON>il .Net lead"], "C": ["<PERSON><PERSON><PERSON>il .Net lead", "A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], ".NET Core": ["PunniyaKodi V updated resume", "<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON> (3)"], "AJAX": ["PunniyaKodi V updated resume", "<PERSON><PERSON>", "Chandra_Resume"], "AngularJS": ["PunniyaKodi V updated resume"], "ReactJS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "SQL": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "XML": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "A<PERSON><PERSON>_resume"], "HTML5": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>", "Uday", "Soham_Resume_Java"], "Sass": ["PunniyaKodi V updated resume"], "TypeScript": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "JSON": ["PunniyaKodi V updated resume", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Uday", "<PERSON><PERSON><PERSON> (3)", "Soham_Resume_Java"], "DynamoDB": ["PunniyaKodi V updated resume", "Chandra_Resume", "Vidwaan_vidwan_resume"], "OpenSearch": ["PunniyaKodi V updated resume"], "EC2": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "CloudFront": ["PunniyaKodi V updated resume"], "IAM": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA", "Vidwaan_vidwan_resume"], "ECS": ["PunniyaKodi V updated resume"], "SQS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "SNS": ["PunniyaKodi V updated resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Lambda": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "API Gateway": ["PunniyaKodi V updated resume", "Chary", "<PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "RDS": ["PunniyaKodi V updated resume"], "CloudWatch": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON>", "Vidwaan_vidwan_resume"], "Step Functions": ["PunniyaKodi V updated resume", "Vidwaan_vidwan_resume"], "Elastic Cache": ["PunniyaKodi V updated resume"], "NodeJS": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>"], "AGGrid": ["PunniyaKodi V updated resume"], "txText Control": ["PunniyaKodi V updated resume"], "ASPX": ["PunniyaKodi V updated resume"], "SOAP": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "Soham_Resume_Java", "SivakumarDega_CV"], "RESTful APIs": ["PunniyaKodi V updated resume"], "Crystal Reports": ["PunniyaKodi V updated resume", "SivakumarDega_CV"], "Active Reports": ["PunniyaKodi V updated resume"], "SSRS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "SSIS": ["PunniyaKodi V updated resume", "Chary", "<PERSON><PERSON><PERSON><PERSON>"], "YAML": ["PunniyaKodi V updated resume"], "Terraform": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "DDD": ["PunniyaKodi V updated resume"], "TDD": ["PunniyaKodi V updated resume"], "Agile": ["PunniyaKodi V updated resume", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Vidwaan_vidwan_resume", "SivakumarDega_CV", "Chary", "Uday"], "NuGet": ["PunniyaKodi V updated resume"], "Object-Oriented Programming (OOP)": ["PunniyaKodi V updated resume", "Chary"], "VB.NET": ["PunniyaKodi V updated resume"], "Domain Driven Design": ["PunniyaKodi V updated resume"], "Test Driven Development": ["PunniyaKodi V updated resume"], "Elastic APM": ["PunniyaKodi V updated resume"], "OpenTelemetry": ["PunniyaKodi V updated resume"], "FullStory": ["PunniyaKodi V updated resume"], "Google Analytics": ["PunniyaKodi V updated resume", "Chary"], "ASP.NET Core 6.0": ["Chary"], "ASP.NET Core 8.0": ["Chary"], ".NET MAUI": ["Chary"], "XAML": ["Chary"], "C# 8.0": ["Chary"], "C# 9.0": ["Chary"], "C# 10.0": ["Chary"], "Java": ["Chary", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "Web Services": ["Chary"], "REST": ["Chary", "<PERSON><PERSON><PERSON>", "Chandra_Resume", "<PERSON><PERSON><PERSON>"], "Angular 7": ["Chary"], "Angular 8": ["Chary"], "Angular 9": ["Chary"], "Angular 10": ["Chary"], "Angular 12": ["Chary"], "Material Design": ["Chary"], ".NET Framework 2.0": ["Chary"], ".NET Framework 3.5": ["Chary"], ".NET Framework 4.0": ["Chary"], ".NET Framework 4.5": ["Chary"], ".NET Framework 4.7": ["Chary", "PunniyaKodi V updated resume"], "CI/CD Pipeline": ["Chary"], "Splunk": ["Chary", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "RabbitMQ": ["Chary"], "Amazon DynamoDB": ["Chary", "<PERSON><PERSON><PERSON>"], "Kendo UI": ["Chary", "<PERSON><PERSON>_DotNET"], "Amazon EC2": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lambda": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure App Services": ["Chary", "<PERSON><PERSON>_DotNET"], "WebJobs": ["Chary"], "Azure Active Directory": ["Chary"], "ServiceNow": ["Chary", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON>"], "HP Service Manager (HPSM)": ["Chary"], "Service-Oriented Architecture (SOA)": ["Chary"], "OAuth 2.0": ["Chary"], "OKTA": ["Chary", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Azure Entra ID": ["Chary"], "Bitbucket": ["Chary", "<PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Team Foundation Server": ["Chary"], "Subversion (SVN)": ["Chary", "<PERSON><PERSON><PERSON>"], "TortoiseSVN": ["Chary", "pavani_resume"], "Visual Studio 2003": ["Chary"], "Visual Studio 2005": ["Chary"], "Visual Studio 2008": ["Chary"], "Visual Studio 2010": ["Chary"], "Visual Studio 2012": ["Chary"], "Visual Studio 2013": ["Chary"], "Visual Studio 2015": ["Chary"], "Visual Studio 2017": ["Chary"], "Visual Studio 2019": ["Chary"], "Visual Studio 2022": ["Chary"], "Azure Cloud Architectures": ["Chary"], "Azure Storage Services": ["Chary"], "Azure SQL Database": ["Chary", "<PERSON><PERSON>_DotNET"], "OpenID Connect": ["Chary"], "Ping Identity": ["Chary"], "Salesforce APIs": ["Chary"], "CQRS": ["Chary"], "Saga Pattern": ["Chary"], "Choreography Pattern": ["Chary"], "Gateway Aggregation": ["Chary"], "Circuit Breaker Pattern": ["Chary"], "Message Queue": ["Chary"], "MuleSoft": ["Chary"], "Kafka": ["Chary", "<PERSON><PERSON>_DotNET", "<PERSON>"], "Tibco": ["Chary"], "AKS (Azure Kubernetes Service)": ["Chary", "<PERSON><PERSON>_DotNET"], "MVC Design Pattern": ["Chary"], "Repository Pattern": ["Chary"], "Dependency Inversion Principle": ["Chary"], "Dependency Injection": ["Chary"], "Factory Pattern": ["Chary"], "Abstract Factory Pattern": ["Chary"], "Tridion CMS 2009": ["Chary"], "Tridion CMS 2011": ["Chary"], "Tridion CMS 2013": ["Chary"], "Tridion CMS 8.5": ["Chary"], "Sitecore": ["Chary"], "SEO Optimization": ["Chary"], "Omniture": ["Chary"], "Google Tag Manager": ["Chary"], "SQL Server 2000": ["Chary"], "SQL Server 2005": ["Chary"], "SQL Server 2008": ["Chary"], "SQL Server 2012": ["Chary"], "SQL Server 2014": ["Chary"], "SQL Server 2017": ["Chary"], "Azure SQL Server": ["Chary"], "Oracle PL/SQL": ["Chary", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Selenium": ["Chary"], "Azure Data Factory": ["Chary"], "PMP (Project Management Professional)": ["Chary"], "Agile (SCRUM)": ["Chary"], "Kanban": ["Chary", "Puneet"], "AZ-104": ["Chary"], "AZ-204": ["Chary"], "AZ-304": ["Chary"], "Machine Learning": ["Chary", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Deep Learning": ["Chary"], "Predictive Analysis": ["Chary"], "Artificial Intelligence": ["Chary"], "IoT Systems": ["Chary"], ".NET": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "PunniyaKodi V updated resume"], "gRPC": ["<PERSON><PERSON>_DotNET"], "SSIS (SQL Server Integration Services)": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SSRS (SQL Server Reporting Services)": ["<PERSON><PERSON>_DotNET"], "LINQ to SQL": ["<PERSON><PERSON>_DotNET"], "LINQ to Objects": ["<PERSON><PERSON>_DotNET"], "Lambda Expressions": ["<PERSON><PERSON>_DotNET"], "S3 (Amazon S3)": ["<PERSON><PERSON>_DotNET"], "Amazon Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON>_DotNET"], "Amazon ECR (Elastic Container Registry)": ["<PERSON><PERSON>_DotNET"], "Elastic Beanstalk": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Application Load Balancer": ["<PERSON><PERSON>_DotNET"], "NoSQL": ["<PERSON><PERSON>_DotNET"], "Datadog": ["<PERSON><PERSON>_DotNET"], "Azure Container Registry (ACR)": ["<PERSON><PERSON>_DotNET"], "Azure Kubernetes Service (AKS)": ["<PERSON><PERSON>_DotNET", "Chary"], "Azure Blob Storage": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON><PERSON>"], "Blazor": ["<PERSON><PERSON>_DotNET"], "MudBlazor": ["<PERSON><PERSON>_DotNET"], "Telerik": ["<PERSON><PERSON>_DotNET"], "Redux": ["<PERSON><PERSON>_DotNET"], "Hangfire": ["<PERSON><PERSON>_DotNET"], "ADFS (Active Directory Federation Services)": ["<PERSON><PERSON>_DotNET"], "Tableau": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DB2": ["<PERSON><PERSON>_DotNET", "<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "SAP": ["<PERSON><PERSON>_DotNET", "Puneet"], "IDoc": ["<PERSON><PERSON>_DotNET"], "Logility": ["<PERSON><PERSON>_DotNET"], "Blue Yonder": ["<PERSON><PERSON>_DotNET"], "CloudFormation": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Jenkins": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "Puneet", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV"], "SonarQube": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Puneet"], "Antifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Elastic Kubernetes Service (EKS)": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "ANT": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume"], "Maven": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "Shell Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "A<PERSON><PERSON>_resume"], "Ansible": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>"], "PowerShell": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON> (3)"], "Tomcat": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "JBoss": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "Chandra_Resume"], "WebLogic": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "WebSphere": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Windows Server": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Red Hat Linux": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Unix": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "CentOS": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "VMware": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Elastic Load Balancers": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Waterfall": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "Chandra_Resume", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "Uday"], "Batch Scripting": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon ECS": ["<PERSON><PERSON><PERSON>"], "Amazon S3": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON>_DotNET"], "Amazon EBS": ["<PERSON><PERSON><PERSON>"], "Amazon VPC": ["<PERSON><PERSON><PERSON>"], "Amazon ELB": ["<PERSON><PERSON><PERSON>"], "Amazon SNS": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Amazon RDS": ["<PERSON><PERSON><PERSON>"], "Amazon IAM": ["<PERSON><PERSON><PERSON>"], "Amazon Route 53": ["<PERSON><PERSON><PERSON>"], "AWS CloudFormation": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "AWS Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Amazon CloudFront": ["<PERSON><PERSON><PERSON>"], "Amazon CloudWatch": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS CLI": ["<PERSON><PERSON><PERSON>"], "Vault": ["<PERSON><PERSON><PERSON>"], "Docker Hub": ["<PERSON><PERSON><PERSON>"], "Docker Registries": ["<PERSON><PERSON><PERSON>"], "AWS Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "Groovy": ["<PERSON><PERSON><PERSON>"], "GitLab": ["<PERSON><PERSON><PERSON>", "Chandra_Resume", "SivakumarDega_CV"], "Apache": ["<PERSON><PERSON><PERSON>"], "Grafana": ["<PERSON><PERSON><PERSON>"], "Pivotal Cloud Foundry (PCF)": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>il .Net lead"], "Infrastructure as Code (IaC)": ["<PERSON><PERSON><PERSON>"], "Configuration Management": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Containerization": ["<PERSON><PERSON><PERSON>"], "Orchestration": ["<PERSON><PERSON><PERSON>"], "Build/Release Management": ["<PERSON><PERSON><PERSON>"], "Source Code Management (SCM)": ["<PERSON><PERSON><PERSON>"], "HTTP (TLS)": ["<PERSON><PERSON><PERSON>"], "Key Management": ["<PERSON><PERSON><PERSON>"], "Encryption": ["<PERSON><PERSON><PERSON>"], "J2EE": ["Puneet", "<PERSON><PERSON>", "Chandra_Resume"], "SAFe": ["Puneet"], "Confluence": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Microsoft Project": ["Puneet"], "SmartSheet": ["Puneet"], "DevOps": ["Puneet"], "Warehouse Management": ["Puneet"], "CMMI Level 5": ["Puneet", "<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "PMP": ["Puneet", "Chary"], "PSM": ["Puneet"], "Agile Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Scrum Master": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Program Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Project Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "<PERSON><PERSON><PERSON> (3)", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Project Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Risk Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Cost Analysis": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Resource Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Stakeholder Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Delivery Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Client Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Release Management": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Microsoft Excel": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>"], "Azure Cloud": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "DA manager <PERSON><PERSON>", "Chary"], "Cobol": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1", "SivakumarDega_CV"], "Ezetrieves": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "IBM BMP": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "ISO 27001": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "DBT": ["<PERSON>"], "AWS": ["<PERSON>", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON><PERSON><PERSON>"], "Azure Data Factory (ADF)": ["<PERSON>"], "Databricks": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Database Migration Service": ["<PERSON>"], "AWS Glue": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Fivetran": ["<PERSON>"], "Snow SQL": ["<PERSON>"], "Streamset": ["<PERSON>"], "Snowpark": ["<PERSON>"], "Column Masking": ["<PERSON>"], "Data Encryption": ["<PERSON>"], "Data Decryption": ["<PERSON>"], "Data Masking": ["<PERSON>"], "Data Governance": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Hive": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Pig": ["<PERSON>"], "Sqoop": ["<PERSON>"], "PySpark": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Sigma": ["<PERSON>"], "Apache Airflow": ["<PERSON>"], "Informatica Power Center": ["<PERSON>"], "Talend": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Peoplesoft FSCM": ["<PERSON>"], "Peoplesoft HCM": ["<PERSON>"], "Oracle": ["<PERSON>", "<PERSON><PERSON><PERSON>", "pavani_resume", "<PERSON><PERSON><PERSON><PERSON>"], "MS SQL Server": ["<PERSON>", "<PERSON><PERSON><PERSON>", "PunniyaKodi V updated resume"], "OLTP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "OLAP": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Data Warehousing": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Data Architecture": ["<PERSON>"], "Data Integration": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "ELT": ["<PERSON>"], "ETL": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SivakumarDega_CV"], "Data Quality": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Real-time Data Ingestion": ["<PERSON>"], "Snow Pipe": ["<PERSON>"], "Confluent Kafka": ["<PERSON>"], "Snowsight": ["<PERSON>"], "SQR 6.0": ["<PERSON>"], "Avro": ["<PERSON>"], "Parquet": ["<PERSON>"], "CSV": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Index Design": ["<PERSON>"], "Query Plan Optimization": ["<PERSON>"], "Data Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>"], "Business Intelligence": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "SivakumarDega_CV", "<PERSON>", "DA manager <PERSON><PERSON>"], "Data Management": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ETL Processes": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Excel": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "DAX": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Statistical Analysis": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Regression": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Hypothesis Testing": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Predictive Modeling": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Time Series Forecasting": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Classification": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Cleaning": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Transformation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Data Automation": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "PivotTables": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Power Query": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON><PERSON><PERSON>"], "Pandas": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "NumPy": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "SQL Server Integration Services (SSIS)": ["<PERSON><PERSON><PERSON><PERSON> Data analyst", "<PERSON><PERSON>_DotNET"], "R": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Google Data Analytics Professional Certificate": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Getting Started with Power BI": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "The Complete Python Developer": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "ISTQB Certified Tester Foundation Level": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Informatica PowerCenter": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "IICS": ["<PERSON><PERSON><PERSON>"], "IDMC": ["<PERSON><PERSON><PERSON>"], "IBM Infosphere DataStage": ["<PERSON><PERSON><PERSON>"], "SAS Data Integration Studio": ["<PERSON><PERSON><PERSON>"], "Oracle 11g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 10g": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 9i": ["<PERSON><PERSON><PERSON>"], "Oracle 8x": ["<PERSON><PERSON><PERSON>"], "Microsoft SQL Server": ["<PERSON><PERSON><PERSON>"], "Amazon Redshift": ["<PERSON><PERSON><PERSON>"], "Data Migration": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Modernization": ["<PERSON><PERSON><PERSON>"], "Data Enrichment": ["<PERSON><PERSON><PERSON>"], "Data Validation": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "Data Processing": ["<PERSON><PERSON><PERSON>"], "Data Pipelining": ["<PERSON><PERSON><PERSON>"], "Data Visualization": ["<PERSON><PERSON><PERSON>", "DA manager <PERSON><PERSON>"], "Enterprise Reporting": ["<PERSON><PERSON><PERSON>"], "Dashboarding": ["<PERSON><PERSON><PERSON>"], "AWS Athena": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume"], "AWS Lake Formation": ["<PERSON><PERSON><PERSON>"], "Microsoft Power BI": ["<PERSON><PERSON><PERSON>"], "OBIEE": ["<PERSON><PERSON><PERSON>"], "SAS Visual Investigator": ["<PERSON><PERSON><PERSON>"], "SAS Visual Analytics": ["<PERSON><PERSON><PERSON>"], "Erwin Data Modeler": ["<PERSON><PERSON><PERSON>", "Chandra_Resume"], "Sparx Enterprise Architect": ["<PERSON><PERSON><PERSON>"], "RDBMS": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Star Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Snowflake Schema": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD)": ["<PERSON><PERSON><PERSON>"], "Normalization": ["<PERSON><PERSON><PERSON>"], "Flat Files": ["<PERSON><PERSON><PERSON>"], "Predictive Forecasting": ["<PERSON><PERSON><PERSON>"], "Alert Management": ["<PERSON><PERSON><PERSON>"], "Regulatory Reporting": ["<PERSON><PERSON><PERSON>"], "AML Compliance": ["<PERSON><PERSON><PERSON>"], "Data Intelligence": ["<PERSON><PERSON><PERSON>"], "Scenario Assessment": ["<PERSON><PERSON><PERSON>"], "MIS Management": ["<PERSON><PERSON><PERSON>"], "MS Excel": ["DA manager <PERSON><PERSON>"], "Data Security": ["DA manager <PERSON><PERSON>"], "Data Wrangling": ["DA manager <PERSON><PERSON>"], "Visual Studio": ["DA manager <PERSON><PERSON>", "Chary"], "Mechanical Product Design": ["<PERSON><PERSON><PERSON>"], "Mechanical Component Design": ["<PERSON><PERSON><PERSON>"], "System Integration": ["<PERSON><PERSON><PERSON>"], "Sheet Metal Design": ["<PERSON><PERSON><PERSON>"], "Machined Parts Design": ["<PERSON><PERSON><PERSON>"], "Design Standardization": ["<PERSON><PERSON><PERSON>"], "Component Localization": ["<PERSON><PERSON><PERSON>"], "Cost Optimization": ["<PERSON><PERSON><PERSON>"], "Design Calculations": ["<PERSON><PERSON><PERSON>"], "Cross-functional Collaboration": ["<PERSON><PERSON><PERSON>"], "Onshore Rigging Calculations": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tool Design": ["<PERSON><PERSON><PERSON>"], "Process Management": ["<PERSON><PERSON><PERSON>"], "UG-NX": ["<PERSON><PERSON><PERSON>"], "SolidWorks": ["<PERSON><PERSON><PERSON>"], "CATIA": ["<PERSON><PERSON><PERSON>"], "AutoCAD": ["<PERSON><PERSON><PERSON>"], "ANSYS": ["<PERSON><PERSON><PERSON>"], "Design FMEA": ["<PERSON><PERSON><PERSON>"], "DFM": ["<PERSON><PERSON><PERSON>"], "DFA": ["<PERSON><PERSON><PERSON>"], "GD&T": ["<PERSON><PERSON><PERSON>"], "Stack Up Analysis": ["<PERSON><PERSON><PERSON>"], "ASME Y14.5": ["<PERSON><PERSON><PERSON>"], "2D Drawing Review": ["<PERSON><PERSON><PERSON>"], "MathCAD": ["<PERSON><PERSON><PERSON>"], "CE Marking": ["<PERSON><PERSON><PERSON>"], "DNVGL": ["<PERSON><PERSON><PERSON>"], "EN-13155": ["<PERSON><PERSON><PERSON>"], "Machinery Directive 2006/42/EC": ["<PERSON><PERSON><PERSON>"], "EN ISO 50308": ["<PERSON><PERSON><PERSON>"], "EN ISO 14122": ["<PERSON><PERSON><PERSON>"], "Reverse Engineering": ["<PERSON><PERSON><PERSON>"], "Informatica Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Intelligent Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Netezza": ["<PERSON><PERSON><PERSON>"], "Teradata": ["<PERSON><PERSON><PERSON>", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)"], "Spark": ["<PERSON><PERSON><PERSON>"], "Data Profiling": ["<PERSON><PERSON><PERSON>"], "Business 360 Console": ["<PERSON><PERSON><PERSON>"], "Cloud Data Governance": ["<PERSON><PERSON><PERSON>"], "Cloud Data Catalog": ["<PERSON><PERSON><PERSON>"], "Data Marts": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, Type 2, Type 3, Type 4)": ["<PERSON><PERSON><PERSON>"], "Data Capture (CDC)": ["<PERSON><PERSON><PERSON>"], "API": ["<PERSON><PERSON><PERSON>"], "ICS": ["<PERSON><PERSON><PERSON>"], "ICRT": ["<PERSON><PERSON><PERSON>"], "Nifi": ["<PERSON><PERSON><PERSON>"], "Technical Design Documentation": ["<PERSON><PERSON><PERSON>"], "Technical Architecture Documentation": ["<PERSON><PERSON><PERSON>"], "Production Support": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Code Review": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Redux Toolkit": ["<PERSON><PERSON><PERSON>"], "Axios": ["<PERSON><PERSON><PERSON>"], "SWR": ["<PERSON><PERSON><PERSON>"], "Formik": ["<PERSON><PERSON><PERSON>"], "React Router": ["<PERSON><PERSON><PERSON>"], "CSS3": ["<PERSON><PERSON><PERSON>", "Soham_Resume_Java"], "ES6": ["<PERSON><PERSON><PERSON>"], "Material UI": ["<PERSON><PERSON><PERSON>"], "Tailwind CSS": ["<PERSON><PERSON><PERSON>"], "PHP": ["<PERSON><PERSON><PERSON>"], "Amazon Lambda": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Gulp": ["<PERSON><PERSON><PERSON>"], "Grunt": ["<PERSON><PERSON><PERSON>"], "Webpack": ["<PERSON><PERSON><PERSON>"], "GitHub Copilot": ["<PERSON><PERSON><PERSON>"], "JWT": ["<PERSON><PERSON><PERSON>"], "RBAC": ["<PERSON><PERSON><PERSON>"], "Software Development Life Cycle (SDLC)": ["<PERSON><PERSON><PERSON>"], "Spring Boot": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Struts 2": ["<PERSON><PERSON>"], "Spring IOC": ["<PERSON><PERSON>"], "Spring MVC": ["<PERSON><PERSON>", "Vidwaan_vidwan_resume"], "Spring Data": ["<PERSON><PERSON>"], "Spring REST": ["<PERSON><PERSON>"], "Jersey REST": ["<PERSON><PERSON>"], "JSF": ["<PERSON><PERSON>"], "Apache POI": ["<PERSON><PERSON>"], "iText": ["<PERSON><PERSON>"], "Servlets": ["<PERSON><PERSON>", "Chandra_Resume"], "JSP": ["<PERSON><PERSON>", "Chandra_Resume"], "JDBC": ["<PERSON><PERSON>", "Chandra_Resume"], "JAX-WS": ["<PERSON><PERSON>"], "JAX-RS": ["<PERSON><PERSON>"], "Java Mail": ["<PERSON><PERSON>"], "JMS": ["<PERSON><PERSON>", "Chandra_Resume"], "JUnits": ["<PERSON><PERSON>"], "IBM MQ": ["<PERSON><PERSON>"], "Amazon EKS": ["<PERSON><PERSON>"], "Cucumber": ["<PERSON><PERSON>", "SivakumarDega_CV"], "Cypress": ["<PERSON><PERSON>", "Chandra_Resume"], "Dojo Toolkit": ["<PERSON><PERSON>"], "MongoDB": ["<PERSON><PERSON>", "Chandra_Resume", "Soham_Resume_Java"], "Quartz": ["<PERSON><PERSON>"], "Hibernate": ["<PERSON><PERSON>", "Soham_Resume_Java"], "Spring JPA": ["<PERSON><PERSON>"], "Putty": ["<PERSON><PERSON>"], "WinSCP": ["<PERSON><PERSON>"], "Bamboo": ["<PERSON><PERSON>"], "AWS Aurora Postgres": ["<PERSON><PERSON>"], "EJB": ["Chandra_Resume"], "JSTL": ["Chandra_Resume"], "JPA": ["Chandra_Resume"], "Struts": ["Chandra_Resume"], "Spring Framework": ["Chandra_Resume"], "NestJS": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "WebLogic 11g": ["Chandra_Resume"], "GlassFish": ["Chandra_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Resin": ["Chandra_Resume"], "Oracle 11g/12c": ["Chandra_Resume"], "IBM DB2": ["Chandra_Resume"], "Eclipse": ["Chandra_Resume"], "NetBeans": ["Chandra_Resume"], "JDeveloper": ["Chandra_Resume"], "IntelliJ": ["Chandra_Resume"], "MyEclipse": ["Chandra_Resume"], "VS Code": ["Chandra_Resume"], "Toad": ["Chandra_Resume", "<PERSON><PERSON><PERSON>-Fusion Financial Cloud", "pavani_resume"], "Visio": ["Chandra_Resume"], "UML": ["Chandra_Resume"], "CVS": ["Chandra_Resume"], "SoapUI": ["Chandra_Resume", "pavani_resume"], "JMS Hermes": ["Chandra_Resume"], "JUnit": ["Chandra_Resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "<PERSON><PERSON><PERSON> (3)", "Vidwaan_vidwan_resume", "Soham_Resume_Java", "SivakumarDega_CV", "<PERSON><PERSON>"], "Log4j": ["Chandra_Resume"], "JRockit Mission Control": ["Chandra_Resume"], "JMeter": ["Chandra_Resume"], "JRebel": ["Chandra_Resume"], "Spiral": ["Chandra_Resume"], "Prototype": ["Chandra_Resume"], "Google Cloud Platform (GCP)": ["Chandra_Resume", "Soham_Resume_Java"], "ITIL Foundation 2011": ["Chandra_Resume"], "AWS Certified Solutions Architect Associate": ["Chandra_Resume"], "AWS Certified Developer Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator Associate": ["Chandra_Resume"], "Dynatrace": ["Chandra_Resume"], "LDAP": ["Chandra_Resume"], "SiteMinder": ["Chandra_Resume"], "SAML": ["Chandra_Resume"], "Harvest": ["Chandra_Resume"], "Nx Monorepo": ["Chandra_Resume"], "OOAD": ["Chandra_Resume"], "SOA": ["Chandra_Resume", "Chary"], "Single Page Application (SPA)": ["Chandra_Resume"], "AWS CDK": ["Chandra_Resume", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume"], "@task": ["Chandra_Resume"], "GitLab Pipelines": ["Chandra_Resume"], "Oracle DBA": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle OCI": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 21c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle TDE": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Data Pump": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "RMAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Linux Shell Scripting": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Crontab": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "AWR": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ADDM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "EXPLAIN PLAN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Trace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "TKPROF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "STATSPACK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 14c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WebLogic 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Veeam Backup and Recovery": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 9": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM LTO 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI IAM": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ITIL V3 Foundation": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Prince2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA - Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator Training": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Teradata Certified Administrator (V2R5)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Fusion Applications": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle E-Business Suite R12": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud Budgetary Control": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financial Accounting Hub": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Transactional Business Intelligence (OTBI)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Financial Reporting Studio (FRS)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Smart View": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Data Loader": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Hyperion FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Business Process Management (BPM)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "AIM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OUM Methodology": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Sub Ledger Accounting (SLA)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Windows 2007/2008/2010": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1056-22 - Oracle Financials Cloud: Receivables 2022 Implementation Professional": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1054-21 - Oracle Financials Cloud: General Ledger 2021 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-1004 - Oracle Financials Cloud: General Ledger 2018 Implementation Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "1Z0-517 - Oracle EBS R12.1 Payables Essentials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "BIP": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Pega Rules Process Engine": ["<PERSON><PERSON><PERSON>"], "Pega Group Benefits Insurance Framework": ["<PERSON><PERSON><PERSON>"], "Pega Product Builder": ["<PERSON><PERSON><PERSON>"], "Pega 7.2.2": ["<PERSON><PERSON><PERSON>"], "Pega 7.3": ["<PERSON><PERSON><PERSON>"], "Pega 7.4": ["<PERSON><PERSON><PERSON>"], "Pega 8": ["<PERSON><PERSON><PERSON>"], "Unit testing": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "A<PERSON><PERSON>_resume"], "Harness": ["<PERSON><PERSON><PERSON>"], "Sections": ["<PERSON><PERSON><PERSON>"], "Flow Actions": ["<PERSON><PERSON><PERSON>"], "List-View": ["<PERSON><PERSON><PERSON>"], "Summary-View Reports": ["<PERSON><PERSON><PERSON>"], "Report Definitions": ["<PERSON><PERSON><PERSON>"], "Clipboard": ["<PERSON><PERSON><PERSON>"], "Tracer": ["<PERSON><PERSON><PERSON>"], "PLA": ["<PERSON><PERSON><PERSON>"], "Product locking": ["<PERSON><PERSON><PERSON>"], "Package locking": ["<PERSON><PERSON><PERSON>"], "Ruleset locking": ["<PERSON><PERSON><PERSON>"], "SDLC": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "E-Commerce": ["<PERSON><PERSON><PERSON>"], "Insurance": ["<PERSON><PERSON><PERSON>"], "Agents": ["<PERSON><PERSON><PERSON>"], "Queue Processors": ["<PERSON><PERSON><PERSON>"], "Decision Rules": ["<PERSON><PERSON><PERSON>"], "Declarative Rules": ["<PERSON><PERSON><PERSON>"], "Application Design": ["<PERSON><PERSON><PERSON>"], "Case Management": ["<PERSON><PERSON><PERSON>"], "Process Flows": ["<PERSON><PERSON><PERSON>"], "Screen Flows": ["<PERSON><PERSON><PERSON>"], "Data Transforms": ["<PERSON><PERSON><PERSON>"], "Activities": ["<PERSON><PERSON><PERSON>"], "Rule Resolution": ["<PERSON><PERSON><PERSON>"], "Enterprise Class Structure": ["<PERSON><PERSON><PERSON>"], "Dev Studio": ["<PERSON><PERSON><PERSON>"], "App Studio": ["<PERSON><PERSON><PERSON>"], "Admin Studio": ["<PERSON><PERSON><PERSON>"], "CDH": ["<PERSON><PERSON><PERSON>"], "Document review": ["<PERSON><PERSON><PERSON>"], "Pega Marketing Consultant": ["<PERSON><PERSON><PERSON>"], "Senior System Architect": ["<PERSON><PERSON><PERSON>"], "System Architect": ["<PERSON><PERSON><PERSON>"], "Postman": ["<PERSON><PERSON><PERSON>", "Vidwaan_vidwan_resume", "SivakumarDega_CV"], "Selenium IDE": ["pavani_resume"], "Selenium RC": ["pavani_resume"], "Selenium WebDriver": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Selenium Grid": ["pavani_resume"], "TestNG": ["pavani_resume", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "QTP": ["pavani_resume"], "Gherkin": ["pavani_resume"], "Ruby": ["pavani_resume", "Vidwaan_vidwan_resume"], "Tortoise SVN": ["pavani_resume"], "HP Quality Center": ["pavani_resume"], "SeeTest (Experitest)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "ACCELQ": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "JBehave": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "HP ALM": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "BrowserStack": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "LambdaTest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Functional Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Smoke Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "System Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Integration Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV", "A<PERSON><PERSON>_resume"], "Regression Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "User Acceptance Testing (UAT)": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "UI Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Mobile Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Automation Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Web Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Compatibility Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Sanity Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Ad hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Design": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Plan Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Test Scripting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Defect Tracking": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Bug Reporting": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "AI-powered Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Mobile Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Web Application Automation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "IOS Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "Android Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)", "SivakumarDega_CV"], "SSAS": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Desktop": ["<PERSON><PERSON><PERSON><PERSON>"], "Power BI Service": ["<PERSON><PERSON><PERSON><PERSON>"], "M Language": ["<PERSON><PERSON><PERSON><PERSON>"], "Dimensional Modeling": ["<PERSON><PERSON><PERSON><PERSON>"], "Microsoft BI Stack": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Pivot": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Gateway": ["<PERSON><PERSON><PERSON><PERSON>"], "Row-Level Security (RLS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Data Flows": ["<PERSON><PERSON><PERSON><PERSON>"], "DataMart": ["<PERSON><PERSON><PERSON><PERSON>"], "Power Automate": ["<PERSON><PERSON><PERSON><PERSON>"], "Visual Studio Code": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP CO": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP SD": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Cash Management (CM)": ["<PERSON><PERSON><PERSON><PERSON>"], "ASAP Methodology": ["<PERSON><PERSON><PERSON><PERSON>"], "HFM": ["<PERSON><PERSON><PERSON><PERSON>"], "FDMEE": ["<PERSON><PERSON><PERSON><PERSON>"], "PCBS": ["<PERSON><PERSON><PERSON><PERSON>"], "WRICEF Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Mapping": ["<PERSON><PERSON><PERSON><PERSON>"], "FIT-GAP Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Solution Design": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Warehouse Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process": ["<PERSON><PERSON><PERSON><PERSON>"], "Demand Forecasting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Pooling": ["<PERSON><PERSON><PERSON><PERSON>"], "Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "F110 Automatic Payment Program": ["<PERSON><PERSON><PERSON><PERSON>"], "Real-time Cash Visibility System": ["<PERSON><PERSON><PERSON><PERSON>"], "Inhouse Cash Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Best Practices": ["<PERSON><PERSON><PERSON><PERSON>"], "Generally Accepted Accounting Principles (GAAP)": ["<PERSON><PERSON><PERSON><PERSON>"], "International Financial Reporting Standards (IFRS)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Analysis": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Data Entry": ["<PERSON><PERSON><PERSON><PERSON>"], "AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Customer Accounting": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Items": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Integration": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP S/4HANA": ["Uday"], "ABAP": ["Uday"], "OData": ["Uday"], "SAP UI5": ["Uday"], "Fiori": ["Uday"], "Fiori Elements": ["Uday"], "PI/PO": ["Uday"], "AIF": ["Uday"], "BRF+": ["Uday"], "Business Workflow": ["Uday"], "CRM": ["Uday"], "Web Dynpro ABAP": ["Uday"], "RAP": ["Uday"], "BTP": ["Uday"], "CAPM": ["Uday"], "Procure to Pay (PTP)": ["Uday"], "Order to Cash Management (OTC)": ["Uday"], "Production Planning (PP)": ["Uday"], "Quality Management (QM)": ["Uday"], "FI-AP": ["Uday"], "FI-AR": ["Uday"], "FI-GL (FICO)": ["Uday"], "RTR": ["Uday"], "SCM": ["Uday", "<PERSON><PERSON><PERSON>"], "Product Life Cycle Management (PLM)": ["Uday"], "Advanced Planner Optimizer (APO)": ["Uday"], "Extended Warehouse Management (EWM)": ["Uday"], "Data Dictionary (DDIC)": ["Uday"], "Module Pool Programming": ["Uday"], "Object-Oriented ABAP (OOABAP)": ["Uday"], "RFCs": ["Uday"], "BADIs": ["Uday"], "BDC": ["Uday"], "BAPI": ["Uday"], "BP Integrations": ["Uday"], "Enhancement Points": ["Uday"], "User Exits": ["Uday"], "Customer Exits": ["Uday"], "ALE IDOCs": ["Uday"], "Inbound/Outbound Proxy": ["Uday"], "SAP NetWeaver Gateway": ["Uday"], "Service Registration": ["Uday"], "Service Extension": ["Uday"], "CDS Views": ["Uday"], "AMDP": ["Uday"], "SAP Fiori List Report Application": ["Uday"], "Web IDE": ["Uday"], "BSP": ["Uday"], "SAP Fiori Launchpad": ["Uday"], "SAP UI5 Framework": ["Uday"], "Business Objects (BO)": ["Uday"], "ATC": ["Uday"], "SPDD": ["Uday"], "SPAU": ["Uday"], "SAP Security": ["Uday"], "PFTC": ["Uday"], "SAP Certified Development Specialist - ABAP for SAP HANA 2.0": ["Uday"], "SAP Certified Development Associate - SAP Fiori Application Developer": ["Uday"], "Next.js": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "REST APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1", "Vidwaan_vidwan_resume", "Soham_Resume_Java"], "GraphQL APIs": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS SAM": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Apache ECharts": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cognito": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "OIDC": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Mantine UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Vite": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "MySQL Aurora": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "AWS API Gateway": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Styled Components": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Sanity": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Amplify": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "ShadCN UI": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Salesforce": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "CDL": ["Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "Cisco Catalyst 9800 Wireless Controller": ["A<PERSON><PERSON>_resume"], "Talwar controller": ["A<PERSON><PERSON>_resume"], "AireOS controller": ["A<PERSON><PERSON>_resume"], "Cisco Access Points": ["A<PERSON><PERSON>_resume"], "Talwar Simulator": ["A<PERSON><PERSON>_resume"], "WiFi": ["A<PERSON><PERSON>_resume"], "802.11": ["A<PERSON><PERSON>_resume"], "WLAN": ["A<PERSON><PERSON>_resume"], "Ethernet": ["A<PERSON><PERSON>_resume"], "IP": ["A<PERSON><PERSON>_resume"], "TCP": ["A<PERSON><PERSON>_resume"], "UDP": ["A<PERSON><PERSON>_resume"], "CAPWAP": ["A<PERSON><PERSON>_resume"], "NETCONF": ["A<PERSON><PERSON>_resume"], "YANG": ["A<PERSON><PERSON>_resume"], "Swift": ["A<PERSON><PERSON>_resume"], "ClearCase": ["A<PERSON><PERSON>_resume"], "Cisco catalyst 3750 Switch": ["A<PERSON><PERSON>_resume"], "ios-xe asr 1K router": ["A<PERSON><PERSON>_resume"], "OpenWRT": ["A<PERSON><PERSON>_resume"], "Linux": ["A<PERSON><PERSON>_resume", "<PERSON><PERSON><PERSON> (3)", "<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "QMI": ["A<PERSON><PERSON>_resume"], "AT interfaces": ["A<PERSON><PERSON>_resume"], "Ubus": ["A<PERSON><PERSON>_resume"], "Qualcomm SDX hardware": ["A<PERSON><PERSON>_resume"], "AT&T Echo controller": ["A<PERSON><PERSON>_resume"], "POLARIS": ["A<PERSON><PERSON>_resume"], "GDB": ["A<PERSON><PERSON>_resume"], "Gre": ["A<PERSON><PERSON>_resume"], "RFID": ["A<PERSON><PERSON>_resume"], "AeroScout tags": ["A<PERSON><PERSON>_resume"], "Cisco Aironet outdoor mesh access points": ["A<PERSON><PERSON>_resume"], "Cisco Prime Infrastructure": ["A<PERSON><PERSON>_resume"], "Mac filtering": ["A<PERSON><PERSON>_resume"], "Bash": ["<PERSON><PERSON><PERSON> (3)"], "Android App Development": ["<PERSON><PERSON><PERSON> (3)"], "Flask": ["<PERSON><PERSON><PERSON> (3)"], "Django": ["<PERSON><PERSON><PERSON> (3)"], "GraphQL": ["<PERSON><PERSON><PERSON> (3)"], "Amazon Web Services": ["<PERSON><PERSON><PERSON> (3)", "Updated_CV_-_<PERSON><PERSON><PERSON>_Ahmad1_1"], "macOS": ["<PERSON><PERSON><PERSON> (3)"], "Kali Linux": ["<PERSON><PERSON><PERSON> (3)"], "OAuth": ["<PERSON><PERSON><PERSON> (3)", "SivakumarDega_CV"], "AWS Certified Solutions Architect - Associate": ["<PERSON><PERSON><PERSON> (3)", "Chandra_Resume"], "Amazon SQS": ["Vidwaan_vidwan_resume"], "Amazon Athena": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON>"], "Amazon Glue": ["Vidwaan_vidwan_resume"], "Amazon Firehose": ["Vidwaan_vidwan_resume"], "AWS Step Functions": ["Vidwaan_vidwan_resume"], "Data Structures": ["Vidwaan_vidwan_resume", "<PERSON><PERSON><PERSON> (3)"], "Test Driven Development (TDD)": ["Vidwaan_vidwan_resume"], "Mockito": ["Vidwaan_vidwan_resume", "Soham_Resume_Java"], "Spark SQL": ["Vidwaan_vidwan_resume"], "Server-Side Encryption": ["Vidwaan_vidwan_resume"], "IAM Role Management": ["Vidwaan_vidwan_resume"], "EKS": ["Vidwaan_vidwan_resume"], "BottleRocket": ["Vidwaan_vidwan_resume"], "React.js": ["Soham_Resume_Java"], "Firebase Cloud Services": ["Soham_Resume_Java"], "Cassandra": ["Soham_Resume_Java"], "Android Studio": ["Soham_Resume_Java"], "Bluetooth": ["Soham_Resume_Java"], "Java Development": ["Soham_Resume_Java"], "Advanced Java Development": ["Soham_Resume_Java"], "Salesforce Platform Administrator": ["Soham_Resume_Java"], "Salesforce Platform Developer": ["Soham_Resume_Java"], "Appium": ["SivakumarDega_CV"], "Perfecto": ["SivakumarDega_CV"], "SeeTest": ["SivakumarDega_CV"], "REST Assured": ["SivakumarDega_CV"], "Karate Framework": ["SivakumarDega_CV"], "UFT": ["SivakumarDega_CV"], "LeanFT": ["SivakumarDega_CV"], "Zephyr": ["SivakumarDega_CV"], "Quality Center": ["SivakumarDega_CV"], "Informatica 10.2": ["SivakumarDega_CV"], "MicroStrategy": ["SivakumarDega_CV"], "CICS": ["SivakumarDega_CV"], "JCL": ["SivakumarDega_CV"], "VSAM": ["SivakumarDega_CV"], "Sufi": ["SivakumarDega_CV"], "File-Aid": ["SivakumarDega_CV"], "CA DevTest": ["SivakumarDega_CV"], "ATOM": ["SivakumarDega_CV"], "GCP": ["SivakumarDega_CV"], "SSO": ["SivakumarDega_CV"], "Test Strategy": ["SivakumarDega_CV"], "Test Design": ["SivakumarDega_CV"], "Test effort estimation": ["SivakumarDega_CV"], "Requirements mapping": ["SivakumarDega_CV"], "Risk-based testing": ["SivakumarDega_CV"], "End-to-End testing": ["SivakumarDega_CV"], "User Acceptance testing": ["SivakumarDega_CV"], "Database testing": ["SivakumarDega_CV"], "API testing": ["SivakumarDega_CV"], "Web services testing": ["SivakumarDega_CV"], "Microservices testing": ["SivakumarDega_CV"], "Browser compatibility testing": ["SivakumarDega_CV"], "Exploratory testing": ["SivakumarDega_CV"], "ETL testing": ["SivakumarDega_CV"], "Data Warehouse testing": ["SivakumarDega_CV"], "Interactive Voice Response (IVR) testing": ["SivakumarDega_CV"], "Customer Telephony Integration (CTI) testing": ["SivakumarDega_CV"], "Mainframes testing": ["SivakumarDega_CV"], "Service Virtualization": ["SivakumarDega_CV"], "Continuous Integration and Continuous Deployment (CI/CD)": ["SivakumarDega_CV"], "Oracle Fusion Financials": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Financials Cloud Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Payable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Accounts Receivable": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "General Ledger": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Fixed Assets": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Cash Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Expenses": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order Management": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "OTBI": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "FRS": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SmartView": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Procure to Pay (P2P)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Order to Cash (O2C)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Record to Report (R2R)": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Allocations": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Oracle Cloud": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "Intercompany": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SeeTest/Experitest": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Case Development": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Schedule Creation": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SAP Financial Accounting (FI)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Controlling (CO)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Sales and Distribution (SD)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Materials Management (MM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Hyperion Financial Management (HFM)": ["<PERSON><PERSON><PERSON><PERSON>"], "Financial Data Management (FDMEE)": ["<PERSON><PERSON><PERSON><PERSON>"], "Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Receivable (AR)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Accounts Payable (AP)": ["<PERSON><PERSON><PERSON><PERSON>"], "General Ledger (GL)": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "Purchase Order (PO) Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Inventory Planning": ["<PERSON><PERSON><PERSON><PERSON>"], "Automatic Payment Program (F110)": ["<PERSON><PERSON><PERSON><PERSON>"], "Network Security": ["<PERSON><PERSON><PERSON> (3)"], "Object Oriented Programming": ["<PERSON><PERSON><PERSON> (3)"], "Operating Systems": ["<PERSON><PERSON><PERSON> (3)"], "Design and Analysis of Algorithms": ["<PERSON><PERSON><PERSON> (3)"], "DBMS": ["<PERSON><PERSON><PERSON> (3)"], "Mainframe Testing": ["SivakumarDega_CV"], "Performance Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "User Interface Testing": ["SivakumarDega_CV"], "Manual Testing": ["SivakumarDega_CV"], "Mobile Web Testing": ["SivakumarDega_CV", "<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Desktop Application Testing": ["SivakumarDega_CV"], "Web Application Testing": ["SivakumarDega_CV"], "Data Analytics": ["Laxman_Gite"], "Real-time Data Analytics": ["Laxman_Gite"], "NoSQL Databases": ["Laxman_Gite"], "Blueprints": ["Laxman_Gite"], "Test Case Execution": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Custom Visuals (Power BI)": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Drill Through": ["<PERSON><PERSON><PERSON><PERSON>"], "Parameters": ["<PERSON><PERSON><PERSON><PERSON>"], "Cascading Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Interactive Dashboards": ["<PERSON><PERSON><PERSON><PERSON>"], "Reports": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCA)": ["<PERSON><PERSON><PERSON><PERSON>"], "Automated Bank Reconciliation": ["<PERSON><PERSON><PERSON><PERSON>"], "Pricing Strategies": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP MM Functionalities": ["<PERSON><PERSON><PERSON><PERSON>"], "Business Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "IVR Testing": ["SivakumarDega_CV"], "CTI Testing": ["SivakumarDega_CV"], "Continuous Integration": ["SivakumarDega_CV", "pavani_resume"], "Continuous Deployment": ["SivakumarDega_CV"], "High-Performance Architecture Design": ["Laxman_Gite"], "Container-based Architecture Design": ["Laxman_Gite"], "High Throughput System Architecture Design": ["Laxman_Gite"], "Real-Time Data Analytics Solution Architecture Design": ["Laxman_Gite"], "E-commerce Architecture Design": ["Laxman_Gite"], "Microsoft technologies": ["Laxman_Gite"], "Microsoft Azure, Amazon Web Services (AWS) and C# Programming Certified Professional": ["Laxman_Gite"], "Coded UI": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "MS SharePoint Server": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], ".NET Core 6.0": ["PunniyaKodi V updated resume"], ".NET Core 8.0": ["PunniyaKodi V updated resume"], "XML Web Services": ["PunniyaKodi V updated resume"], ".NET Core Apps": ["PunniyaKodi V updated resume"], "Domain Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Web Jobs": ["Chary"], "Model-View-Controller (MVC)": ["Chary"], "Tridion CMS": ["Chary"], "Internet of Things (IoT)": ["Chary"], "Azure SQL": ["<PERSON><PERSON>_DotNET"], "Azure Pipelines": ["<PERSON><PERSON>_DotNET"], "Rally": ["<PERSON><PERSON>_DotNET"], "Multi-AZ": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "High Availability": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume", "KRISHNA_KANT_NIRALA_Oracle_DBA"], "Disaster Recovery": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS-Kops (EKS)": ["<PERSON><PERSON><PERSON>"], "HTTP": ["<PERSON><PERSON><PERSON>"], "TLS": ["<PERSON><PERSON><PERSON>"], "Windows Application Migration": ["Puneet"], "OTT": ["Puneet"], "RACI Matrix": ["Puneet"], "S3": ["<PERSON>", "Vidwaan_vidwan_resume"], "Performance Tuning": ["<PERSON>"], "Query Optimization": ["<PERSON>"], "Informatica Intelligent Cloud Services (IICS)": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Center (IDMC)": ["<PERSON><PERSON><PERSON>"], "Amazon Lake Formation": ["<PERSON><PERSON><PERSON>"], "Cloud Migration": ["<PERSON><PERSON><PERSON>"], "Nebula": ["<PERSON><PERSON><PERSON>"], "Advanced Analytics": ["DA manager <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Data Compliance": ["DA manager <PERSON><PERSON>"], "Key Performance Indicators (KPIs)": ["DA manager <PERSON><PERSON>"], "Service Level Agreement (SLAs)": ["DA manager <PERSON><PERSON>"], "Data Flow Architectures": ["DA manager <PERSON><PERSON>"], "Data Collection": ["DA manager <PERSON><PERSON>"], "Data Storage Strategies": ["DA manager <PERSON><PERSON>"], "Agile Transformation": ["DA manager <PERSON><PERSON>"], "ISO27001 Compliance": ["DA manager <PERSON><PERSON>"], "Mechanical Design": ["<PERSON><PERSON><PERSON>"], "Service Lifting Tools Design": ["<PERSON><PERSON><PERSON>"], "2D Drawings Review": ["<PERSON><PERSON><PERSON>"], "Unix Shell Scripting": ["<PERSON><PERSON><PERSON>"], "Slowly Changing Dimensions (SCD Type 1, 2, 3, 4)": ["<PERSON><PERSON><PERSON>"], "Technical Design": ["<PERSON><PERSON><PERSON>"], "Technical Architecture": ["<PERSON><PERSON><PERSON>"], "Big Data": ["<PERSON><PERSON><PERSON>"], "Real-time Data Integration": ["<PERSON><PERSON><PERSON>"], "Amazon Web Services (S3, EC2, Lambda)": ["<PERSON><PERSON><PERSON>"], "Silverlight": ["<PERSON><PERSON><PERSON>"], "ITIL Foundation": ["Chandra_Resume"], "AWS Certified Developer - Associate": ["Chandra_Resume"], "AWS Certified SysOps Administrator - Associate": ["Chandra_Resume"], "Oracle 19c Data Guard": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c RAC": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 8": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Red Hat Linux Enterprise 7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager 12c": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Enterprise Manager Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Export/Import": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Transportable Tablespaces": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQLTrace": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Real Application Cluster": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Windows Server 2016": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Fault Tolerance": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Scalability": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Virtualization": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Data Warehouse (ADW)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Autonomous Transaction Processing (ATP)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "VCN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Object Storage": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Load Balancing": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Auto Scaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "CDN": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "WAF": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata X9M-2": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI-Oracle Cloud Infrastructure Foundations Associate": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCA-Oracle Database Administrator Certified": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle 19c Database Administrator": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO/IEC 27001": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 20000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "ISO 27000": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Pega Marketing Consultant (Certification)": ["<PERSON><PERSON><PERSON>"], "Senior System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "System Architect (Certification)": ["<PERSON><PERSON><PERSON>"], "RICEF": ["Uday"], "SAP Script": ["Uday"], "Smart Forms": ["Uday"], "Adobe Forms": ["Uday"], "ALV Reports": ["Uday"], "Mac filter configuration": ["A<PERSON><PERSON>_resume"], "Athena": ["Vidwaan_vidwan_resume"], "JDK 8": ["Vidwaan_vidwan_resume"], "JDK 17": ["Vidwaan_vidwan_resume"], "Java Development (Certification)": ["Soham_Resume_Java"], "Advanced Java Development (Certification)": ["Soham_Resume_Java"], "Salesforce Platform Administrator (Certification - In process)": ["Soham_Resume_Java"], "Salesforce Platform Developer (Certification - In process)": ["Soham_Resume_Java"], "C# Programming Certified Professional": ["Laxman_Gite"], "Strapi": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "Wix": ["<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dot_Net_Full_Stack_Developer"], "AG Grid": ["PunniyaKodi V updated resume"], "Domain-Driven Design (DDD)": ["PunniyaKodi V updated resume"], "Pub/Sub": ["PunniyaKodi V updated resume"], "HPSM": ["Chary"], "Subversion": ["Chary"], "Saga": ["Chary"], "Choreography": ["Chary"], "Circuit Breaker": ["Chary"], "AKS": ["Chary"], "Repository Design Pattern": ["Chary"], "Factory Design Pattern": ["Chary"], "Abstract Factory Design Pattern": ["Chary"], "SEO": ["Chary"], "Object-Oriented Programming": ["Chary"], "IoT": ["Chary"], "Microsoft .NET": ["<PERSON><PERSON>_DotNET"], "AWS S3": ["<PERSON><PERSON>_DotNET", "<PERSON><PERSON><PERSON>"], "Amazon Elastic Container Registry (ECR)": ["<PERSON><PERSON>_DotNET"], "ADFS": ["<PERSON><PERSON>_DotNET"], "Maven POM": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Build.xml": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "AWS Storage Services": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Security Groups": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Multi-AZ VPC": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Amazon CloudFormation": ["<PERSON><PERSON><PERSON>"], "Amazon Auto Scaling": ["<PERSON><PERSON><PERSON>"], "Microsoft Project (MPP)": ["Puneet"], "Strategic Planning": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "EZTrieve": ["<PERSON><PERSON><PERSON>_Project_manager_Nithin1"], "Peoplesoft Financials": ["<PERSON>"], "Peoplesoft Supply Chain Management": ["<PERSON>"], "Account Payables": ["<PERSON>"], "Account Receivables": ["<PERSON>"], "GL": ["<PERSON>"], "Billing": ["<PERSON>"], "Dimension Modeling": ["<PERSON>"], "Fact Tables": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "Relational Databases": ["<PERSON><PERSON><PERSON><PERSON> Data analyst"], "Data Mining": ["<PERSON><PERSON><PERSON>"], "DWH": ["<PERSON><PERSON><PERSON>"], "DM": ["<PERSON><PERSON><PERSON>"], "Dimension Tables": ["<PERSON><PERSON><PERSON>"], "Dashboards": ["<PERSON><PERSON><PERSON>"], "Data Security and Compliance": ["DA manager <PERSON><PERSON>"], "Product Validation": ["<PERSON><PERSON><PERSON>"], "API Development (ICS, ICRT)": ["<PERSON><PERSON><PERSON>"], "Production Support (L3)": ["<PERSON><PERSON><PERSON>"], "Test Plan Development": ["<PERSON><PERSON><PERSON>"], "Test Script Development": ["<PERSON><PERSON><PERSON>"], "IntelliJ IDEA": ["Chandra_Resume"], "Spiral Model": ["Chandra_Resume"], "Prototype Model": ["Chandra_Resume"], "Oracle Database Administration": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud Infrastructure (OCI)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle GoldenGate (implied)": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Java JDK": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Oracle Cloud I-Receivables": ["<PERSON><PERSON><PERSON>-Fusion Financial Cloud"], "SharePoint": ["pavani_resume"], "Microsoft Office": ["pavani_resume", "<PERSON><PERSON><PERSON>"], "Ad-hoc Testing": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Test Planning": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "SSMS": ["<PERSON><PERSON><PERSON><PERSON>"], "SQL Server Data Tools": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Profit Center Accounting (PCBS)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AR Processing & Reporting": ["<PERSON><PERSON><PERSON><PERSON>"], "Cash Discount Management": ["<PERSON><PERSON><PERSON><PERSON>"], "Dispute and Deduction Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP FI-GL Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP AP/AR Transactions": ["<PERSON><PERSON><PERSON><PERSON>"], "Vendor/Customer Open Item Management": ["<PERSON><PERSON><PERSON><PERSON>"], "BPP Documentation": ["<PERSON><PERSON><PERSON><PERSON>"], "Order-to-Delivery Process Optimization": ["<PERSON><PERSON><PERSON><PERSON>"], "Certified SAP Functional Consultant": ["<PERSON><PERSON><PERSON><PERSON>"], "PFTC Roles": ["Uday"], "SAP ECC": ["Uday"], "SAP Scripts": ["Uday"], "Device Drivers": ["A<PERSON><PERSON>_resume"], "LED Manager": ["A<PERSON><PERSON>_resume"], "Mesh Networking": ["A<PERSON><PERSON>_resume"], "Cisco Aironet": ["A<PERSON><PERSON>_resume"], "ATOM (Mainframes/AS400 automation tool)": ["SivakumarDega_CV"], "Test Automation": ["SivakumarDega_CV"], "Test Strategy Creation": ["SivakumarDega_CV"], "Test Coverage": ["SivakumarDega_CV"], "Requirements Prioritization": ["SivakumarDega_CV"], "ASP": ["PunniyaKodi V updated resume"], "N-tier applications": ["PunniyaKodi V updated resume"], "Client-server applications": ["PunniyaKodi V updated resume"], "Auto-scaling": ["PunniyaKodi V updated resume"], "Artifactory": ["<PERSON><PERSON><PERSON>_04_<PERSON><PERSON><PERSON>_Resume"], "Physical Database Design": ["<PERSON>"], "Database Tuning": ["<PERSON>"], "Snowpark API": ["<PERSON>"], "PII": ["<PERSON>"], "PCI": ["<PERSON>"], "Trifacta": ["<PERSON>"], "Oracle 8i": ["<PERSON>"], "Oracle 11i": ["<PERSON>"], "DB2 8.1": ["<PERSON>"], "Python Worksheet": ["<PERSON>"], "Certified Professional Data Engineer": ["<PERSON>"], "ETL Design": ["<PERSON><PERSON><PERSON>"], "ETL Development": ["<PERSON><PERSON><PERSON>"], "Python Scripting": ["<PERSON><PERSON><PERSON>"], "Informatica Data Management Cloud (IDMC)": ["<PERSON><PERSON><PERSON>"], "Data Mart": ["<PERSON><PERSON><PERSON>"], "Requirement Gathering": ["<PERSON><PERSON><PERSON>"], "Solution Architecture": ["<PERSON><PERSON><PERSON>"], "Dojo": ["<PERSON><PERSON>"], "Spring": ["Chandra_Resume"], "Oracle Grid Control 11g": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "SQL*Plus": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "OCI AutoScaling": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E980": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "HP/IBM Power E850": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Exadata CS DBX7": ["KRISHNA_KANT_NIRALA_Oracle_DBA"], "Defect Management": ["<PERSON><PERSON><PERSON>ddy_ QA Consultant (1)"], "Speedometer Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Sankey Diagrams": ["<PERSON><PERSON><PERSON><PERSON>"], "Pareto Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "Waterfall Charts": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Material Master Data Management": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Procurement Processes": ["<PERSON><PERSON><PERSON><PERSON>"], "GAAP (Generally Accepted Accounting Principles)": ["<PERSON><PERSON><PERSON><PERSON>"], "IFRS (International Financial Reporting Standards)": ["<PERSON><PERSON><PERSON><PERSON>"], "SAP Treasury Modules": ["<PERSON><PERSON><PERSON><PERSON>"], "LTE": ["A<PERSON><PERSON>_resume"], "5G": ["A<PERSON><PERSON>_resume"], "Firehose": ["Vidwaan_vidwan_resume"], "Test Estimation": ["SivakumarDega_CV"]}, "skill_categories": {}, "skill_metadata": {}}