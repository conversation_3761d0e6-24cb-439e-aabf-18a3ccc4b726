#!/usr/bin/env python3
"""
Test Phase 2 Enhanced AI Matching Features
Tests all new functionality: JD caching, evolution tracking, multi-profile support, skill clustering, smart filtering
"""

import os
import sys
import json
import logging
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_phase2_features():
    """Test all Phase 2 features"""
    print("🚀 TESTING PHASE 2 ENHANCED AI MATCHING FEATURES")
    print("=" * 70)
    
    try:
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize the matcher
        print("🔧 Initializing Enhanced AI Matcher with Phase 2 features...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False
        
        print("✅ AI connection successful")
        
        # Load test consultants
        consultants = load_test_consultants()
        if not consultants:
            print("❌ No test consultants loaded")
            return False
        
        print(f"📋 Loaded {len(consultants)} test consultants")
        
        # Test 1: JD Caching
        print(f"\n🧪 TEST 1: JD Caching and Evolution Detection")
        test_jd_caching(matcher, consultants)
        
        # Test 2: Multi-Profile Support
        print(f"\n🧪 TEST 2: Multi-Profile Support")
        test_multi_profile_support(matcher, consultants)
        
        # Test 3: Smart Filtering
        print(f"\n🧪 TEST 3: Smart Filtering")
        test_smart_filtering(matcher, consultants)
        
        # Test 4: Skill Categorization
        print(f"\n🧪 TEST 4: Skill Categorization")
        test_skill_categorization(matcher, consultants)
        
        # Test 5: Match Logging
        print(f"\n🧪 TEST 5: Match Decision Logging")
        test_match_logging(matcher)
        
        print(f"\n🎉 ALL PHASE 2 TESTS COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error in Phase 2 testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_test_consultants():
    """Load consultant data from CSV"""
    consultants = []
    
    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Skip header
    for line in lines[1:]:
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]
            
            # Try to find matching resume file
            resume_path = find_resume_file(name)
            
            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)
    
    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None
    
    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')
    
    # List all resume files
    resume_files = os.listdir(resumes_dir)
    
    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')
        
        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)
    
    return None

def test_jd_caching(matcher, consultants):
    """Test JD caching functionality"""
    print("   🔍 Testing JD caching and evolution detection...")
    
    # Test job description
    jd1 = """
    Senior Java Developer needed for fintech startup.
    Requirements: 5+ years Java, Spring Boot, AWS experience.
    Location: Remote. Visa: H1B welcome.
    """
    
    # First match - should create cache entry
    print("   📝 First match (should create cache)...")
    selected_consultants1, message1, response1 = matcher.enhanced_match_consultant_to_job(jd1, consultants)
    
    if selected_consultants1:
        print(f"   ✅ First match successful: {len(selected_consultants1)} consultant(s)")
    else:
        print("   ⚠️ No match found for first JD")
    
    # Second match with identical JD - should use cache
    print("   📝 Second match with identical JD (should use cache)...")
    selected_consultants2, message2, response2 = matcher.enhanced_match_consultant_to_job(jd1, consultants)
    
    if selected_consultants2:
        print(f"   ✅ Cached match successful: {len(selected_consultants2)} consultant(s)")
    else:
        print("   ⚠️ No cached match found")
    
    # Third match with evolved JD - should detect change and re-match
    jd2 = """
    Senior Java Developer needed for fintech startup.
    Requirements: 8+ years Java, Spring Boot, AWS, Kubernetes experience.
    Location: Remote. Visa: H1B welcome.
    """
    
    print("   📝 Third match with evolved JD (should detect change)...")
    selected_consultants3, message3, response3 = matcher.enhanced_match_consultant_to_job(jd2, consultants)
    
    if selected_consultants3:
        print(f"   ✅ Evolution detection successful: {len(selected_consultants3)} consultant(s)")
    else:
        print("   ⚠️ No match found for evolved JD")

def test_multi_profile_support(matcher, consultants):
    """Test multi-profile support"""
    print("   🔍 Testing multi-profile support...")
    
    # Job description that should match multiple consultants
    jd = """
    .NET Full Stack Developer for enterprise application.
    Requirements: 5+ years .NET, C#, ASP.NET, Angular/React.
    Location: Any US location. Visa: H1B acceptable.
    """
    
    selected_consultants, message, response = matcher.enhanced_match_consultant_to_job(jd, consultants)
    
    if selected_consultants and len(selected_consultants) > 1:
        print(f"   ✅ Multi-profile match successful: {len(selected_consultants)} consultants")
        for i, consultant in enumerate(selected_consultants, 1):
            print(f"      {i}. {consultant.get('name', 'Unknown')} - {consultant.get('match_confidence', 'Unknown')} confidence")
    elif selected_consultants and len(selected_consultants) == 1:
        print(f"   ✅ Single profile match: {selected_consultants[0].get('name', 'Unknown')}")
    else:
        print("   ⚠️ No multi-profile matches found")

def test_smart_filtering(matcher, consultants):
    """Test smart filtering functionality"""
    print("   🔍 Testing smart filtering...")
    
    # Job description with specific requirements
    jd = """
    Senior Software Engineer position.
    Requirements: 10+ years experience, US Citizens only.
    Location: Must be based in California.
    No visa sponsorship available.
    """
    
    selected_consultants, message, response = matcher.enhanced_match_consultant_to_job(jd, consultants)
    
    if selected_consultants:
        print(f"   ✅ Smart filtering successful: {len(selected_consultants)} consultant(s) passed filters")
        for consultant in selected_consultants:
            print(f"      - {consultant.get('name', 'Unknown')}: {consultant.get('visa_status', 'Unknown')} visa")
    else:
        print("   ⚠️ No consultants passed smart filtering (expected if no US Citizens in test data)")

def test_skill_categorization(matcher, consultants):
    """Test skill categorization"""
    print("   🔍 Testing skill categorization...")
    
    # Test with a consultant who has a resume
    consultants_with_resumes = [c for c in consultants if c.get('resume_path')]
    
    if consultants_with_resumes:
        test_consultant = consultants_with_resumes[0]
        print(f"   📄 Testing skill categorization for: {test_consultant['name']}")
        
        # Extract skills (this should also categorize them)
        skills = matcher.extract_skills_from_resume(test_consultant['resume_path'])
        
        if skills:
            print(f"   ✅ Extracted {len(skills)} skills")
            
            # Check if categorization was saved
            consultant_name = os.path.splitext(os.path.basename(test_consultant['resume_path']))[0]
            if consultant_name in matcher.skill_categories:
                categories = matcher.skill_categories[consultant_name]
                print(f"   ✅ Skills categorized into {len(categories)} categories:")
                for category, category_skills in categories.items():
                    print(f"      - {category}: {len(category_skills)} skills")
            else:
                print("   ⚠️ Skill categorization not found")
        else:
            print("   ⚠️ No skills extracted")
    else:
        print("   ⚠️ No consultants with resumes found for categorization test")

def test_match_logging(matcher):
    """Test match decision logging"""
    print("   🔍 Testing match decision logging...")
    
    # Check if match log exists and has entries
    if matcher.match_log:
        print(f"   ✅ Match log contains {len(matcher.match_log)} entries")
        
        # Show latest entry
        if matcher.match_log:
            latest_entry = matcher.match_log[-1]
            print(f"   📝 Latest entry: {latest_entry.get('match_type', 'Unknown')} match")
            print(f"      Timestamp: {latest_entry.get('timestamp', 'Unknown')}")
            print(f"      Consultants: {latest_entry.get('consultant_count', 0)}")
    else:
        print("   ⚠️ No match log entries found")

if __name__ == "__main__":
    success = test_phase2_features()
    sys.exit(0 if success else 1)
