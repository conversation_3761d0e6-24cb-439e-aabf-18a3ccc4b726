#!/usr/bin/env python3
"""
Setup script for AI Consultant Matcher
This script helps install dependencies and configure the AI matcher
"""

import os
import sys
import subprocess
import json

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        # Install google-generativeai
        subprocess.check_call([sys.executable, "-m", "pip", "install", "google-generativeai==0.8.3"])
        print("✅ google-generativeai installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_config():
    """Setup configuration file"""
    print("\n⚙️ Setting up configuration...")
    
    config_path = os.path.join("backend", "config.json")
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found at {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check if Gemini API key is already configured
        current_key = config.get('gemini_api_key', '')
        if current_key and current_key != 'YOUR_GEMINI_API_KEY_HERE':
            print("✅ Gemini API key is already configured")
            return True
        
        # Prompt user for API key
        print("\n🔑 Gemini API Key Setup")
        print("To use the AI matcher, you need a Gemini API key from Google AI Studio.")
        print("Get your free API key at: https://makersuite.google.com/app/apikey")
        print()
        
        api_key = input("Enter your Gemini API key (or press Enter to skip): ").strip()
        
        if api_key:
            config['gemini_api_key'] = api_key
            
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=4)
            
            print("✅ Gemini API key saved to config.json")
            return True
        else:
            print("⚠️ Skipped API key setup. You can add it later to config.json or set GEMINI_API_KEY environment variable")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up config: {e}")
        return False

def test_setup():
    """Test the setup"""
    print("\n🧪 Testing setup...")
    
    try:
        # Change to backend directory for testing
        original_dir = os.getcwd()
        backend_dir = os.path.join(original_dir, "backend")
        
        if os.path.exists(backend_dir):
            os.chdir(backend_dir)
        
        # Import and test the AI matcher
        sys.path.insert(0, backend_dir)
        
        from ai_matcher import AIConsultantMatcher
        
        # Try to initialize (this will test API key)
        try:
            matcher = AIConsultantMatcher()
            print("✅ AI Matcher initialized successfully")
            
            # Test connection
            if matcher.test_connection():
                print("✅ Connection to Gemini AI successful")
                return True
            else:
                print("❌ Connection to Gemini AI failed")
                return False
                
        except Exception as e:
            print(f"⚠️ AI Matcher initialization failed: {e}")
            print("This might be due to missing or invalid API key")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import AI Matcher: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing setup: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_dir)

def main():
    """Main setup function"""
    print("🚀 AI Consultant Matcher Setup\n")
    
    # Check if we're in the right directory
    if not os.path.exists("backend"):
        print("❌ Please run this script from the project root directory")
        print("Expected directory structure:")
        print("  project_root/")
        print("    ├── backend/")
        print("    ├── frontend/")
        print("    └── setup_ai_matcher.py")
        return
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        return
    
    # Setup configuration
    config_success = setup_config()
    
    # Test setup
    if test_setup():
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Make sure your Gemini API key is configured")
        print("2. Run the email handler to start processing emails with AI")
        print("3. Check the logs for AI matching results")
    else:
        print("\n⚠️ Setup completed with warnings")
        if not config_success:
            print("\n🔧 To complete setup:")
            print("1. Get a Gemini API key from: https://makersuite.google.com/app/apikey")
            print("2. Add it to backend/config.json as 'gemini_api_key'")
            print("3. Or set it as GEMINI_API_KEY environment variable")

if __name__ == "__main__":
    main()
