[{"timestamp": "2025-05-30T17:04:56.214384", "jd_hash": "018f8f69cbde4e3b4475103241367fc2", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 5+ years Java, Spring Boot, AWS experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "enhanced_multi", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "AWS"]}], "reasoning": "<PERSON><PERSON><PERSON>'s skills and experience in Java, Spring Boot, and AWS, along with her recent experience at AWS, far exceed the minimum requirements.  Other candidates lacked sufficient experience in the core required technologies.", "consultant_count": 1}, {"timestamp": "2025-05-30T17:04:56.216012", "jd_hash": "018f8f69cbde4e3b4475103241367fc2", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 5+ years Java, Spring Boot, AWS experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "AWS"]}], "reasoning": "Used cached result", "consultant_count": 1}, {"timestamp": "2025-05-30T17:04:59.533566", "jd_hash": "64d64d01f1a68fa17a53a438be448b25", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 8+ years Java, Spring Boot, AWS, Kubernetes experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "enhanced_multi", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "Kubernetes", "AWS"]}], "reasoning": "<PERSON><PERSON><PERSON>'s skillset directly addresses the core requirements of the job description, making her a highly suitable candidate. While other candidates possess relevant skills, none achieve the same level of alignment with the specified technologies and experience.", "consultant_count": 1}]