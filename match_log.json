[{"timestamp": "2025-05-30T17:04:56.214384", "jd_hash": "018f8f69cbde4e3b4475103241367fc2", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 5+ years Java, Spring Boot, AWS experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "enhanced_multi", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "AWS"]}], "reasoning": "<PERSON><PERSON><PERSON>'s skills and experience in Java, Spring Boot, and AWS, along with her recent experience at AWS, far exceed the minimum requirements.  Other candidates lacked sufficient experience in the core required technologies.", "consultant_count": 1}, {"timestamp": "2025-05-30T17:04:56.216012", "jd_hash": "018f8f69cbde4e3b4475103241367fc2", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 5+ years Java, Spring Boot, AWS experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "AWS"]}], "reasoning": "Used cached result", "consultant_count": 1}, {"timestamp": "2025-05-30T17:04:59.533566", "jd_hash": "64d64d01f1a68fa17a53a438be448b25", "job_description_preview": "\n    Senior Java Developer needed for fintech startup.\n    Requirements: 8+ years Java, Spring Boot, AWS, Kubernetes experience.\n    Location: Remote. Visa: H1B welcome.\n    ", "match_type": "enhanced_multi", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Spring Boot", "Kubernetes", "AWS"]}], "reasoning": "<PERSON><PERSON><PERSON>'s skillset directly addresses the core requirements of the job description, making her a highly suitable candidate. While other candidates possess relevant skills, none achieve the same level of alignment with the specified technologies and experience.", "consultant_count": 1}, {"timestamp": "2025-05-30T17:13:17.330620", "jd_hash": "f16e8ed22be95f702c8a1bd23e2923cf", "job_description_preview": "\n            We need a DevOps Engineer to manage our AWS cloud infrastructure.\n\n            Required Skills:\n            - 5+ years of DevOps and cloud infrastructure experience\n            - Strong e...", "match_type": "enhanced_multi", "selected_consultants": [{"name": "Punniyakodi", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["AWS services (EC2, S3, RDS, ECS, CloudWatch)", "Terraform", "CI/CD pipeline development", "Python"]}, {"name": "Konda<PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["AWS services (EC2, CloudWatch)", "Terraform", "Kubernetes", "<PERSON>", "CI/CD", "Shell Scripting", "PowerShell"]}], "reasoning": "The selection prioritizes candidates with explicit mention of key AWS services and Infrastructure as Code tools.  While some skills were implied through broader experience in DevOps, the presence of several essential skills such as Terraform and experience with at least several AWS services (EC2, S3, Cloudwatch, etc.) were prioritized.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:19:40.353767", "jd_hash": "260af4107fef23a1326a97a0ed672c34", "job_description_preview": "\n            Senior Java Developer position for government contractor.\n            \n            Required Skills:\n            - 8+ years of Java development experience\n            - Strong experience w...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 80, "key_skills": ["Microsoft .NET", ".NET Core", "C#", "SQL Server", "Azure", "Azure App Services", "Azure Pipelines", "<PERSON>er", "Kubernetes", "AWS", "Microservices"]}, {"name": "Chary", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Java", "Microservices", "REST", "SOAP", "Azure", "Azure Functions", "Azure App Services", "SQL Server", "AWS", "Amazon DynamoDB", "Amazon EC2", "AWS Lambda"]}], "reasoning": "The selection prioritizes consultants with demonstrable experience in relevant technologies such as Azure/AWS, microservices, and strong backend development skills (Java or equivalent .NET expertise).  Given the client's strict US Citizen requirement, it is imperative to verify immigration statuses.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:19:50.525424", "jd_hash": "45b460275f2ff8644edef62db87567aa", "job_description_preview": "\n            .NET Full Stack Developer for financial services company.\n            \n            Required Skills:\n            - 6+ years of .NET Core/.NET Framework experience\n            - Strong C# p...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": [".NET Core/.NET Framework", "C#", "ASP.NET MVC", "Web API", "SQL Server", "Angular"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": [".NET Core", "C#", "ASP.NET MVC", "Web API", "SQL Server", "Angular"]}], "reasoning": "The selection is based primarily on the candidates' demonstrated skills and experience in the core technologies required for the role, specifically focusing on .NET Framework/Core, C#, ASP.NET MVC/Web API, SQL Server, and Angular/React.  Experience level was also a key consideration. Visa status and location requirements were secondary factors, but are crucial for client awareness.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:19:59.967275", "jd_hash": "455e5af114097bf1d154f0ecd4d9d9d3", "job_description_preview": "\n            DevOps Engineer for startup in Silicon Valley.\n            \n            Required Skills:\n            - 5+ years of DevOps experience\n            - Strong AWS experience\n            - Dock...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "Punniyakodi", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["AWS", "Terraform", "CI/CD", "<PERSON>er", "Kubernetes"]}, {"name": "Chary", "confidence": "High", "skill_match_percentage": 85, "key_skills": ["AWS", "<PERSON>er", "Kubernetes", "CI/CD Pipeline", "Azure"]}], "reasoning": "Both <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> display strong technical skills aligning with the job description's requirements for AWS expertise, Docker and Kubernetes experience, CI/CD pipeline development, and Infrastructure as Code (Terraform).  <PERSON><PERSON><PERSON>yako<PERSON>'s resume exhibits a more comprehensive match with the listed skills. However, both consultants should be considered with careful attention to the location and visa restrictions highlighted in the job description.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:26.882673", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Both <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> exhibit a high degree of skill alignment with the job description. <PERSON><PERSON><PERSON> displays a slightly broader and deeper skillset within the Azure ecosystem, while <PERSON><PERSON><PERSON> offers comparable expertise with a slightly longer tenure.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:32.502800", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:38.554169", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:43.447569", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:48.022372", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:52.517204", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:26:56.961875", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:01.566654", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:06.190140", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:10.744213", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:15.313241", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:19.873387", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:31.361487", "jd_hash": "8c58784094f53e61c5d99fa812697710", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 80, "key_skills": ["C#", "ASP.NET Core", "Web API", "Azure Functions", "Azure", "SQL Server", "Oracle (PL/SQL)"]}, {"name": "Chary", "confidence": "High", "skill_match_percentage": 75, "key_skills": ["ASP.NET Core", "C#", "Web API", "Azure", "Azure Functions", "SQL Server", "Oracle PL/SQL"]}], "reasoning": "Both <PERSON><PERSON><PERSON> and <PERSON><PERSON> demonstrate a high level of proficiency in the core technologies required for the Oracle JDE payroll techno-functional SME role. Their experience with related technologies, such as Azure and various database systems, further strengthens their qualifications.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:43.070554", "jd_hash": "b5d022063fbdba633d0084dd59f7a968", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL"]}, {"name": "Chary", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["ASP.NET Core", "ASP.NET MVC", "C#", "Azure Functions", "Azure App Services", "Azure Active Directory", "SQL Server", "PowerShell (implied through extensive .NET experience and automation skills)", "Azure DevOps", "CI/CD Pipeline"]}], "reasoning": "Both <PERSON><PERSON><PERSON> and <PERSON><PERSON> demonstrate a high level of proficiency in the core technologies required for the M365 Administrator role.  Their experience with Azure services, .NET framework, and automation capabilities significantly outweighs the other candidates.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:54.595427", "jd_hash": "ab0860cab8e4d79087c6bd1581dccf4e", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 80, "key_skills": ["Oracle (PL/SQL)", "ASP.NET Core", "Web API"]}, {"name": "Chary", "confidence": "High", "skill_match_percentage": 75, "key_skills": ["ASP.NET", "ASP.NET Core", "Oracle PL/SQL", "Web API", "Microservices"]}], "reasoning": "The selection prioritizes candidates with explicit mention of Oracle PL/SQL and strong experience in related .NET technologies.  The overlap between .NET skills (especially Web API) and the required Oracle EBS, Apex, and OAF suggests a high likelihood of successful skill transfer.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:27:58.846671", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:28:03.310126", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:28:07.849686", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:28:12.419573", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "cached", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Logic Apps", "Service Bus", "API Management", "Azure Storage", "Cosmos DB", "<PERSON><PERSON>", "YAML Pipelines", "Azure AD", "Virtual Network", "Application Insights", "Log Analytics", "<PERSON>", "<PERSON>er", "CI/CD", "Microservices", "Azure Monitor", "SQL Server", "Azure Container Registry", "Serverless Architecture", "Kubernetes"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["C#", "ASP.NET Core", "Web API", "MS Azure", "Azure Functions", "Blob Storage", "Table Storage", "App Services", "Redis", "App Insights", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL", "Angular"]}], "reasoning": "Used cached result", "consultant_count": 2}, {"timestamp": "2025-05-30T17:28:24.319493", "jd_hash": "22479020800233db969a4a059bb569d9", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["C#", "ASP.NET Core", "Web API", "Azure Functions", "Azure Blob Storage", "Azure Table Storage", "Azure App Services", "Azure APIM", "Azure Service Bus", "Logic Apps", "SQL Server", "T-SQL"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 82, "key_skills": ["ASP.NET Core", "C#", "Azure", "Azure App Service", "Azure Functions", "Azure Storage", "Azure Monitor", "Azure DevOps", "Azure Service Bus", "Microservices", "SQL Server", "CosmosDB", "Web API"]}], "reasoning": "Both <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> possess a high percentage of skills explicitly or implicitly mentioned in the job description.  Their extensive experience with C#, ASP.NET Core, Web API, Azure services, and relevant databases make them top candidates.  The selection prioritizes the best technical fit considering the forwarded job requirements.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:36:34.368232", "jd_hash": "4cd5b98a56308da5018bed36df1ba71b", "job_description_preview": "\n            We are looking for a Python Data Scientist with machine learning expertise.\n            \n            Required Skills:\n            - 5+ years of Python programming experience\n            -...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Data Analysis", "Data Visualization", "SQL", "Python", "<PERSON><PERSON>", "Power BI"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 70, "key_skills": ["SQL", "Python", "Data Modeling", "Data Warehousing", "Regression Analysis", "Predictive Modeling"]}], "reasoning": "Both <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> possess a significant portion of the required skills for this position. Although neither perfectly aligns with every requirement, their strong foundation in data science and relevant tools, coupled with their remote availability, makes them worthy of consideration. Further discussion might be needed to clarify their experience in machine learning model deployment and Jupyter notebooks.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:36:43.595813", "jd_hash": "bf8c53d06af53664ba5315b671dd142a", "job_description_preview": "\n            Java Developer needed for microservices development.\n            \n            Required Skills:\n            - 6+ years of Java development experience\n            - Strong experience with S...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["Java", "Spring Boot", "Microservices Architecture", "REST APIs", "JUnit", "<PERSON><PERSON>"]}, {"name": "Chary", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Java", "Microservices", "REST", "Web API"]}], "reasoning": "The selection prioritizes technical skills precisely matching the job description.  <PERSON><PERSON> provides a strong, high-confidence match given his extensive and relevant Java experience. <PERSON><PERSON> offers a medium-confidence match because of his Java and API experience, despite a stronger .NET focus. The selection considers both top-performing candidates but balances the quality of match with the potential need to further clarify skills.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:36:52.361398", "jd_hash": "9776b445e3f162fef9e2c7d6ac055bbe", "job_description_preview": "\n            Frontend Developer specializing in React applications.\n            \n            Required Skills:\n            - 4+ years of React.js development experience\n            - Strong JavaScript/...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["ReactJS", "TypeScript", "JavaScript", "HTML", "CSS", "Git"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 85, "key_skills": ["React", "HTML5", "CSS3", "TypeScript", "JavaScript", "Git"]}], "reasoning": "The selection of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> is based on their strong alignment with the technical skills outlined in the job description. Both candidates possess the essential frontend skills (React, JavaScript, TypeScript, HTML, CSS) and experience with Git version control, exceeding the 80% skill match threshold for high confidence.  Their diverse skill sets and stated willingness to relocate are additional factors considered.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:37:01.567470", "jd_hash": "f2c4b149cf9dc643d9becbe414312c9e", "job_description_preview": "\n            DevOps Engineer with AWS cloud expertise.\n            \n            Required Skills:\n            - 5+ years of DevOps experience\n            - Strong AWS services experience (EC2, S3, RDS,...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["5+ years of DevOps experience", "Amazon Web Services (AWS)", "<PERSON>er", "Kubernetes", "CI/CD", "Terraform", "Linux system administration"]}, {"name": "Konda<PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["DevOps experience", "Amazon Web Services (AWS)", "CloudFormation", "Kubernetes", "Terraform", "<PERSON>", "<PERSON>er", "Linux system administration"]}], "reasoning": "The selection prioritized candidates demonstrating strong AWS expertise and a comprehensive skillset in DevOps, including CI/CD pipeline development and Infrastructure as Code.  While a perfect match wasn't found among all candidates, these two provide the strongest combination of technical expertise and experience relevant to the job description.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:41:35.462132", "jd_hash": "8a9f97dbacb4b8947bdee11167befcf8", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "Konda<PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["Kubernetes", "<PERSON>", "SonarQube", "<PERSON>er", "Shell Scripting", "AWS", "CI/CD", "Linux", "Ansible", "Git", "OpenShift", "Azure DevOps", "ELK Stack"]}, {"name": "<PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Java", "Python", "SQL", "<PERSON>er", "<PERSON>", "CI/CD", "Linux", "<PERSON><PERSON>", "Shell", "Git", "AWS", "Azure", "LDAP"]}], "reasoning": "The selection prioritizes candidates with demonstrable experience in OpenShift and Azure DevOps, along with a strong foundation in core DevOps practices and tooling.  While a perfect match is difficult to find given the specific tool requirements, these two represent the best balance between essential skills and potential.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:41:46.937660", "jd_hash": "cd1d061c11d3cb82bb91abbde4ab8393", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["C#", ".NET", "ASP.NET Core", "Azure", "SQL Server", "Microservices", "<PERSON>er", "Kubernetes"]}, {"name": "Chary", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["ASP.NET Core", "C#", ".NET", "Azure", "Microservices", "SQL Server", "<PERSON>er", "Kubernetes"]}], "reasoning": "The selection prioritizes candidates with direct experience in C# and .NET, which are fundamental for NXOpen development.  While explicit NXOpen experience is ideal, candidates with strong transferable skills in related technologies and extensive experience in relevant frameworks (microservices, cloud, containerization) were also considered due to the high demand and potential for quick onboarding.  Location and visa status were also considered and noted in the email.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:43:14.659058", "jd_hash": "1c960b469ea05fe3d848c59f0fecc303", "job_description_preview": "\n        We are looking for a Java Developer with Spring Boot experience.\n        \n        Required Skills:\n        - 5+ years of Java development experience\n        - Strong experience with Spring Bo...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["ASP.NET Core", ".NET Framework", "C#", "Web API", "SQL Server", "Angular", "Microservices", "Azure", "REST", "Agile Methodologies"]}, {"name": "Punniyakodi", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["C#", ".NET Core", "Web API", "SQL Server", "RESTful Web Services", "Agile", "Scrum"]}], "reasoning": "The selection prioritizes technical skill alignment. <PERSON><PERSON><PERSON>'s profile shows the closest match to the required Java/Spring Boot skills, with a substantial overlap. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s skills are more transferable from a .NET background to a Java environment, but their full-stack experience increases their potential value and provides a medium-confidence option.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:51:21.419795", "jd_hash": "4cd5b98a56308da5018bed36df1ba71b", "job_description_preview": "\n            We are looking for a Python Data Scientist with machine learning expertise.\n            \n            Required Skills:\n            - 5+ years of Python programming experience\n            -...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Python", "SQL", "Snowflake", "Databricks", "Data Modeling"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 70, "key_skills": ["Python", "SQL", "Data Analysis", "Data Modeling", "Predictive Modeling"]}], "reasoning": "The selection prioritizes candidates with demonstrated experience in Python and SQL, core requirements of the role.  While a perfect match with all specified Python libraries wasn't found, the selected consultants possess a strong foundation in data science and machine learning, making them suitable candidates for further evaluation.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:51:31.574304", "jd_hash": "bf8c53d06af53664ba5315b671dd142a", "job_description_preview": "\n            Java Developer needed for microservices development.\n            \n            Required Skills:\n            - 6+ years of Java development experience\n            - Strong experience with S...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "Chary", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["Java", "Microservices", "REST", "Spring Framework", "Spring Boot", "Web API", "<PERSON><PERSON>"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Java", "Microservices", "REST", "C#", ".NET Core", "Web API", "Angular"]}], "reasoning": "The selection prioritized candidates with direct experience in Java and microservices architecture. <PERSON><PERSON> presents as the strongest match given the breadth and depth of his skillset. <PERSON><PERSON><PERSON> offers a solid medium confidence match due to his background in Java and related technologies, albeit with a stronger .NET focus. Both candidates fulfill the experience requirement.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:51:41.104474", "jd_hash": "9776b445e3f162fef9e2c7d6ac055bbe", "job_description_preview": "\n            Frontend Developer specializing in React applications.\n            \n            Required Skills:\n            - 4+ years of React.js development experience\n            - Strong JavaScript/...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 90, "key_skills": ["React", "ReactJS", "Redux Toolkit", "TypeScript", "JavaScript", "Webpack", "HTML5", "CSS3", "Git"]}, {"name": "Chary", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["ReactJS", "TypeScript", "JavaScript", "HTML5", "CSS3"]}], "reasoning": "The selection prioritizes candidates with demonstrable React.js experience, complemented by strong JavaScript/TypeScript skills and experience with essential tools like Webpack and Git.  Visa status and relocation willingness are considered, but technical skill alignment is the primary factor.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:51:52.145121", "jd_hash": "f2c4b149cf9dc643d9becbe414312c9e", "job_description_preview": "\n            DevOps Engineer with AWS cloud expertise.\n            \n            Required Skills:\n            - 5+ years of DevOps experience\n            - Strong AWS services experience (EC2, S3, RDS,...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["AWS", "Amazon EC2", "Amazon S3", "Amazon RDS", "<PERSON>er", "Kubernetes", "<PERSON>", "CI/CD", "AWS CloudFormation"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "Medium", "skill_match_percentage": 75, "key_skills": ["Amazon Web Services (AWS)", "EC2", "S3", "AWS Lambda", "<PERSON>er", "Kubernetes", "<PERSON>", "CI/CD"]}], "reasoning": "The selection prioritized candidates demonstrating strong AWS expertise, experience with Docker and Kubernetes, and a proven track record in CI/CD pipeline development.  <PERSON><PERSON><PERSON> presents a strong match due to her extensive experience and explicit mention of key skills. <PERSON><PERSON><PERSON><PERSON>'s profile is also suitable, though lacks mention of Infrastructure as Code, and Linux administration.  Both candidates require further discussion regarding visa and relocation to Austin, TX.", "consultant_count": 2}, {"timestamp": "2025-05-30T17:52:02.390607", "jd_hash": "cdffb3a8fef316da1f744841c14774b8", "job_description_preview": "\n            QA Automation Engineer for test framework development.\n            \n            Required Skills:\n            - 4+ years of QA automation experience\n            - Strong experience with Se...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "<PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["Selenium WebDriver", "TestNG", "API testing (SoapUI - similar to Postman/REST Assured)", "<PERSON> (CI/CD experience)"]}, {"name": "<PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 85, "key_skills": ["Selenium WebDriver", "Java", "REST Assured", "<PERSON>", "Appium (mobile testing - indirectly relevant)"]}], "reasoning": "The selection prioritizes candidates with direct experience in Selenium WebDriver and relevant testing frameworks.  While performance testing wasn't explicitly listed on the resumes, the broad automation experience and years of expertise of the selected candidates suggest proficiency in this area.  Furthermore, both possess experience within CI/CD pipelines, a critical aspect of the role.  Location and visa status were also taken into consideration. ", "consultant_count": 2}, {"timestamp": "2025-05-30T17:57:06.874511", "jd_hash": "b4c6aa3c7647a7f992d05cda0f104843", "job_description_preview": "Best Regards,\r\n\r\n\r\n<PERSON><PERSON><PERSON>\r\n*Technical Recruiter *\r\nPh. No +1 ‪(*************‬\r\nWeb: www.decisionsix.com\r\nEmail: <EMAIL> <<EMAIL>>\r\nLinkedIn: https://www.li...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "Punniyakodi", "confidence": "High", "skill_match_percentage": 90, "key_skills": [".NET Core 6.0", ".NET Core 8.0", "ASP.NET MVC", "ASP.NET", "Web API", "Azure DevOps", "CI/CD", "Amazon Web Services (AWS)", "Microservices", "SQL Server", "Azure"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 88, "key_skills": ["C#", ".NET 6", "ASP.NET Core", "Azure Developer", "Azure Functions", "Azure Storage", "Cosmos DB", "CI/CD", "Microservices", "Azure AD", "SQL Server"]}], "reasoning": "Both <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> exhibit a strong match to the technical requirements, with over 80% skill alignment.  Their experience in Azure services, .NET technologies, microservices, and CI/CD pipelines makes them excellent candidates.  Punniyakodi offers broader skillset with AWS experience, while <PERSON>x<PERSON> demonstrates a deeper focus on Azure.", "consultant_count": 2}, {"timestamp": "2025-05-30T18:03:06.174136", "jd_hash": "83e2a3463626712b4cb5279b63c21cb4", "job_description_preview": "\n        We are looking for a Senior .NET Developer with C# and ASP.NET Core experience.\n        \n        Required Skills:\n        - 8+ years of .NET development experience\n        - Strong experience...", "match_type": "enhanced_multi_no_filter", "selected_consultants": [{"name": "Chary", "confidence": "High", "skill_match_percentage": 90, "key_skills": [".NET Core", "ASP.NET MVC", "C#", "Web API", "SQL Server"]}, {"name": "<PERSON><PERSON><PERSON>", "confidence": "High", "skill_match_percentage": 85, "key_skills": [".NET 6", "ASP.NET Core", "ASP.NET MVC", "Web API", "SQL Server"]}], "reasoning": "Both <PERSON><PERSON> and <PERSON><PERSON><PERSON> exhibit a strong alignment with the required technical skills. <PERSON><PERSON>'s resume showcases a more explicit emphasis on .NET versions matching the job description. <PERSON><PERSON><PERSON>'s experience is broader, encompassing additional Azure technologies, but he also clearly possesses the core .NET expertise required.", "consultant_count": 2}]