import os
import re
import docx
from datetime import datetime
from pdfminer.high_level import extract_text
from pdfminer.layout import LAParams
from nltk.tokenize import sent_tokenize
import logging
from skill_repository import SkillRepository

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResumeParser:
    def __init__(self, resumes_dir='resumes'):
        """Initialize the resume parser

        Note: Skill extraction has been disabled - skills are now taken from the JSON database
        """
        self.resumes_dir = resumes_dir
        self.skill_repository = SkillRepository()  # Keep for compatibility

    def extract_text_from_pdf(self, pdf_path):
        """Extract text from PDF file using pdfminer.six"""
        try:
            logger.info(f"Extracting text from PDF: {pdf_path}")

            # Configure PDF extraction parameters
            laparams = LAParams(
                line_margin=0.5,
                word_margin=0.1,
                char_margin=2.0,
                all_texts=True
            )

            # Extract text from PDF
            text = extract_text(pdf_path, laparams=laparams)

            if not text:
                logger.warning(f"No text extracted from {pdf_path}")
                return ""

            logger.info(f"Successfully extracted {len(text)} characters from {pdf_path}")

            # Clean up the extracted text
            # Remove excessive whitespace
            text = re.sub(r'\s+', ' ', text)
            # Remove special characters that might interfere with parsing
            text = re.sub(r'[^\w\s\.\,\;\:\-\(\)\[\]\{\}\'\"\&\%\$\#\@\!\?\+\=\/\\]', '', text)

            return text
        except Exception as e:
            logger.error(f"Error extracting text from PDF {pdf_path}: {e}")
            return ""

    def extract_text_from_docx(self, docx_path):
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(docx_path)
            text = ""
            for para in doc.paragraphs:
                text += para.text + "\n"
            return text
        except Exception as e:
            print(f"Error extracting text from DOCX {docx_path}: {e}")
            return ""

    def extract_name_from_filename(self, filename):
        """Extract consultant name from filename"""
        # Remove file extension
        name = os.path.splitext(os.path.basename(filename))[0]

        # Remove common prefixes/suffixes
        name = re.sub(r'(resume|cv|_|-|\.)+', ' ', name, flags=re.IGNORECASE)

        # Clean up extra spaces
        name = re.sub(r'\s+', ' ', name).strip()

        return name

    def extract_skills(self, text):
        """
        Extract skills from resume text and years of experience
        """
        if not text:
            logger.warning("Empty text provided for experience extraction")
            return [], 0

        # Extract years of experience
        years_experience = self._extract_years_experience(text.lower())

        # Extract skills from text
        skills = self._extract_skills_from_text(text)

        logger.info(f"Extracted {years_experience} years of experience and {len(skills)} skills")

        return skills, years_experience

    def _extract_skills_from_text(self, text):
        """Extract skills from resume text using the skill repository"""
        # Get all skills from the repository
        all_skills = self.skill_repository.get_all_skills()

        # If we don't have any skills in the repository, add some common ones
        if not all_skills:
            common_skills = [
                # Programming Languages
                "Python", "Java", "JavaScript", "C#", "C++", "Ruby", "PHP", "Swift", "Kotlin", "Go",
                # Web Technologies
                "HTML", "CSS", "React", "Angular", "Vue.js", "Node.js", "Express", "Django", "Flask",
                # Databases
                "SQL", "MySQL", "PostgreSQL", "MongoDB", "Oracle", "SQL Server", "Redis", "Elasticsearch",
                # Cloud & DevOps
                "AWS", "Azure", "GCP", "Docker", "Kubernetes", "Jenkins", "Git", "CI/CD", "Terraform",
                # Data Science & AI
                "Machine Learning", "Deep Learning", "TensorFlow", "PyTorch", "NLP", "Computer Vision",
                # Microsoft Technologies
                ".NET", "ASP.NET", "SharePoint", "Power BI", "Dynamics 365", "Office 365",
                # Mobile
                "iOS", "Android", "React Native", "Flutter", "Xamarin",
                # Other
                "Agile", "Scrum", "REST API", "GraphQL", "Microservices", "Serverless"
            ]

            # Add these skills to the repository
            for skill in common_skills:
                self.skill_repository.add_skill(skill)

            # Update all_skills
            all_skills = self.skill_repository.get_all_skills()

        # Convert text to lowercase for case-insensitive matching
        text_lower = text.lower()

        # Find skills in the text
        found_skills = []
        for skill in all_skills:
            # Skip very short skills (likely false positives)
            if len(skill) < 2:
                continue

            # Check if skill is in text (case-insensitive)
            if skill.lower() in text_lower:
                found_skills.append(skill)

        # Return unique skills
        return list(set(found_skills))

    def _extract_years_experience(self, text):
        """Extract years of experience from resume text"""
        # Look for patterns like "X years of experience" or "X+ years"
        experience_patterns = [
            r'(\d+)\+?\s*years?\s+(?:of\s+)?experience',
            r'experience\s+(?:of\s+)?(\d+)\+?\s*years?',
            r'(\d+)\+?\s*years?\s+(?:in\s+)?(?:the\s+)?(?:industry|field)',
            r'(?:professional\s+)?experience\s+(?:of\s+)?(\d+)\+?\s*years?',
            r'(?:career|professional)\s+(?:spanning|of)\s+(\d+)\+?\s*years?',
            r'(\d+)\+?\s*years?\s+(?:of\s+)?(?:professional|relevant|total)\s+experience',
            r'(?:over|more\s+than)\s+(\d+)\s+years?\s+(?:of\s+)?experience',
            r'experience\s*:\s*(\d+)\+?\s*years?',
            r'(\d+)\+?\s*years?\s+experience',
            r'worked\s+(?:for|as)(?:\s+\w+){0,3}\s+(\d+)\+?\s*years?'
        ]

        # Also look for dates in resume to calculate experience
        date_ranges = re.findall(r'((?:19|20)\d{2})\s*(?:-|–|to)\s*((?:19|20)\d{2}|present|current|now)', text, re.IGNORECASE)

        max_years = 0
        current_year = datetime.now().year

        # Check explicit mentions of years of experience
        for pattern in experience_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    years = int(match)
                    max_years = max(max_years, years)
                except ValueError:
                    continue

        # Calculate years from date ranges
        for start_year, end_year in date_ranges:
            try:
                start = int(start_year)
                if end_year.lower() in ('present', 'current', 'now'):
                    end = current_year
                else:
                    end = int(end_year)

                # Only consider reasonable date ranges
                if 1960 <= start <= current_year and start <= end <= current_year:
                    years = end - start
                    max_years = max(max_years, years)
            except ValueError:
                continue

        # Default to 5 years if we couldn't extract experience
        if max_years == 0:
            # Look for senior/lead/architect titles which suggest more experience
            if re.search(r'senior|lead|architect|manager|director|head|chief|principal', text, re.IGNORECASE):
                max_years = 8
            else:
                max_years = 5

        return max_years

    def parse_resume(self, file_path):
        """Parse a single resume file - extracts basic info but not skills"""
        try:
            logger.info(f"Parsing resume: {file_path}")
            file_ext = os.path.splitext(file_path)[1].lower()

            # Extract text based on file type
            if file_ext == '.pdf':
                text = self.extract_text_from_pdf(file_path)
            elif file_ext == '.docx':
                text = self.extract_text_from_docx(file_path)
            else:
                logger.warning(f"Unsupported file format: {file_path}")
                return None

            if not text:
                logger.warning(f"Failed to extract text from {file_path}")
                return None

            # Extract consultant information
            name = self.extract_name_from_filename(file_path)
            _, years_experience = self.extract_skills(text)  # Only get years_experience, skills are empty

            # Extract summary if available
            summary = self._extract_summary(text)

            # Count words in the resume
            word_count = len(text.split())

            # Create consultant profile
            consultant = {
                'name': name,
                'skills': [],  # Empty skills list - skills will be added manually
                'resume_path': file_path,
                'years_experience': years_experience,
                'summary': summary,
                'word_count': word_count,
                'file_type': file_ext[1:],  # Remove the dot
                'skill_count': 0,  # No skills from resume
                'location': '',    # Will be filled in manually
                'visa_status': 'H1B',  # Default visa status
                'relocation': 'Yes'    # Default relocation preference
            }

            logger.info(f"Successfully parsed resume for {name} (skills extraction disabled)")
            return consultant

        except Exception as e:
            logger.error(f"Error parsing resume {file_path}: {e}")
            return None

    def _extract_summary(self, text):
        """Extract a summary from the resume text"""
        # Look for common summary section headers
        summary_headers = [
            'summary', 'professional summary', 'profile', 'professional profile',
            'career objective', 'objective', 'about me', 'overview'
        ]

        lines = text.split('\n')
        summary = ""
        in_summary_section = False

        for i, line in enumerate(lines):
            line_lower = line.lower().strip()

            # Check if this line is a summary header
            is_header = False
            for header in summary_headers:
                if header in line_lower and len(line_lower) < 50:  # Headers are usually short
                    in_summary_section = True
                    is_header = True
                    break

            # If we're in a summary section and this isn't a header, add to summary
            if in_summary_section and not is_header:
                summary += line + " "

                # Check if we've reached the end of the summary section
                # Usually summaries are 1-3 sentences
                if len(summary.split('.')) > 3 or len(summary) > 500:
                    break

                # Check if the next line might be a new section header
                if i < len(lines) - 1:
                    next_line = lines[i+1].lower().strip()
                    if any(next_line.startswith(h) for h in ['experience', 'education', 'skills', 'projects']):
                        break

        # If no summary section found, create one from the beginning of the resume
        if not summary:
            # Take the first 2-3 sentences or 300 characters
            sentences = sent_tokenize(text)
            if sentences:
                summary = ' '.join(sentences[:min(3, len(sentences))])
                if len(summary) > 300:
                    summary = summary[:300] + '...'

        return summary.strip()

    def parse_all_resumes(self):
        """Parse all resumes in the resumes directory - no skill extraction"""
        consultants = []

        # Create resumes directory if it doesn't exist
        os.makedirs(self.resumes_dir, exist_ok=True)

        # Try multiple possible locations for resume files
        possible_paths = [
            # Current directory's resumes folder
            os.path.join(os.getcwd(), self.resumes_dir),
            # Parent directory's resumes folder
            os.path.join(os.path.dirname(os.getcwd()), self.resumes_dir),
            # Parent directory's resume folder (singular)
            os.path.join(os.path.dirname(os.getcwd()), 'resume'),
            # Absolute path to backend/resumes
            os.path.join(os.path.dirname(os.path.abspath(__file__)), self.resumes_dir),
            # Absolute path to resumes at project root
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), self.resumes_dir),
            # Absolute path to resume at project root
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resume')
        ]

        resume_files = []

        # Try each path until we find resume files
        for path in possible_paths:
            logger.info(f"Looking for resumes in: {path}")
            if os.path.exists(path) and os.path.isdir(path):
                files = [os.path.join(path, f) for f in os.listdir(path)
                        if f.endswith(('.pdf', '.docx'))]
                if files:
                    resume_files = files
                    logger.info(f"Found {len(resume_files)} resume files in {path}")
                    break

        if not resume_files:
            logger.warning("No resume files found in any location")
            return []

        # Parse each resume
        for file_path in resume_files:
            consultant = self.parse_resume(file_path)
            if consultant:
                # Add consultant to the list
                consultants.append(consultant)

        logger.info(f"Parsed {len(consultants)} resumes (skill extraction disabled)")
        return consultants
