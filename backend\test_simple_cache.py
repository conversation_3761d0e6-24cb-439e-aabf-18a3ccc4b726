#!/usr/bin/env python3
"""
Simple test to verify skills caching is working
"""

import os
import sys
import time

def test_simple_cache():
    """Simple test for skills caching"""
    print("🧪 SIMPLE CACHING TEST")
    print("=" * 50)
    
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        print("🔧 Initializing matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Clear cache
        matcher.skills_cache = {}
        print(f"📊 Cache cleared. Size: {len(matcher.skills_cache)}")
        
        # Test with one resume file
        resume_path = "resumes/Laxman_Gite.pdf"
        if not os.path.exists(resume_path):
            print(f"❌ Resume file not found: {resume_path}")
            return False
        
        print(f"📄 Testing with: {resume_path}")
        
        # First extraction
        print(f"\n🚀 FIRST EXTRACTION:")
        start_time = time.time()
        skills_1 = matcher.extract_skills_from_resume(resume_path)
        first_time = time.time() - start_time
        
        print(f"⏱️ First extraction: {first_time:.2f} seconds")
        print(f"📊 Skills found: {len(skills_1)}")
        print(f"📊 Cache size: {len(matcher.skills_cache)}")
        
        # Second extraction (should use cache)
        print(f"\n🚀 SECOND EXTRACTION:")
        start_time = time.time()
        skills_2 = matcher.extract_skills_from_resume(resume_path)
        second_time = time.time() - start_time
        
        print(f"⏱️ Second extraction: {second_time:.2f} seconds")
        print(f"📊 Skills found: {len(skills_2)}")
        print(f"📊 Cache size: {len(matcher.skills_cache)}")
        
        # Analysis
        print(f"\n📊 ANALYSIS:")
        if second_time < 0.1:  # Should be nearly instant if cached
            print(f"✅ CACHING WORKING: Second extraction was {second_time:.3f}s (cached)")
        else:
            print(f"❌ CACHING FAILED: Second extraction took {second_time:.2f}s")
        
        if skills_1 == skills_2:
            print(f"✅ CONSISTENCY: Same skills extracted both times")
        else:
            print(f"❌ INCONSISTENCY: Different skills extracted")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_cache()
    sys.exit(0 if success else 1)
