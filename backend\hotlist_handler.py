import pandas as pd
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HotlistHandler:
    def __init__(self, csv_file='hotlist.csv'):
        """Initialize HotlistHandler with CSV file path"""
        # If csv_file is just a filename, look for it in the parent directory
        if not os.path.isabs(csv_file) and not os.path.exists(csv_file):
            parent_dir = os.path.dirname(os.path.dirname(__file__))
            csv_file = os.path.join(parent_dir, csv_file)
        self.csv_file = csv_file
        self.consultants = []
        self.load_consultants()

    def load_consultants(self):
        """Load consultants from CSV file"""
        try:
            if not os.path.exists(self.csv_file):
                logger.warning(f"CSV file not found: {self.csv_file}")
                return []

            # Read CSV file
            df = pd.read_csv(self.csv_file)
            logger.info(f"Loaded CSV file: {self.csv_file} with {len(df)} rows")

            # Convert DataFrame to list of dictionaries
            consultants = []
            for _, row in df.iterrows():
                consultant = {
                    'name': str(row.get('Name', '')).strip() if pd.notna(row.get('Name')) else '',
                    'skills': str(row.get('skills', '')).strip() if pd.notna(row.get('skills')) else '',
                    'experience': str(row.get('EXP', '')).strip() if pd.notna(row.get('EXP')) else '',
                    'visa': str(row.get('VISA', '')).strip() if pd.notna(row.get('VISA')) else '',
                    'visa_status': str(row.get('VISA', '')).strip() if pd.notna(row.get('VISA')) else '',  # Frontend expects visa_status
                    'location': str(row.get('Location', '')).strip() if pd.notna(row.get('Location')) else '',
                    'relocation': str(row.get('Willing to Relocate', '')).strip() if pd.notna(row.get('Willing to Relocate')) else '',
                    'availability': 'Immediate',  # Default availability
                    'rate': 'Negotiable',  # Default rate
                    'years_experience': self._extract_years_from_experience(str(row.get('EXP', '')))
                }

                # Parse skills from skills field
                if consultant['skills']:
                    # Split by comma and clean up
                    skills_list = [skill.strip() for skill in consultant['skills'].split(',') if skill.strip()]
                    consultant['skills_list'] = skills_list
                else:
                    consultant['skills_list'] = []

                # Find resume path based on name
                consultant['resume_path'] = self._find_resume_path(consultant['name'])

                # Only add consultants with names
                if consultant['name']:
                    consultants.append(consultant)

            self.consultants = consultants
            logger.info(f"Loaded {len(consultants)} consultants from CSV")
            return consultants

        except Exception as e:
            logger.error(f"Error loading consultants from CSV: {e}")
            return []

    def _find_resume_path(self, consultant_name):
        """Find resume path for a consultant based on their name"""
        if not consultant_name:
            return ''

        # Look in the resumes folder
        resumes_folder = os.path.join(os.path.dirname(__file__), 'resumes')
        if not os.path.exists(resumes_folder):
            return ''

        # Try different variations of the name
        name_variations = [
            consultant_name,
            consultant_name.replace(' ', '_'),
            consultant_name.replace(' ', ''),
            consultant_name.split()[0] if ' ' in consultant_name else consultant_name,  # First name only
        ]

        # Common file extensions
        extensions = ['.pdf', '.doc', '.docx']

        for name_var in name_variations:
            for ext in extensions:
                # Try exact match
                resume_path = os.path.join(resumes_folder, f"{name_var}{ext}")
                if os.path.exists(resume_path):
                    return resume_path

                # Try case-insensitive match
                for file in os.listdir(resumes_folder):
                    if file.lower() == f"{name_var.lower()}{ext}":
                        return os.path.join(resumes_folder, file)

                    # Try partial match
                    if name_var.lower() in file.lower() and file.lower().endswith(ext):
                        return os.path.join(resumes_folder, file)

        return ''

    def _extract_years_from_experience(self, experience_str):
        """Extract years of experience from experience string"""
        if not experience_str or pd.isna(experience_str):
            return 0

        import re
        # Look for patterns like "5 years", "5+", "5-7", "5.0", etc.
        patterns = [
            r'(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\s*\+',
            r'(\d+)\s*-\s*\d+',
            r'^(\d+)\.?\d*$',  # Match numbers with optional decimal (5, 5.0, 5.5)
            r'^(\d+)$'
        ]

        experience_str = str(experience_str).strip().lower()
        for pattern in patterns:
            match = re.search(pattern, experience_str)
            if match:
                try:
                    # Convert to float first, then to int to handle decimals
                    return int(float(match.group(1)))
                except ValueError:
                    continue

        # If no pattern matches, try to convert directly to number
        try:
            return int(float(experience_str))
        except (ValueError, TypeError):
            return 0

    def get_all_consultants(self):
        """Get all consultants"""
        return self.consultants

    def get_consultant_by_name(self, name):
        """Get consultant by name"""
        for consultant in self.consultants:
            if consultant['name'].lower() == name.lower():
                return consultant
        return None

    def get_consultants_by_technology(self, technology):
        """Get consultants by technology/skill"""
        matching_consultants = []
        technology_lower = technology.lower()

        for consultant in self.consultants:
            # Check in skills field
            if technology_lower in consultant.get('skills', '').lower():
                matching_consultants.append(consultant)
                continue

            # Check in skills list
            for skill in consultant.get('skills_list', []):
                if technology_lower in skill.lower():
                    matching_consultants.append(consultant)
                    break

        return matching_consultants

    def match_subject_to_technology(self, subject):
        """Match email subject to technologies in consultants database

        Args:
            subject (str): Email subject line

        Returns:
            list: List of matching consultant profiles with keyword_matched field added
        """
        # Always reload consultants to ensure we have the latest data from CSV
        self.load_consultants()

        matching_consultants = []

        # Skip if no subject or consultants
        if not subject or not self.consultants:
            return matching_consultants

        # Convert subject to lowercase for case-insensitive matching
        subject_lower = subject.lower()

        # Log the subject for debugging
        print(f"[INFO] Matching subject: '{subject}'")
        print(f"[DEBUG] Subject lowercase: '{subject_lower}'")

        # Extract all skills from consultants
        all_skills = set()
        consultant_skills = {}  # Map of consultant name to skills

        # First, collect all skills and build consultant_skills map
        for consultant in self.consultants:
            consultant_name = consultant.get('name', '')
            if consultant_name:
                consultant_skills[consultant_name] = []

                # Add skills from skills field
                if consultant.get('skills'):
                    skills_list = consultant.get('skills_list', [])
                    for skill in skills_list:
                        if skill and len(skill) > 1:  # Skip single character skills
                            skill_lower = skill.lower().strip()
                            all_skills.add(skill_lower)
                            consultant_skills[consultant_name].append(skill_lower)

                            # Also add base technology names (e.g., "react" from "react js")
                            if 'react' in skill_lower and skill_lower != 'react':
                                all_skills.add('react')
                                consultant_skills[consultant_name].append('react')
                            if 'java' in skill_lower and skill_lower != 'java':
                                all_skills.add('java')
                                consultant_skills[consultant_name].append('java')
                            if 'python' in skill_lower and skill_lower != 'python':
                                all_skills.add('python')
                                consultant_skills[consultant_name].append('python')
                            if 'angular' in skill_lower and skill_lower != 'angular':
                                all_skills.add('angular')
                                consultant_skills[consultant_name].append('angular')

        # Log the skills found
        print(f"[INFO] Found {len(all_skills)} unique skills across all consultants")

        # Find skills that match the subject with stricter matching
        matched_skills = []
        import re

        for skill in all_skills:
            # Skip very short skills (likely false positives)
            if len(skill) < 2:
                continue

            skill_lower = skill.lower()

            # Skip common words that could be false positives
            common_words = ['r', 'c', 'go', 'is', 'it', 'in', 'on', 'at', 'to', 'be', 'or', 'as', 'of', 'and', 'the', 'for', 'are', 'you', 'can', 'has', 'had', 'was', 'but', 'not', 'all', 'any', 'may', 'she', 'her', 'him', 'his', 'our', 'out', 'day', 'get', 'use', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'too', 'try']
            if skill_lower in common_words:
                continue

            # Check for exact word match (surrounded by word boundaries)
            word_pattern = r'\b' + re.escape(skill_lower) + r'\b'
            if re.search(word_pattern, subject_lower):
                matched_skills.append(skill)
                print(f"[INFO] Exact word match for skill: '{skill}' in subject")
                continue

            # For skills with 3+ characters, also check substring match
            if len(skill_lower) >= 3 and skill_lower in subject_lower:
                matched_skills.append(skill)
                print(f"[INFO] Substring match for skill: '{skill}' in subject")

        # If no direct matches, try partial matching for longer skills (VERY restrictive)
        if not matched_skills:
            print("[INFO] No direct skill matches, trying partial matching")
            for skill in all_skills:
                # Only consider skills with 6+ characters for partial matching (very restrictive)
                if len(skill) >= 6:
                    # Check if at least 6 characters of the skill appear consecutively in the subject
                    # AND the skill must be a technology name (not generic words like "developer")
                    if any(tech in skill.lower() for tech in ['java', 'python', 'react', 'angular', 'node', 'spring', 'aws', 'azure', 'oracle', 'sql', 'mongodb', 'docker', 'kubernetes']):
                        for i in range(len(skill) - 5):
                            if skill[i:i+6] in subject_lower:
                                matched_skills.append(skill)
                                print(f"[INFO] Partial match for skill: '{skill}' with '{skill[i:i+6]}' in subject")
                                break

        # Log the matched skills
        print(f"[INFO] Found {len(matched_skills)} matching skills: {', '.join(matched_skills)}")

        # Find consultants with matching skills
        if matched_skills:
            for consultant in self.consultants:
                consultant_name = consultant.get('name', '')
                if not consultant_name or consultant_name not in consultant_skills:
                    continue

                # Get this consultant's skills
                skills = consultant_skills[consultant_name]

                # Debug: Print consultant skills
                print(f"[DEBUG] Checking consultant '{consultant_name}' with skills: {skills}")

                # Check if any of the consultant's skills match
                found_match = False
                for skill_lower in skills:
                    if skill_lower in matched_skills:
                        # Create a copy of the consultant data with the matched keyword
                        consultant_copy = consultant.copy()
                        consultant_copy['keyword_matched'] = skill_lower

                        # Add to matching consultants list (avoid duplicates)
                        if not any(c['name'] == consultant_copy['name'] for c in matching_consultants):
                            matching_consultants.append(consultant_copy)
                            print(f"[INFO] ✅ Added consultant '{consultant_name}' with matching skill '{skill_lower}'")
                            found_match = True
                        break

                if not found_match and skills:
                    print(f"[DEBUG] ❌ Consultant '{consultant_name}' has no matching skills")

        # Log the final result
        print(f"[INFO] Found {len(matching_consultants)} matching consultants")

        return matching_consultants

    def save_consultants(self, consultants):
        """Save consultants back to CSV file"""
        try:
            # Convert consultants list to DataFrame
            df_data = []
            for consultant in consultants:
                df_data.append({
                    'Name': consultant.get('name', ''),
                    'skills': consultant.get('skills', ''),
                    'EXP': consultant.get('experience', ''),
                    'VISA': consultant.get('visa', ''),
                    'Location': consultant.get('location', ''),
                    'Willing to Relocate': consultant.get('relocation', '')
                })

            df = pd.DataFrame(df_data)

            # Save to CSV
            df.to_csv(self.csv_file, index=False)
            logger.info(f"Saved {len(consultants)} consultants to {self.csv_file}")

            # Reload consultants
            self.load_consultants()
            return True

        except Exception as e:
            logger.error(f"Error saving consultants to CSV: {e}")
            return False

    def add_consultant(self, consultant):
        """Add a new consultant"""
        self.consultants.append(consultant)
        return self.save_consultants(self.consultants)

    def update_consultant(self, name, updated_consultant):
        """Update an existing consultant"""
        for i, consultant in enumerate(self.consultants):
            if consultant['name'].lower() == name.lower():
                self.consultants[i] = updated_consultant
                return self.save_consultants(self.consultants)
        return False

    def delete_consultant(self, name):
        """Delete a consultant"""
        self.consultants = [c for c in self.consultants if c['name'].lower() != name.lower()]
        return self.save_consultants(self.consultants)
