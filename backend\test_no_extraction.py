#!/usr/bin/env python3
"""
Test that the system no longer extracts skills and uses database instead
"""

import os
import sys
import time

def test_no_extraction():
    """Test that the system uses database instead of extracting"""
    print("🚫 TESTING NO SKILL EXTRACTION")
    print("=" * 60)
    
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        print("🔧 Initializing matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Load a few consultants for testing
        consultants = [
            {
                'name': 'Laxman Gite',
                'experience': '10',
                'location': 'NJ',
                'visa_status': 'H1B',
                'relocation': 'Yes',
                'resume_path': 'resumes/Laxman_Gite.pdf'
            },
            {
                'name': 'Kondaru',
                'experience': '12',
                'location': 'IL',
                'visa_status': 'H1B',
                'relocation': 'Yes',
                'resume_path': 'resumes/Kondaru_04_Manjunath_Resume.pdf'
            },
            {
                'name': 'Chary',
                'experience': '8',
                'location': 'TX',
                'visa_status': 'H1B',
                'relocation': 'Yes',
                'resume_path': 'resumes/Chary.pdf'
            }
        ]
        
        print(f"📋 Testing with {len(consultants)} consultants")
        
        # Test job description
        job_description = """
        We are looking for a Senior .NET Developer with C# and ASP.NET Core experience.
        
        Required Skills:
        - 8+ years of .NET development experience
        - Strong experience with C# and ASP.NET Core
        - Experience with Web APIs and MVC
        - SQL Server database experience
        
        Location: Remote
        Experience Level: Senior (8+ years)
        """
        
        print(f"\n🚀 TESTING CONSULTANT FORMATTING (should use database):")
        print("=" * 60)
        
        start_time = time.time()
        
        # This should use database skills, not extract
        consultant_data = matcher._format_consultant_data_with_resume_analysis(consultants)
        
        formatting_time = time.time() - start_time
        
        print(f"⏱️ Formatting time: {formatting_time:.3f} seconds")
        print(f"📄 Data length: {len(consultant_data)} characters")
        
        # Check for extraction indicators
        extraction_indicators = [
            "extracting skills from",
            "not in cache",
            "ai processing",
            "generating content"
        ]
        
        found_extraction = False
        for indicator in extraction_indicators:
            if indicator.lower() in consultant_data.lower():
                print(f"⚠️ Found extraction indicator: '{indicator}'")
                found_extraction = True
        
        if not found_extraction:
            print(f"✅ No extraction indicators found in output")
        
        # Test full matching process
        print(f"\n🚀 TESTING FULL MATCHING PROCESS:")
        print("=" * 60)
        
        start_time = time.time()
        
        selected_consultants, email_message, ai_response = matcher.enhanced_match_consultant_to_job(
            job_description, consultants
        )
        
        matching_time = time.time() - start_time
        
        print(f"⏱️ Total matching time: {matching_time:.3f} seconds")
        
        if selected_consultants:
            print(f"✅ Found {len(selected_consultants)} matching consultant(s)")
            for consultant in selected_consultants:
                name = consultant.get('name', 'Unknown')
                confidence = consultant.get('match_confidence', 'Unknown')
                print(f"   - {name}: {confidence} confidence")
        else:
            print(f"❌ No matches found")
        
        # Performance analysis
        print(f"\n📊 PERFORMANCE ANALYSIS:")
        print("=" * 60)
        
        if formatting_time < 1.0:
            print(f"✅ FAST FORMATTING: {formatting_time:.3f}s (using database)")
        else:
            print(f"⚠️ SLOW FORMATTING: {formatting_time:.3f}s (likely extracting)")
        
        if matching_time < 15.0:  # Should be much faster without extraction
            print(f"✅ FAST MATCHING: {matching_time:.3f}s (using database)")
        else:
            print(f"⚠️ SLOW MATCHING: {matching_time:.3f}s (likely extracting)")
        
        # Overall assessment
        if formatting_time < 1.0 and matching_time < 15.0 and not found_extraction:
            print(f"\n🎉 SUCCESS: NO SKILL EXTRACTION!")
            print(f"   ✅ Fast formatting ({formatting_time:.3f}s)")
            print(f"   ✅ Fast matching ({matching_time:.3f}s)")
            print(f"   ✅ No extraction indicators found")
            print(f"   ✅ Using database skills successfully")
            return True
        else:
            print(f"\n⚠️ STILL EXTRACTING SKILLS:")
            if formatting_time >= 1.0:
                print(f"   ❌ Slow formatting ({formatting_time:.3f}s)")
            if matching_time >= 15.0:
                print(f"   ❌ Slow matching ({matching_time:.3f}s)")
            if found_extraction:
                print(f"   ❌ Extraction indicators found")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_no_extraction()
    sys.exit(0 if success else 1)
