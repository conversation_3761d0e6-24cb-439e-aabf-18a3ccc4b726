#!/usr/bin/env python3
"""
Auto Resume Application - Comprehensive Health Check
Diagnoses and fixes common issues automatically
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path

class HealthChecker:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []

    def print_header(self):
        print("🏥" + "=" * 58)
        print("🏥 AUTO RESUME APPLICATION - HEALTH CHECK")
        print("🏥" + "=" * 58)

    def check_python_version(self):
        """Check Python version compatibility"""
        print("🐍 Checking Python version...")

        version = sys.version_info
        if version.major == 3 and version.minor >= 7:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not supported")
            print("💡 Please upgrade to Python 3.7 or higher")
            self.issues_found.append("Incompatible Python version")
            return False

    def check_dependencies(self):
        """Check if all required packages are installed"""
        print("📦 Checking dependencies...")

        required_packages = [
            ('flask', 'flask'),
            ('pandas', 'pandas'),
            ('nltk', 'nltk'),
            ('tqdm', 'tqdm'),
            ('psutil', 'psutil'),
            ('flask_cors', 'flask_cors'),
            ('python-docx', 'docx'),
            ('PyPDF2', 'PyPDF2'),
            ('openpyxl', 'openpyxl')
        ]

        missing_packages = []

        for package_name, import_name in required_packages:
            try:
                __import__(import_name)
                print(f"✅ {package_name}")
            except ImportError:
                print(f"❌ {package_name} - MISSING")
                missing_packages.append(package_name)

        if missing_packages:
            self.issues_found.append(f"Missing packages: {', '.join(missing_packages)}")
            print(f"💡 Run: pip install {' '.join(missing_packages)}")
            return False

        print("✅ All dependencies are installed")
        return True

    def check_files(self):
        """Check if required files exist"""
        print("📁 Checking required files...")

        required_files = [
            'backend/app.py',
            'backend/email_handler.py',
            'backend/hotlist_handler.py',
            'backend/resume_parser.py',
            'frontend/index.html'
        ]

        missing_files = []

        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path} - MISSING")
                missing_files.append(file_path)

        if missing_files:
            self.issues_found.append(f"Missing files: {', '.join(missing_files)}")
            return False

        print("✅ All required files are present")
        return True

    def check_configuration(self):
        """Check configuration files"""
        print("⚙️  Checking configuration...")

        # Check config.json
        if not os.path.exists('config.json'):
            print("❌ config.json - MISSING")
            self.create_default_config()
        else:
            try:
                with open('config.json', 'r') as f:
                    config = json.load(f)
                print("✅ config.json - Valid")

                # Check if email is configured
                if not config.get('email'):
                    print("⚠️  Email not configured in config.json")
                    self.issues_found.append("Email not configured")

            except json.JSONDecodeError:
                print("❌ config.json - Invalid JSON")
                self.issues_found.append("Invalid config.json")

        # Check hotlist.csv
        if not os.path.exists('hotlist.csv'):
            print("❌ hotlist.csv - MISSING")
            self.create_sample_hotlist()
        else:
            print("✅ hotlist.csv - Present")

        return True

    def create_default_config(self):
        """Create default configuration file"""
        default_config = {
            "email": "",
            "password": "",
            "label": "JobRequirements",
            "hotlist_image": "",
            "recent_days": 1
        }

        with open('config.json', 'w') as f:
            json.dump(default_config, f, indent=2)

        print("🔧 Created default config.json")
        self.fixes_applied.append("Created default config.json")

    def create_sample_hotlist(self):
        """Create sample hotlist.csv"""
        sample_data = """Name,skills,EXP,VISA,Location,Willing to Relocate
John Doe,Java Developer,5,H1B,New York,Yes
Jane Smith,Python Developer,3,USC,California,No
Mike Johnson,.NET Developer,7,GC,Texas,Yes"""

        with open('hotlist.csv', 'w') as f:
            f.write(sample_data)

        print("🔧 Created sample hotlist.csv")
        self.fixes_applied.append("Created sample hotlist.csv")

    def check_backend_startup(self):
        """Test if backend can start"""
        print("🚀 Testing backend startup...")

        try:
            # Try to start backend process
            backend_dir = Path(__file__).parent / 'backend'

            process = subprocess.Popen(
                [sys.executable, 'app.py'],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a few seconds
            time.sleep(5)

            # Check if process is still running
            if process.poll() is None:
                print("✅ Backend starts successfully")

                # Try to make a request
                try:
                    response = requests.get('http://localhost:5000/api/config', timeout=10)
                    if response.status_code == 200:
                        print("✅ Backend API is responding")
                        result = True
                    else:
                        print(f"❌ Backend API returned status {response.status_code}")
                        result = False
                except requests.RequestException as e:
                    print(f"❌ Cannot connect to backend API: {e}")
                    result = False

                # Stop the process
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()

                return result
            else:
                _, stderr = process.communicate()
                print(f"❌ Backend failed to start")
                print(f"Error: {stderr}")
                self.issues_found.append("Backend startup failure")
                return False

        except Exception as e:
            print(f"❌ Error testing backend: {e}")
            self.issues_found.append(f"Backend test error: {e}")
            return False

    def check_ports(self):
        """Check if required ports are available"""
        print("🔌 Checking port availability...")

        import socket

        ports_to_check = [5000]

        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)

            try:
                result = sock.connect_ex(('localhost', port))
                if result == 0:
                    print(f"⚠️  Port {port} is already in use")
                    self.issues_found.append(f"Port {port} in use")
                else:
                    print(f"✅ Port {port} is available")
            except Exception as e:
                print(f"❌ Error checking port {port}: {e}")
            finally:
                sock.close()

        return True

    def check_disk_space(self):
        """Check available disk space"""
        print("💾 Checking disk space...")

        try:
            import shutil
            total, _, free = shutil.disk_usage('.')

            free_gb = free // (1024**3)
            total_gb = total // (1024**3)

            print(f"💾 Free space: {free_gb}GB / {total_gb}GB")

            if free_gb < 1:
                print("⚠️  Low disk space (less than 1GB free)")
                self.issues_found.append("Low disk space")
                return False

            print("✅ Sufficient disk space")
            return True

        except Exception as e:
            print(f"❌ Error checking disk space: {e}")
            return False

    def run_comprehensive_check(self):
        """Run all health checks"""
        self.print_header()

        checks = [
            ("Python Version", self.check_python_version),
            ("Dependencies", self.check_dependencies),
            ("Required Files", self.check_files),
            ("Configuration", self.check_configuration),
            ("Port Availability", self.check_ports),
            ("Disk Space", self.check_disk_space),
            ("Backend Startup", self.check_backend_startup)
        ]

        passed = 0
        total = len(checks)

        for check_name, check_func in checks:
            print(f"\n🔍 {check_name}:")
            try:
                if check_func():
                    passed += 1
            except Exception as e:
                print(f"❌ Error during {check_name}: {e}")
                self.issues_found.append(f"{check_name} error: {e}")

        # Print summary
        print("\n" + "=" * 60)
        print("📊 HEALTH CHECK SUMMARY")
        print("=" * 60)

        print(f"✅ Passed: {passed}/{total} checks")

        if self.fixes_applied:
            print(f"🔧 Fixes applied: {len(self.fixes_applied)}")
            for fix in self.fixes_applied:
                print(f"   • {fix}")

        if self.issues_found:
            print(f"❌ Issues found: {len(self.issues_found)}")
            for issue in self.issues_found:
                print(f"   • {issue}")
        else:
            print("🎉 No issues found! Application is healthy.")

        print("=" * 60)

        return len(self.issues_found) == 0

def main():
    """Main entry point"""
    checker = HealthChecker()

    try:
        is_healthy = checker.run_comprehensive_check()

        if is_healthy:
            print("🎉 Application is ready for production!")
            print("💡 Run: python start_production.py")
        else:
            print("⚠️  Please fix the issues above before running the application")

        sys.exit(0 if is_healthy else 1)

    except KeyboardInterrupt:
        print("\n🛑 Health check interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error during health check: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
