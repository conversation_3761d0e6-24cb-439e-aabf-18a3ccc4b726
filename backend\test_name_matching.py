#!/usr/bin/env python3
"""
Test name matching for database skills
"""

def test_name_matching():
    """Test the improved name matching"""
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        print("🔧 Initializing matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test cases
        test_cases = [
            ("Laxman Gite", "resumes/Laxman_Gite.pdf"),
            ("Kondaru", "resumes/Kondaru_04_Manjunath_Resume.pdf"),
            ("Chary", "resumes/Chary.pdf"),
            ("<PERSON><PERSON><PERSON>", "resumes/<PERSON><PERSON><PERSON>_<PERSON>oo<PERSON>_Dot_Net_Full_Stack_Developer.pdf")
        ]
        
        print(f"\n🧪 TESTING NAME MATCHING:")
        print("=" * 60)
        
        for consultant_name, resume_path in test_cases:
            print(f"\n👤 Testing: {consultant_name}")
            
            skills = matcher._get_consultant_skills_from_database(consultant_name, resume_path)
            
            print(f"📊 Skills found: {len(skills)}")
            if skills:
                print(f"📋 Sample skills: {', '.join(skills[:5])}{'...' if len(skills) > 5 else ''}")
                print(f"✅ SUCCESS: Found skills in database")
            else:
                print(f"❌ FAILED: No skills found")
        
        print(f"\n🎉 Name matching test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_name_matching()
