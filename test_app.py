#!/usr/bin/env python3
"""
Quick Test Suite for Auto Resume Application
Tests core functionality to ensure everything is working
"""

import requests
import sys
import os
import time

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from hotlist_handler import HotlistHandler

class QuickTest:
    def __init__(self):
        self.base_url = 'http://localhost:5000'
        self.passed = 0
        self.failed = 0
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status} {test_name}"
        if details:
            result += f" - {details}"
        
        print(result)
        
        if passed:
            self.passed += 1
        else:
            self.failed += 1
    
    def test_backend_running(self):
        """Test if backend is running"""
        print("\n🔌 TESTING BACKEND")
        print("-" * 40)
        
        try:
            response = requests.get(f"{self.base_url}/api/config", timeout=5)
            self.log_test("Backend API", response.status_code == 200, 
                         f"Status: {response.status_code}")
            
            response = requests.get(self.base_url, timeout=5)
            self.log_test("Main Page", response.status_code == 200,
                         f"Status: {response.status_code}")
            
        except Exception as e:
            self.log_test("Backend Connection", False, f"Error: {e}")
    
    def test_consultant_data(self):
        """Test consultant data loading"""
        print("\n👥 TESTING CONSULTANT DATA")
        print("-" * 40)
        
        try:
            # Test hotlist file exists
            hotlist_exists = os.path.exists('hotlist.csv')
            self.log_test("Hotlist CSV File", hotlist_exists, 
                         "hotlist.csv found" if hotlist_exists else "hotlist.csv missing")
            
            if hotlist_exists:
                # Test hotlist handler
                hotlist_handler = HotlistHandler('hotlist.csv')
                consultants = hotlist_handler.get_all_consultants()
                
                self.log_test("Load Consultants", len(consultants) > 0, 
                             f"Loaded {len(consultants)} consultants")
                
                # Test API endpoint
                response = requests.get(f"{self.base_url}/api/consultants", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    api_count = data.get('count', 0)
                    self.log_test("Consultants API", api_count > 0, 
                                 f"API returned {api_count} consultants")
                else:
                    self.log_test("Consultants API", False, 
                                 f"Status: {response.status_code}")
        
        except Exception as e:
            self.log_test("Consultant Data", False, f"Error: {e}")
    
    def test_skill_matching(self):
        """Test skill matching functionality"""
        print("\n🎯 TESTING SKILL MATCHING")
        print("-" * 40)
        
        try:
            hotlist_handler = HotlistHandler('hotlist.csv')
            
            # Test .NET matching
            net_matches = hotlist_handler.match_subject_to_technology(
                "Looking for .NET developer with C# experience"
            )
            self.log_test(".NET Skill Matching", len(net_matches) > 0, 
                         f"Found {len(net_matches)} .NET consultants")
            
            # Test Java matching
            java_matches = hotlist_handler.match_subject_to_technology(
                "Java Spring Boot developer needed"
            )
            self.log_test("Java Skill Matching", len(java_matches) > 0, 
                         f"Found {len(java_matches)} Java consultants")
            
            # Test React matching
            react_matches = hotlist_handler.match_subject_to_technology(
                "React frontend developer position"
            )
            self.log_test("React Skill Matching", len(react_matches) > 0, 
                         f"Found {len(react_matches)} React consultants")
            
            # Test no match scenario
            no_matches = hotlist_handler.match_subject_to_technology(
                "Marketing manager position available"
            )
            self.log_test("No Match Scenario", len(no_matches) == 0, 
                         f"Correctly found {len(no_matches)} matches for non-tech role")
        
        except Exception as e:
            self.log_test("Skill Matching", False, f"Error: {e}")
    
    def test_ui_tabs(self):
        """Test UI tab functionality"""
        print("\n🎨 TESTING UI FUNCTIONALITY")
        print("-" * 40)
        
        try:
            # Test main page loads
            response = requests.get(self.base_url, timeout=5)
            main_page_ok = response.status_code == 200
            self.log_test("Main Page Load", main_page_ok, 
                         f"Status: {response.status_code}")
            
            if main_page_ok:
                content = response.text
                
                # Check for tab elements
                has_tabs = all(tab in content for tab in [
                    'tab-main', 'tab-consultants', 'tab-dashboard', 'tab-skills'
                ])
                self.log_test("Tab Navigation", has_tabs, 
                             "All tab elements found" if has_tabs else "Missing tab elements")
                
                # Check for key functionality
                has_features = all(feature in content for feature in [
                    'Gmail Configuration', 'Consultant', 'Dashboard', 'Skills'
                ])
                self.log_test("Core Features", has_features, 
                             "All features present" if has_features else "Missing features")
        
        except Exception as e:
            self.log_test("UI Functionality", False, f"Error: {e}")
    
    def test_file_structure(self):
        """Test required files exist"""
        print("\n📁 TESTING FILE STRUCTURE")
        print("-" * 40)
        
        required_files = [
            'hotlist.csv',
            'config.json',
            'backend/app.py',
            'backend/hotlist_handler.py',
            'backend/email_handler.py',
            'frontend/index.html'
        ]
        
        for file_path in required_files:
            exists = os.path.exists(file_path)
            self.log_test(f"File: {file_path}", exists,
                         "Found" if exists else "Missing")
        
        # Check resumes directory
        resumes_dir = 'backend/resumes'
        if os.path.exists(resumes_dir):
            resume_count = len([f for f in os.listdir(resumes_dir) 
                              if f.endswith(('.pdf', '.docx'))])
            self.log_test("Resume Files", resume_count > 0, 
                         f"Found {resume_count} resume files")
        else:
            self.log_test("Resume Directory", False, "Directory missing")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪" + "=" * 50)
        print("🧪 AUTO RESUME SYSTEM - QUICK TEST SUITE")
        print("🧪" + "=" * 50)
        
        start_time = time.time()
        
        # Run test suites
        self.test_backend_running()
        self.test_consultant_data()
        self.test_skill_matching()
        self.test_ui_tabs()
        self.test_file_structure()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        total_tests = self.passed + self.failed
        success_rate = (self.passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if self.failed == 0:
            print("\n🎉 ALL TESTS PASSED! Application is ready to use.")
            print("\n🌐 Open http://localhost:5000 in your browser")
            print("📋 Configure Gmail settings in the Main tab")
            print("👥 View consultants in the Consultants tab")
            print("📊 Monitor activity in the Dashboard tab")
        else:
            print(f"\n⚠️  {self.failed} test(s) failed. Please check the issues above.")
        
        print("=" * 50)
        
        return self.failed == 0

def main():
    """Main test runner"""
    tester = QuickTest()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Fatal test error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
