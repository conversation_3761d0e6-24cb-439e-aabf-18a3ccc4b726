/* Dark Theme CSS */
:root {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-card: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --border-color: #334155;
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dark-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark-header {
  background-color: rgba(15, 23, 42, 0.5);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.dark-input {
  background-color: #0f172a;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.dark-input:focus {
  border-color: var(--accent-color);
  outline: none;
}

.dark-button {
  background-color: var(--accent-color);
  color: white;
  transition: all 0.2s ease;
}

.dark-button:hover {
  background-color: var(--accent-hover);
}

.dark-table {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.dark-table th {
  background-color: rgba(15, 23, 42, 0.7);
  color: var(--text-secondary);
}

.dark-table tr:nth-child(even) {
  background-color: rgba(15, 23, 42, 0.3);
}

.dark-table tr:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Technology List Styles */
.tech-list {
  list-style: none;
  padding: 0;
}

.tech-list li {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.tech-list li:last-child {
  border-bottom: none;
}

.tech-count {
  background-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
}
