#!/usr/bin/env python3
"""
Test the new brief email format
"""

def test_brief_email():
    """Test the new brief email format"""
    print("📧 TESTING NEW BRIEF EMAIL FORMAT")
    print("=" * 60)
    
    try:
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize matcher
        matcher = EnhancedAIConsultantMatcher()
        
        # Mock AI response
        ai_response = {
            "job_summary": "a Senior .NET Developer with C# and ASP.NET Core experience"
        }
        
        # Mock selected consultants
        selected_consultants = [
            {
                "name": "Laxman Gite",
                "experience": "10",
                "location": "NJ",
                "visa_status": "H1B",
                "relocation": "Yes",
                "key_matching_skills": ["C#", "ASP.NET Core", "Web API", "SQL Server", "Azure"]
            }
        ]
        
        # Generate brief email
        email = matcher._generate_professional_email(ai_response, selected_consultants)
        
        print("📧 NEW BRIEF EMAIL FORMAT:")
        print("=" * 60)
        print(email)
        print("=" * 60)
        
        # Analyze email
        lines = email.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        print(f"\n📊 EMAIL ANALYSIS:")
        print(f"📏 Total lines: {len(lines)}")
        print(f"📝 Non-empty lines: {len(non_empty_lines)}")
        print(f"📄 Character count: {len(email)}")
        print(f"📖 Word count: {len(email.split())}")
        
        # Check if it's brief
        if len(non_empty_lines) <= 12 and len(email) <= 800:
            print(f"\n✅ EMAIL IS BRIEF AND CLEAN!")
            print(f"   ✅ Short ({len(non_empty_lines)} lines)")
            print(f"   ✅ Concise ({len(email)} characters)")
            print(f"   ✅ Professional format")
        else:
            print(f"\n⚠️ EMAIL COULD BE SHORTER")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_brief_email()
