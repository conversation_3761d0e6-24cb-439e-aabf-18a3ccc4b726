#!/usr/bin/env python3
"""
Test script for the AI Consultant Matcher
"""

import os
import sys
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_matcher():
    """Test the AI matcher functionality"""
    print("🧪 Testing AI Consultant Matcher...")
    
    try:
        from ai_matcher import AIConsultantMatcher
        print("✅ AI Matcher module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import AI Matcher: {e}")
        return False
    
    # Test sample data
    sample_consultants = [
        {
            "name": "<PERSON>",
            "skills": "Java, Spring Boot, Microservices, AWS",
            "experience": "8",
            "location": "New York, NY",
            "visa": "H1B",
            "relocation": "Yes"
        },
        {
            "name": "<PERSON>",
            "skills": "React, Node.js, JavaScript, MongoDB",
            "experience": "5",
            "location": "San Francisco, CA",
            "visa": "<PERSON>",
            "relocation": "No"
        },
        {
            "name": "<PERSON>",
            "skills": "Python, Django, Machine Learning, TensorFlow",
            "experience": "6",
            "location": "Seattle, WA",
            "visa": "H1B",
            "relocation": "Yes"
        }
    ]
    
    sample_job_description = """
    We are looking for a Senior Java Developer to join our team.
    
    Requirements:
    - 5+ years of experience with Java
    - Experience with Spring Boot and microservices
    - Knowledge of AWS cloud services
    - Strong problem-solving skills
    
    This is a full-time position based in New York.
    """
    
    try:
        # Initialize AI matcher
        print("\n🤖 Initializing AI Matcher...")
        matcher = AIConsultantMatcher()
        print("✅ AI Matcher initialized successfully")
        
        # Test connection
        print("\n🔗 Testing connection to Gemini AI...")
        if matcher.test_connection():
            print("✅ Connection to Gemini AI successful")
        else:
            print("❌ Connection to Gemini AI failed")
            return False
        
        # Test consultant matching
        print("\n🎯 Testing consultant matching...")
        consultant_name, ai_message, ai_response = matcher.match_consultant_and_generate_reply(
            sample_job_description, sample_consultants
        )
        
        if consultant_name and ai_message:
            print(f"✅ AI matching successful!")
            print(f"📋 Selected Consultant: {consultant_name}")
            print(f"💬 AI Message: {ai_message[:100]}...")
            
            if ai_response:
                print(f"🎯 Match Confidence: {ai_response.get('match_confidence', 'Unknown')}")
                print(f"🔧 Key Skills: {', '.join(ai_response.get('key_matching_skills', []))}")
            
            return True
        else:
            print("❌ AI matching failed to return valid results")
            return False
            
    except Exception as e:
        print(f"❌ Error testing AI matcher: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """Test configuration setup"""
    print("\n⚙️ Testing configuration...")
    
    # Check if config.json exists
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    if not os.path.exists(config_path):
        print("❌ config.json not found")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check if Gemini API key is configured
        gemini_key = config.get('gemini_api_key', '')
        if not gemini_key or gemini_key == 'YOUR_GEMINI_API_KEY_HERE':
            print("⚠️ Gemini API key not configured in config.json")
            print("Please set your Gemini API key in config.json or as GEMINI_API_KEY environment variable")
            return False
        
        print("✅ Configuration looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def test_dependencies():
    """Test if required dependencies are installed"""
    print("\n📦 Testing dependencies...")
    
    try:
        import google.generativeai as genai
        print("✅ google-generativeai is installed")
        return True
    except ImportError:
        print("❌ google-generativeai is not installed")
        print("Please run: pip install google-generativeai")
        return False

def main():
    """Main test function"""
    print("🚀 Starting AI Matcher Tests\n")
    
    # Test dependencies
    if not test_dependencies():
        print("\n❌ Dependency test failed")
        return
    
    # Test configuration
    if not test_config():
        print("\n⚠️ Configuration test failed - some features may not work")
    
    # Test AI matcher
    if test_ai_matcher():
        print("\n🎉 All tests passed! AI Matcher is ready to use.")
    else:
        print("\n❌ AI Matcher tests failed")

if __name__ == "__main__":
    main()
