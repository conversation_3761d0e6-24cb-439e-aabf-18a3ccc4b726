import os
import json
import logging
from collections import Counter, defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Excel integration has been removed
HotlistHandler = None

class SkillRepository:
    """Repository for storing and retrieving skills extracted from resumes"""

    def __init__(self, skills_file='skills_database.json'):
        """Initialize the skill repository

        Args:
            skills_file (str): Path to the JSON file where skills will be stored
        """
        # Use absolute path for skills file
        self.skills_file = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', skills_file))
        logger.info(f"Skills database path: {self.skills_file}")

        # Excel integration has been removed
        self.hotlist_handler = None

        self.skills_data = {
            'all_skills': set(),  # Set of all unique skills
            'skill_frequency': Counter(),  # Count of each skill across all consultants
            'skill_by_consultant': defaultdict(list),  # Skills grouped by consultant
            'consultants_by_skill': defaultdict(list),  # Consultants grouped by skill
            'skill_categories': defaultdict(set),  # Skills grouped by category
            'skill_metadata': {}  # Additional metadata for each skill
        }
        self.load_skills()

    def load_skills(self):
        """Load skills from the JSON file if it exists"""
        try:
            logger.info(f"Attempting to load skills from {self.skills_file}")

            if not os.path.exists(self.skills_file):
                logger.warning(f"Skills file not found at {self.skills_file}, creating a new one")
                # Create an empty skills file
                self.save_skills()
                return

            with open(self.skills_file, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                    logger.info(f"Successfully loaded JSON data from {self.skills_file}")
                except json.JSONDecodeError as json_err:
                    logger.error(f"Invalid JSON in skills file: {json_err}")
                    # Create a backup of the corrupted file
                    backup_file = f"{self.skills_file}.bak"
                    try:
                        import shutil
                        shutil.copy2(self.skills_file, backup_file)
                        logger.info(f"Created backup of corrupted skills file at {backup_file}")
                    except Exception as backup_err:
                        logger.error(f"Failed to create backup: {backup_err}")

                    # Initialize with empty data and save a new file
                    self.save_skills()
                    return

                # Convert lists back to sets where needed
                self.skills_data['all_skills'] = set(data.get('all_skills', []))
                self.skills_data['skill_frequency'] = Counter(data.get('skill_frequency', {}))

                # Convert defaultdicts
                self.skills_data['skill_by_consultant'] = defaultdict(list)
                for consultant, skills in data.get('skill_by_consultant', {}).items():
                    self.skills_data['skill_by_consultant'][consultant] = skills

                self.skills_data['consultants_by_skill'] = defaultdict(list)
                for skill, consultants in data.get('consultants_by_skill', {}).items():
                    self.skills_data['consultants_by_skill'][skill] = consultants

                self.skills_data['skill_categories'] = defaultdict(set)
                for category, skills in data.get('skill_categories', {}).items():
                    self.skills_data['skill_categories'][category] = set(skills)

                self.skills_data['skill_metadata'] = data.get('skill_metadata', {})

                logger.info(f"Loaded {len(self.skills_data['all_skills'])} skills from {self.skills_file}")
        except Exception as e:
            logger.error(f"Error loading skills from {self.skills_file}: {e}")
            # Initialize with empty data
            self.skills_data = {
                'all_skills': set(),
                'skill_frequency': Counter(),
                'skill_by_consultant': defaultdict(list),
                'consultants_by_skill': defaultdict(list),
                'skill_categories': defaultdict(set),
                'skill_metadata': {}
            }

    def save_skills(self):
        """Save skills to the JSON file"""
        try:
            logger.info(f"Attempting to save skills to {self.skills_file}")

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.skills_file), exist_ok=True)

            # Convert sets to lists for JSON serialization
            data_to_save = {
                'all_skills': list(self.skills_data['all_skills']),
                'skill_frequency': dict(self.skills_data['skill_frequency']),
                'skill_by_consultant': dict(self.skills_data['skill_by_consultant']),
                'consultants_by_skill': dict(self.skills_data['consultants_by_skill']),
                'skill_categories': {k: list(v) for k, v in self.skills_data['skill_categories'].items()},
                'skill_metadata': self.skills_data['skill_metadata']
            }

            # Use a temporary file to avoid corruption if the process is interrupted
            temp_file = f"{self.skills_file}.tmp"
            try:
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, indent=2, ensure_ascii=False)

                # Replace the original file with the temporary file
                if os.path.exists(self.skills_file):
                    os.replace(temp_file, self.skills_file)
                else:
                    os.rename(temp_file, self.skills_file)

                logger.info(f"Successfully saved {len(self.skills_data['all_skills'])} skills to {self.skills_file}")
            except Exception as write_err:
                logger.error(f"Error writing to temporary file: {write_err}")
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                raise
        except Exception as e:
            logger.error(f"Error saving skills to {self.skills_file}: {e}")
            # Try to save to a fallback location if the main location fails
            try:
                fallback_file = os.path.join(os.path.dirname(__file__), 'skills_database_fallback.json')
                logger.warning(f"Attempting to save to fallback location: {fallback_file}")

                # Convert sets to lists for JSON serialization (in case data_to_save is not defined)
                fallback_data = {
                    'all_skills': list(self.skills_data['all_skills']),
                    'skill_frequency': dict(self.skills_data['skill_frequency']),
                    'skill_by_consultant': dict(self.skills_data['skill_by_consultant']),
                    'consultants_by_skill': dict(self.skills_data['consultants_by_skill']),
                    'skill_categories': {k: list(v) for k, v in self.skills_data['skill_categories'].items()},
                    'skill_metadata': self.skills_data['skill_metadata']
                }

                with open(fallback_file, 'w', encoding='utf-8') as f:
                    json.dump(fallback_data, f, indent=2, ensure_ascii=False)
                logger.info(f"Successfully saved to fallback location: {fallback_file}")
            except Exception as fallback_err:
                logger.error(f"Failed to save to fallback location: {fallback_err}")

    def add_skill(self, skill, consultant=None, category=None, metadata=None):
        """Add a skill to the repository

        Args:
            skill (str): Skill to add
            consultant (str, optional): Consultant who has this skill. Defaults to None.
            category (str, optional): Category for this skill. Defaults to None.
            metadata (dict, optional): Additional metadata for this skill. Defaults to None.
        """
        # Normalize skill
        skill = skill.strip()
        if not skill:
            return

        # Create a lowercase lookup map if it doesn't exist
        if not hasattr(self, '_skill_case_map'):
            self._skill_case_map = {s.lower(): s for s in self.skills_data['all_skills']}

        # Check if skill already exists using the lowercase map (much faster)
        skill_lower = skill.lower()
        if skill_lower in self._skill_case_map:
            # Use the existing case
            skill = self._skill_case_map[skill_lower]
        else:
            # Add to all_skills
            self.skills_data['all_skills'].add(skill)
            # Update the lowercase map
            self._skill_case_map[skill_lower] = skill

        # Increment frequency
        self.skills_data['skill_frequency'][skill] += 1

        # Add to consultant's skills
        if consultant:
            # Check if skill already exists for this consultant
            if skill not in self.skills_data['skill_by_consultant'][consultant]:
                self.skills_data['skill_by_consultant'][consultant].append(skill)

            # Add consultant to skill's consultants
            if consultant not in self.skills_data['consultants_by_skill'][skill]:
                self.skills_data['consultants_by_skill'][skill].append(consultant)

            # Excel integration has been removed
            pass

        # Add to category
        if category:
            self.skills_data['skill_categories'][category].add(skill)

        # Add metadata
        if metadata:
            if skill not in self.skills_data['skill_metadata']:
                self.skills_data['skill_metadata'][skill] = {}
            self.skills_data['skill_metadata'][skill].update(metadata)

        # Save changes to JSON
        self.save_skills()

        # Excel integration has been removed
        pass

    def add_consultant_skills(self, consultant_name, skills, categories=None):
        """Add skills for a consultant

        Args:
            consultant_name (str): Name of the consultant
            skills (list): List of skills to add
            categories (dict, optional): Dictionary mapping skills to categories
        """
        if not skills:
            return

        # Update all_skills set
        self.skills_data['all_skills'].update(skills)

        # Update skill frequency counter
        self.skills_data['skill_frequency'].update(skills)

        # Update skill_by_consultant
        self.skills_data['skill_by_consultant'][consultant_name] = skills

        # Update consultants_by_skill
        for skill in skills:
            if consultant_name not in self.skills_data['consultants_by_skill'][skill]:
                self.skills_data['consultants_by_skill'][skill].append(consultant_name)

        # Update skill categories if provided
        if categories:
            for category, cat_skills in categories.items():
                self.skills_data['skill_categories'][category].update(
                    skill for skill in cat_skills if skill in skills
                )

        # Excel integration has been removed
        pass

        logger.info(f"Added {len(skills)} skills for consultant {consultant_name}")

    def get_all_skills(self):
        """Get all unique skills"""
        return list(self.skills_data['all_skills'])

    def get_top_skills(self, n=10):
        """Get the top N most frequent skills"""
        return self.skills_data['skill_frequency'].most_common(n)

    def get_consultant_skills(self, consultant_name):
        """Get skills for a specific consultant"""
        return self.skills_data['skill_by_consultant'].get(consultant_name, [])

    def get_consultants_with_skill(self, skill):
        """Get all consultants who have a specific skill"""
        return self.skills_data['consultants_by_skill'].get(skill, [])

    def get_skills_by_category(self, category):
        """Get all skills in a specific category"""
        return list(self.skills_data['skill_categories'].get(category, set()))

    def get_skill_categories(self):
        """Get all skill categories"""
        return list(self.skills_data['skill_categories'].keys())

    def categorize_skills(self, categories_map):
        """Categorize skills based on a mapping

        Args:
            categories_map (dict): Dictionary mapping categories to lists of skills
        """
        for category, skills in categories_map.items():
            self.skills_data['skill_categories'][category].update(
                skill for skill in skills if skill in self.skills_data['all_skills']
            )

        logger.info(f"Categorized skills into {len(categories_map)} categories")
        self.save_skills()

    def update_consultant_skills(self, consultant_name, skills):
        """Update skills for a consultant (remove old skills, add new ones)

        Args:
            consultant_name (str): Name of the consultant
            skills (list): New list of skills
        """
        # First, remove all skills for this consultant
        self.remove_consultant_skills(consultant_name)

        # Then add the new skills
        self.add_consultant_skills(consultant_name, skills)

        logger.info(f"Updated skills for consultant {consultant_name}")

    def remove_consultant_skills(self, consultant_name):
        """Remove all skills for a consultant

        Args:
            consultant_name (str): Name of the consultant
        """
        # Get current skills for this consultant
        current_skills = self.skills_data['skill_by_consultant'].get(consultant_name, [])

        if not current_skills:
            return

        # Remove consultant from consultants_by_skill
        for skill in current_skills:
            if consultant_name in self.skills_data['consultants_by_skill'][skill]:
                self.skills_data['consultants_by_skill'][skill].remove(consultant_name)

                # If no consultants left with this skill, remove from all_skills
                if not self.skills_data['consultants_by_skill'][skill]:
                    if skill in self.skills_data['all_skills']:
                        self.skills_data['all_skills'].remove(skill)

                    # Remove from skill frequency
                    if skill in self.skills_data['skill_frequency']:
                        del self.skills_data['skill_frequency'][skill]

                    # Remove from consultants_by_skill
                    del self.skills_data['consultants_by_skill'][skill]

                    # Remove from categories
                    for category in self.skills_data['skill_categories']:
                        if skill in self.skills_data['skill_categories'][category]:
                            self.skills_data['skill_categories'][category].remove(skill)

        # Remove from skill_by_consultant
        if consultant_name in self.skills_data['skill_by_consultant']:
            del self.skills_data['skill_by_consultant'][consultant_name]

        # Excel integration has been removed
        pass

        logger.info(f"Removed all skills for consultant {consultant_name}")
