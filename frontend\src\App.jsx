import { useState, useEffect } from 'react';
import axios from 'axios';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './darkTheme.css';

function App() {
  const [config, setConfig] = useState({
    email: '',
    password: '',
    label: 'JobRequirements',
    recent_days: 1
  });
  const [consultants, setConsultants] = useState([]);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [botRunning, setBotRunning] = useState(false);


  // Load config on component mount
  useEffect(() => {
    fetchConfig();
  }, []);



  // Periodically check status
  useEffect(() => {
    const interval = setInterval(() => {
      checkStatus();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const checkStatus = async () => {
    try {
      const response = await axios.get('/api/status');
      setStatus(response.data.status);

      // Update bot running state based on status
      if (response.data.status && (
          response.data.status.includes('Processing') ||
          response.data.status.includes('Fetching') ||
          response.data.status.includes('Searching')
        )) {
        setBotRunning(true);
      } else if (response.data.status && response.data.status.includes('stopped')) {
        setBotRunning(false);
      }
    } catch (error) {
      console.error('Error checking status:', error);
    }
  };

  const fetchConfig = async () => {
    try {
      const response = await axios.get('/api/config');
      setConfig(response.data);
    } catch (error) {
      console.error('Error fetching config:', error);
      setStatus('Error loading configuration');
    }
  };

  const saveConfig = async () => {
    try {
      setLoading(true);
      setStatus('Saving configuration...');

      await axios.post('/api/config', config);

      setStatus('Configuration saved successfully');
    } catch (error) {
      console.error('Error saving config:', error);
      setStatus('Error saving configuration');
    } finally {
      setLoading(false);
    }
  };

  const parseResumes = async () => {
    try {
      setLoading(true);
      setStatus('Parsing resumes...');

      const response = await axios.post('/api/parse-resumes');

      setConsultants(response.data.consultants);
      setStatus(`Successfully parsed ${response.data.consultants.length} resumes`);
    } catch (error) {
      console.error('Error parsing resumes:', error);
      setStatus('Error parsing resumes');
    } finally {
      setLoading(false);
    }
  };

  const startBot = async () => {
    try {
      setLoading(true);
      setStatus('Starting auto-reply bot...');

      const response = await axios.post('/api/start');
      setBotRunning(true);

      setStatus(`Email processing started. Bot is now running in the background.`);
    } catch (error) {
      console.error('Error starting bot:', error);
      setStatus('Error starting auto-reply bot');
      setBotRunning(false);
    } finally {
      setLoading(false);
    }
  };

  const stopBot = async () => {
    try {
      setLoading(true);
      setStatus('Stopping auto-reply bot...');

      const response = await axios.post('/api/stop');
      setBotRunning(false);

      setStatus('Bot stopped successfully.');
      toast.info('Bot has been stopped. Email processing halted.');
    } catch (error) {
      console.error('Error stopping bot:', error);
      setStatus('Error stopping auto-reply bot');
    } finally {
      setLoading(false);
    }
  };

  const clearRepliedCache = async () => {
    try {
      setLoading(true);
      setStatus('Clearing replied emails cache...');

      const response = await axios.post('/api/clear-replied-cache');

      setStatus('Replied emails cache cleared successfully. All emails will be reprocessed on next run.');
      toast.success('Cache cleared! All emails will be processed on next run.');
    } catch (error) {
      console.error('Error clearing replied cache:', error);
      setStatus('Error clearing replied emails cache');
      toast.error('Failed to clear cache. Check console for details.');
    } finally {
      setLoading(false);
    }
  };

  const tryRequirementLabel = async () => {
    try {
      setLoading(true);
      setStatus('Updating label to "Requirement"...');

      const response = await axios.post('/api/try-requirement-label');

      // Update the local config state
      setConfig(prev => ({
        ...prev,
        label: 'Requirement'
      }));

      setStatus('Updated label to "Requirement". Try starting the bot again.');
      toast.success('Label updated to "Requirement". Try starting the bot again.');
    } catch (error) {
      console.error('Error updating label:', error);
      setStatus('Error updating label');
      toast.error('Failed to update label. Check console for details.');
    } finally {
      setLoading(false);
    }
  };



  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setConfig(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white py-4 flex flex-col">
      {/* Toast notifications container */}
      <ToastContainer position="top-right" autoClose={5000} theme="dark" />

      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-xl font-semibold">Auto Resume Reply System</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Configuration Panel */}
          <div className="dark-card p-3 md:col-span-1">
            <h2 className="text-lg font-semibold mb-3 flex items-center">
              <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Configuration
            </h2>

            <div className="grid grid-cols-1 gap-2">
              <div className="mb-2">
                <label className="block text-gray-300 text-xs font-medium mb-1" htmlFor="email">
                  Gmail Email
                </label>
                <input
                  className="dark-input shadow appearance-none border rounded w-full py-1 px-2 text-sm leading-tight focus:outline-none focus:shadow-outline"
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={config.email}
                  onChange={handleInputChange}
                />
              </div>

              <div className="mb-2">
                <label className="block text-gray-300 text-xs font-medium mb-1" htmlFor="password">
                  App Password
                </label>
                <input
                  className="dark-input shadow appearance-none border rounded w-full py-1 px-2 text-sm leading-tight focus:outline-none focus:shadow-outline"
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Gmail App Password"
                  value={config.password}
                  onChange={handleInputChange}
                />
                <p className="text-xs text-gray-400 mt-1">
                  Create an app password in Google Account settings
                </p>
              </div>

              <div className="mb-2">
                <label className="block text-gray-300 text-xs font-medium mb-1" htmlFor="label">
                  Gmail Label
                </label>
                <div className="flex">
                  <input
                    className="dark-input shadow appearance-none border rounded-l w-full py-1 px-2 text-sm leading-tight focus:outline-none focus:shadow-outline"
                    id="label"
                    name="label"
                    type="text"
                    placeholder="JobRequirements"
                    value={config.label}
                    onChange={handleInputChange}
                  />
                  <button
                    className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-r focus:outline-none focus:shadow-outline flex items-center"
                    type="button"
                    onClick={tryRequirementLabel}
                    disabled={loading}
                    title="Try using 'Requirement' label instead of 'Requirment' (fixes common typo)"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="mb-2">
                <label className="block text-gray-300 text-xs font-medium mb-1" htmlFor="recent_days">
                  Process Emails From Last X Days
                </label>
                <input
                  className="dark-input shadow appearance-none border rounded w-full py-1 px-2 text-sm leading-tight focus:outline-none focus:shadow-outline"
                  id="recent_days"
                  name="recent_days"
                  type="number"
                  min="1"
                  max="30"
                  placeholder="1"
                  value={config.recent_days || 1}
                  onChange={handleInputChange}
                />
                <p className="text-xs text-gray-400 mt-1">
                  Only process emails from the last X days (1-30)
                </p>
              </div>
            </div>

            <div className="mt-3 grid grid-cols-2 gap-2">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded focus:outline-none focus:shadow-outline flex items-center justify-center text-xs"
                type="button"
                onClick={saveConfig}
                disabled={loading}
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                Save
              </button>

              <button
                className="bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-2 rounded focus:outline-none focus:shadow-outline flex items-center justify-center text-xs"
                type="button"
                onClick={parseResumes}
                disabled={loading}
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Parse
              </button>
            </div>

            <div className="mt-2 grid grid-cols-1 gap-2">
              {!botRunning ? (
                <button
                  className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-3 rounded focus:outline-none focus:shadow-outline flex items-center justify-center text-sm"
                  type="button"
                  onClick={startBot}
                  disabled={loading}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Start Bot
                </button>
              ) : (
                <button
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-3 rounded focus:outline-none focus:shadow-outline flex items-center justify-center text-sm animate-pulse"
                  type="button"
                  onClick={stopBot}
                  disabled={loading}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                  </svg>
                  Stop Bot
                </button>
              )}

              <button
                className="bg-orange-600 hover:bg-orange-700 text-white font-bold py-1 px-2 rounded focus:outline-none focus:shadow-outline flex items-center justify-center text-xs"
                type="button"
                onClick={clearRepliedCache}
                disabled={loading || botRunning}
                title="Clear the cache of already replied emails to force reprocessing all emails"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Clear Cache
              </button>
            </div>

            <div className="mt-3 dark-card p-2">
              <h3 className="text-sm font-medium mb-1 flex items-center text-gray-300">
                <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Status
              </h3>
              <div className={`p-2 rounded-md ${botRunning ? 'bg-blue-900 bg-opacity-50 border border-blue-700' : status ? 'bg-blue-900 bg-opacity-30 border border-blue-800' : 'bg-gray-800'}`}>
                <p className="text-xs flex items-center text-gray-300">
                  {botRunning ? (
                    <>
                      <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                      <span className="animate-pulse">{status || 'Bot is running...'}</span>
                    </>
                  ) : status ? (
                    <>
                      <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                      {status}
                    </>
                  ) : (
                    <>
                      <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mr-1"></span>
                      Waiting for action...
                    </>
                  )}
                </p>
                {botRunning && (
                  <div className="mt-1 w-full bg-gray-700 rounded-full h-1.5">
                    <div className="bg-blue-500 h-1.5 rounded-full animate-pulse" style={{ width: '100%' }}></div>
                  </div>
                )}
              </div>
            </div>

          </div>
        </div>

        {/* Status Panel */}
        <div className="dark-card p-3 md:col-span-2">
          <h2 className="text-lg font-semibold mb-3 flex items-center text-gray-200">
            <svg className="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Status
          </h2>

          <div className="bg-gray-800 p-3 rounded text-xs text-gray-300 h-40 overflow-y-auto mb-4">
            {status ? (
              <div dangerouslySetInnerHTML={{ __html: status }} />
            ) : (
              <p className="text-gray-500">No status updates yet</p>
            )}
          </div>

          {consultants.length > 0 && (
            <div className="mt-3 dark-card">
              <div className="p-2 border-b border-gray-700">
                <h3 className="text-sm font-medium flex items-center text-gray-300">
                  <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Consultants <span className="ml-1 px-1.5 py-0.5 bg-blue-900 text-blue-300 rounded-full text-xs">{consultants.length}</span>
                </h3>
              </div>
              <div className="max-h-40 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-700 dark-table">
                  <thead className="bg-gray-800 sticky top-0">
                    <tr>
                      <th className="px-2 py-1 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Name</th>
                      <th className="px-2 py-1 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Skills</th>
                    </tr>
                  </thead>
                  <tbody>
                    {consultants.map((consultant, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-gray-900' : 'bg-gray-800'}>
                        <td className="px-2 py-1 whitespace-nowrap text-xs font-medium text-gray-300">{consultant.name}</td>
                        <td className="px-2 py-1 text-xs text-gray-400">
                          <div className="flex flex-wrap gap-1">
                            {consultant.skills.slice(0, 3).map((skill, idx) => (
                              <span key={idx} className="px-1.5 py-0.5 bg-blue-900 text-blue-300 rounded-full text-xs">
                                {skill}
                              </span>
                            ))}
                            {consultant.skills.length > 3 && (
                              <span className="px-1.5 py-0.5 bg-gray-700 text-gray-300 rounded-full text-xs">
                                +{consultant.skills.length - 3}
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
