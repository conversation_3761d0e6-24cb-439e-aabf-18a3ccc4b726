import os
import json
import logging
import google.generativeai as genai
from typing import List, Dict, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIConsultantMatcher:
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the AI Consultant Matcher with Gemini Pro

        Args:
            api_key: Gemini API key. If None, will try to load from environment or config
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("Gemini API key not found. Please set GEMINI_API_KEY environment variable or add to config.json")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        logger.info("AI Consultant Matcher initialized with Gemini Pro")

    def _load_api_key(self) -> Optional[str]:
        """Load API key from environment variable or config file"""
        # Try environment variable first
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            logger.info("Loaded Gemini API key from environment variable")
            return api_key

        # Try config.json file
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    api_key = config.get('gemini_api_key')
                    if api_key:
                        logger.info("Loaded Gemini API key from config.json")
                        return api_key
        except Exception as e:
            logger.warning(f"Error loading config.json: {e}")

        logger.warning("Gemini API key not found in environment or config")
        return None

    def _format_consultant_data(self, consultants: List[Dict]) -> str:
        """Format consultant data for the AI prompt"""
        if not consultants:
            return "No consultants available."

        formatted_consultants = []
        for i, consultant in enumerate(consultants, 1):
            name = consultant.get('name', 'Unknown')
            skills = consultant.get('skills', '')
            experience = consultant.get('experience', consultant.get('years_experience', ''))
            location = consultant.get('location', '')
            visa = consultant.get('visa', consultant.get('visa_status', ''))
            relocation = consultant.get('relocation', '')

            consultant_info = f"""
Consultant #{i}:
- Name: {name}
- Skills/Technologies: {skills}
- Experience: {experience} years
- Location: {location}
- Visa Status: {visa}
- Willing to Relocate: {relocation}
"""
            formatted_consultants.append(consultant_info.strip())

        return "\n\n".join(formatted_consultants)

    def _create_matching_prompt(self, job_description: str, consultant_data: str) -> str:
        """Create the prompt for Gemini to match consultants and generate response"""
        prompt = f"""
You are an expert technical recruiter assistant. Your task is to:
1. Analyze the job description to understand the key requirements
2. Match the BEST consultant from the list based on skills, experience, and other factors
3. Generate a professional, personalized email response explaining why this consultant is an excellent fit

Job Description:
{job_description}

Available Consultants:
{consultant_data}

Instructions:
- Choose the top which fit for this positions and best consultant who matches the job requirements most closely
- Consider technical skills, experience level, location preferences, and visa status
- If multiple consultants are equally good, prefer the one with more relevant experience
- Generate a professional email response that highlights the consultant's strengths and relevance to the role

Please respond in the following JSON format:
{{
    "selected_consultant_name": "Name of the best matching consultant",
    "match_confidence": "High/Medium/Low",
    "key_matching_skills": ["skill1", "skill2", "skill3"],
    "email_message": "Professional email message explaining why this consultant is a great fit for the role. Keep it concise but compelling, highlighting specific skills and experience that match the job requirements."
}}

Important:
- Only return valid JSON
- The email_message should be professional and ready to send to a client
- Focus on the consultant's relevant skills and experience
- Keep the message between 100-200 words
- Do not include salutations or signatures in the email_message
"""
        return prompt

    def match_consultant_and_generate_reply(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """
        Use Gemini AI to match the best consultant and generate a custom reply message

        Args:
            job_description: The job description from the email
            consultants: List of consultant dictionaries

        Returns:
            Tuple of (consultant_name, custom_message, full_ai_response)
        """
        try:
            if not consultants:
                logger.warning("No consultants provided for matching")
                return None, None, None

            # Format consultant data
            consultant_data = self._format_consultant_data(consultants)

            # Create prompt
            prompt = self._create_matching_prompt(job_description, consultant_data)

            logger.info(f"Sending request to Gemini AI for {len(consultants)} consultants")

            # Generate response using Gemini
            response = self.model.generate_content(prompt)

            if not response or not response.text:
                logger.error("Empty response from Gemini AI")
                return None, None, None

            # Parse JSON response
            try:
                # Clean the response text - remove markdown code blocks if present
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]  # Remove ```json
                if response_text.endswith('```'):
                    response_text = response_text[:-3]  # Remove ```
                response_text = response_text.strip()

                ai_response = json.loads(response_text)

                consultant_name = ai_response.get('selected_consultant_name')
                email_message = ai_response.get('email_message')
                match_confidence = ai_response.get('match_confidence', 'Unknown')
                key_skills = ai_response.get('key_matching_skills', [])

                logger.info(f"AI selected consultant: {consultant_name} with {match_confidence} confidence")
                logger.info(f"Key matching skills: {', '.join(key_skills)}")

                return consultant_name, email_message, ai_response

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI response as JSON: {e}")
                logger.error(f"Raw response: {response.text}")

                # Fallback: try to extract consultant name and create basic message
                return self._fallback_parsing(response.text, consultants)

        except Exception as e:
            logger.error(f"Error in AI consultant matching: {e}")
            return None, None, None

    def _fallback_parsing(self, response_text: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """Fallback parsing when JSON parsing fails"""
        try:
            # Try to find consultant name in the response
            for consultant in consultants:
                name = consultant.get('name', '')
                if name and name.lower() in response_text.lower():
                    # Create a basic message
                    skills = consultant.get('skills', '')
                    experience = consultant.get('experience', consultant.get('years_experience', ''))

                    basic_message = f"I'd like to recommend {name} for this position. They have {experience} years of experience with {skills}, which aligns well with your requirements. They are available immediately and would be a great fit for your team."

                    fallback_response = {
                        "selected_consultant_name": name,
                        "match_confidence": "Medium",
                        "key_matching_skills": skills.split(',')[:3] if skills else [],
                        "email_message": basic_message
                    }

                    logger.info(f"Fallback parsing selected: {name}")
                    return name, basic_message, fallback_response

            logger.warning("Fallback parsing failed to find consultant name")
            return None, None, None

        except Exception as e:
            logger.error(f"Error in fallback parsing: {e}")
            return None, None, None

    def test_connection(self) -> bool:
        """Test the connection to Gemini AI"""
        try:
            test_prompt = "Hello, please respond with 'Connection successful'"
            response = self.model.generate_content(test_prompt)

            if response and response.text:
                logger.info("Gemini AI connection test successful")
                return True
            else:
                logger.error("Gemini AI connection test failed - empty response")
                return False

        except Exception as e:
            logger.error(f"Gemini AI connection test failed: {e}")
            return False

# Example usage and testing
if __name__ == "__main__":
    # Test the AI matcher
    try:
        matcher = AIConsultantMatcher()

        # Test connection
        if matcher.test_connection():
            print("✅ AI Matcher initialized successfully")
        else:
            print("❌ AI Matcher connection failed")

    except Exception as e:
        print(f"❌ Error initializing AI Matcher: {e}")
