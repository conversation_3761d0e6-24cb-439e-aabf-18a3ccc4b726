#!/usr/bin/env python3
"""
Test Professional Email Formatting
Verifies that the new email generation produces clean, professional emails.
"""

import os
import sys
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_professional_email_formatting():
    """Test the new professional email formatting"""
    print("📧 TESTING PROFESSIONAL EMAIL FORMATTING")
    print("=" * 80)
    
    try:
        # Import the enhanced matcher
        from enhanced_ai_matcher import EnhancedAIConsultantMatcher
        
        # Initialize components
        print("🔧 Initializing Enhanced AI Matcher...")
        matcher = EnhancedAIConsultantMatcher()
        
        # Test connection
        if not matcher.test_connection():
            print("❌ AI connection failed")
            return False
        
        print("✅ AI connection successful")
        
        # Load consultants
        consultants = load_test_consultants()
        if not consultants:
            print("❌ No test consultants loaded")
            return False
        
        print(f"📋 Loaded {len(consultants)} test consultants")
        
        # Test with a DevOps job description
        test_job_description = """
        Subject: DevOps Engineer - Azure DevOps & OpenShift Specialist
        
        We are looking for a Senior DevOps Engineer with expertise in Azure DevOps and OpenShift.
        
        Required Skills:
        - 8+ years of DevOps experience
        - Strong experience with Azure DevOps, Azure Cloud Platform
        - OpenShift container orchestration experience
        - CI/CD pipeline development and management
        - Infrastructure as Code (Terraform, ARM templates)
        - Kubernetes and Docker containerization
        - Scripting (PowerShell, Bash, Python)
        
        Location: Southfield, MI (Hybrid - 3 days onsite)
        Experience Level: Senior (8+ years)
        Visa: H1B acceptable
        Start Date: Immediate
        """
        
        print(f"\n🔍 PROFESSIONAL EMAIL TEST:")
        print("="*80)
        
        print(f"🚀 Testing with DevOps job description...")
        
        selected_consultants, email_message, ai_response = matcher.enhanced_match_consultant_to_job(
            test_job_description, consultants
        )
        
        if selected_consultants:
            print(f"✅ Found {len(selected_consultants)} matching consultant(s)")
            for consultant in selected_consultants:
                name = consultant.get('name', 'Unknown')
                confidence = consultant.get('match_confidence', 'Unknown')
                skill_match = consultant.get('skill_match_percentage', 0)
                print(f"   - {name}: {confidence} confidence, {skill_match}% skill match")
        else:
            print(f"❌ No matches found")
        
        print(f"\n📧 GENERATED EMAIL:")
        print("="*80)
        print(email_message)
        print("="*80)
        
        # Analyze email quality
        print(f"\n📊 EMAIL QUALITY ANALYSIS:")
        print("="*80)
        
        email_lines = email_message.split('\n')
        
        # Check for professional elements
        checks = {
            "Has opening": email_message.startswith("Thank you for your job requirement"),
            "Has job summary": "Based on" in email_message,
            "Has recommendation section": "**Recommendation" in email_message,
            "Has consultant details": "**Skills/Technologies:**" in email_message,
            "Has match confidence": "Match Confidence:" in email_message,
            "Has closing": "Best regards" in email_message,
            "Has signature": "Mukesh Saini" in email_message,
            "Proper formatting": "**" in email_message and "•" in email_message,
            "Multiple consultants handled": len(selected_consultants) > 1 and "Consultant #" in email_message if selected_consultants else True
        }
        
        passed_checks = sum(checks.values())
        total_checks = len(checks)
        
        print(f"Professional Elements Check: {passed_checks}/{total_checks}")
        for check, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check}")
        
        # Check email length
        word_count = len(email_message.split())
        print(f"\nEmail Statistics:")
        print(f"  📝 Word count: {word_count}")
        print(f"  📏 Character count: {len(email_message)}")
        print(f"  📄 Line count: {len(email_lines)}")
        
        if word_count > 50 and word_count < 400:
            print(f"  ✅ Good length (50-400 words)")
        else:
            print(f"  ⚠️ Length concern (too short or too long)")
        
        # Overall assessment
        if passed_checks >= total_checks * 0.8:  # 80% pass rate
            print(f"\n🎉 EMAIL QUALITY: EXCELLENT ({passed_checks}/{total_checks} checks passed)")
        elif passed_checks >= total_checks * 0.6:  # 60% pass rate
            print(f"\n👍 EMAIL QUALITY: GOOD ({passed_checks}/{total_checks} checks passed)")
        else:
            print(f"\n⚠️ EMAIL QUALITY: NEEDS IMPROVEMENT ({passed_checks}/{total_checks} checks passed)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in professional email test: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_test_consultants():
    """Load consultant data from CSV"""
    consultants = []
    
    # Read the CSV file
    csv_path = os.path.join('..', 'hotlist.csv')
    if not os.path.exists(csv_path):
        print("❌ hotlist.csv not found")
        return []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Skip header and take first 15 consultants for testing
    for line in lines[1:16]:  # Test with first 15 consultants
        parts = [p.strip().strip('"') for p in line.split(',')]
        if len(parts) >= 6:
            name = parts[0]
            skills = parts[1]
            experience = parts[2]
            visa = parts[3]
            location = parts[4]
            relocation = parts[5]
            
            # Try to find matching resume file
            resume_path = find_resume_file(name)
            
            consultant = {
                'name': name,
                'skills': skills,
                'experience': experience,
                'visa_status': visa,
                'location': location,
                'relocation': relocation,
                'resume_path': resume_path
            }
            consultants.append(consultant)
    
    return consultants

def find_resume_file(consultant_name):
    """Find resume file for a consultant"""
    resumes_dir = 'resumes'
    if not os.path.exists(resumes_dir):
        return None
    
    # Clean the consultant name for matching
    clean_name = consultant_name.lower().replace(' ', '').replace('.', '')
    
    # List all resume files
    resume_files = os.listdir(resumes_dir)
    
    # Try to find a matching file
    for filename in resume_files:
        clean_filename = filename.lower().replace(' ', '').replace('.', '').replace('_', '').replace('-', '')
        
        # Check if consultant name is in filename
        if clean_name in clean_filename or any(part in clean_filename for part in clean_name.split() if len(part) > 2):
            return os.path.join(resumes_dir, filename)
    
    return None

if __name__ == "__main__":
    success = test_professional_email_formatting()
    sys.exit(0 if success else 1)
