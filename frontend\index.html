<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Auto Resume - Match job requirements with consultant profiles and send automated responses">
    <title>Auto Resume - Job Requirement Matcher</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            primary: '#1e293b',
                            secondary: '#0f172a',
                            accent: '#3b82f6',
                            accent2: '#8b5cf6', /* Purple accent */
                            accent3: '#10b981', /* Green accent */
                            text: '#f1f5f9',
                            muted: '#94a3b8'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Professional Dark theme styles */
        .dark body {
            background-color: #0f172a;
            color: #f1f5f9;
        }

        .dark .bg-white {
            background-color: #1e293b;
        }

        .dark .bg-gray-50, .dark .bg-gray-100 {
            background-color: #334155;
        }

        .dark .text-gray-700, .dark .text-gray-800, .dark .text-gray-900 {
            color: #f1f5f9;
        }

        .dark .text-gray-500, .dark .text-gray-600 {
            color: #cbd5e1;
        }

        .dark .border, .dark .divide-gray-200 {
            border-color: #475569;
        }

        .dark .shadow, .dark .shadow-lg {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }

        .dark input, .dark select, .dark textarea {
            background-color: #334155;
            color: #f1f5f9;
            border-color: #475569;
        }

        .dark input::placeholder, .dark textarea::placeholder {
            color: #94a3b8;
        }

        .dark .divide-y {
            border-color: #475569;
        }

        /* Enhanced UI elements */
        .dark .bg-blue-500 {
            background-color: #3b82f6;
        }

        .dark .bg-blue-500:hover {
            background-color: #2563eb;
        }

        .dark .bg-green-500 {
            background-color: #10b981;
        }

        .dark .bg-green-500:hover {
            background-color: #059669;
        }

        .dark .bg-purple-500 {
            background-color: #8b5cf6;
        }

        .dark .bg-purple-500:hover {
            background-color: #7c3aed;
        }

        .dark .bg-red-500 {
            background-color: #ef4444;
        }

        .dark .bg-red-500:hover {
            background-color: #dc2626;
        }

        /* Table styling */
        .dark table th {
            background-color: #1e293b;
            color: #f1f5f9;
            border-color: #475569;
        }

        .dark table td {
            border-color: #475569;
        }

        .dark tr:hover {
            background-color: #1e293b !important;
        }

        /* Card styling */
        .dark .card {
            background-color: #1e293b;
            border-color: #475569;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }

        /* Gradient accents */
        .dark .gradient-border {
            position: relative;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .dark .gradient-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, #3b82f6, #8b5cf6, #10b981);
        }

        /* Accessibility improvements */
        :focus {
            outline: 3px solid #3b82f6;
            outline-offset: 2px;
        }

        .skip-link {
            position: absolute;
            top: -40px;
            left: 0;
            background: #3b82f6;
            color: white;
            padding: 8px;
            z-index: 100;
            transition: top 0.3s;
        }

        .skip-link:focus {
            top: 0;
        }

        /* High contrast mode */
        .high-contrast {
            filter: contrast(1.5);
        }

        /* Increase font size */
        .large-text {
            font-size: 1.2em;
        }

        /* Reduce motion */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.001ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.001ms !important;
            }
        }

        /* Accessibility toolbar */
        .accessibility-toolbar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #1e293b;
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 50;
            display: flex;
            gap: 8px;
        }

        .accessibility-button {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #334155;
            color: white;
            cursor: pointer;
            transition: background 0.2s;
        }

        .accessibility-button:hover {
            background: #475569;
        }

        .accessibility-button.active {
            background: #3b82f6;
        }

        /* Accessibility styles */
        body.high-contrast {
            filter: contrast(1.5);
        }

        body.large-text {
            font-size: 120%;
        }

        body.reduce-motion * {
            transition: none !important;
            animation: none !important;
        }

        /* Tab functionality styles */
        .tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.hidden {
            display: none;
        }

        .tab-button.active {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Smooth transitions for tab buttons */
        .tab-button {
            transition: all 0.2s ease-in-out;
        }

        .tab-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-slate-900 min-h-screen h-screen flex flex-col overflow-hidden">
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header Bar -->
    <header class="bg-white dark:bg-slate-800 shadow-md py-3 px-6">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Auto Resume Reply System</h1>

            <!-- Navigation Tabs -->
            <nav class="flex space-x-1">
                <button id="tab-main" class="tab-button active bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg text-sm flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 7 4-4 4 4"></path>
                    </svg>
                    Main
                </button>
                <button id="tab-consultants" class="tab-button bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg text-sm flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Consultants
                </button>
                <button id="tab-dashboard" class="tab-button bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg text-sm flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Dashboard
                </button>
                <button id="tab-skills" class="tab-button bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg text-sm flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Skills
                </button>
            </nav>
        </div>
    </header>

    <div class="flex-1 overflow-auto p-4" id="main-content">
        <div class="max-w-7xl mx-auto">

            <!-- Tab Content Container -->
            <div id="tab-content">

                <!-- Main Tab Content -->
                <div id="content-main" class="tab-content">
                    <!-- Main Layout - Two Column Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">

                <!-- Left Column - Configuration and Actions -->
                <div class="lg:col-span-1">
                    <!-- Configuration Section -->
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 mb-6 border border-gray-200 dark:border-slate-700 gradient-border">
                        <h2 class="text-xl font-semibold mb-5 text-gray-900 dark:text-white flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Configuration
                        </h2>
                        <div class="space-y-5">
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="email">
                                    Gmail Email
                                </label>
                                <input
                                    class="shadow-sm appearance-none border border-gray-300 dark:border-slate-600 rounded-md w-full py-2.5 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 transition-colors"
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                >
                            </div>
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="password">
                                    App Password
                                </label>
                                <input
                                    class="shadow-sm appearance-none border border-gray-300 dark:border-slate-600 rounded-md w-full py-2.5 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 transition-colors"
                                    id="password"
                                    type="password"
                                    placeholder="Gmail App Password"
                                >
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1.5">
                                    Create an app password in your Google Account settings
                                </p>
                            </div>
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="label">
                                    Gmail Label
                                </label>
                                <div class="flex">
                                    <select
                                        class="shadow-sm appearance-none border border-gray-300 dark:border-slate-600 rounded-l-md w-full py-2.5 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 transition-colors"
                                        id="label"
                                    >
                                        <option value="">Loading labels...</option>
                                    </select>
                                    <button
                                        id="refreshLabels"
                                        class="bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors"
                                        title="Refresh Gmail Labels"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1.5">
                                    Select the Gmail label to monitor for job requirements
                                </p>
                            </div>
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="hotlistImage">
                                    Hotlist Image
                                </label>
                                <div class="flex items-center space-x-2">
                                    <input
                                        class="block w-full text-sm text-gray-500 dark:text-gray-400
                                        file:mr-4 file:py-2.5 file:px-4
                                        file:rounded-md file:border-0
                                        file:text-sm file:font-medium
                                        file:bg-blue-50 file:text-blue-700 dark:file:bg-blue-900 dark:file:text-blue-100
                                        hover:file:bg-blue-100 dark:hover:file:bg-blue-800 transition-colors"
                                        id="hotlistImage"
                                        type="file"
                                        accept=".png,.jpg,.jpeg,.gif"
                                    >
                                    <button
                                        id="uploadHotlistImage"
                                        class="bg-green-500 hover:bg-green-600 active:bg-green-700 text-white font-medium py-2.5 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors"
                                    >
                                        Upload
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1.5">
                                    This image will be included in emails when no consultant matches are found
                                </p>
                                <div id="currentHotlistImage" class="mt-3 hidden">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-1.5">Current image:</p>
                                    <img id="hotlistImagePreview" src="" alt="Hotlist Image" class="max-h-24 border dark:border-slate-600 rounded-md">
                                </div>
                            </div>
                            <div class="mt-5">
                                <button
                                    id="saveConfig"
                                    class="w-full bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors flex justify-center items-center"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                                    </svg>
                                    Save Configuration
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Status and Statistics -->
                <div class="lg:col-span-2">
                    <!-- Status Section -->
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 mb-6 border border-gray-200 dark:border-slate-700 gradient-border">
                        <h2 class="text-xl font-semibold mb-5 text-gray-900 dark:text-white flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Status
                        </h2>
                        <div id="status" class="p-5 bg-gray-50 dark:bg-slate-700 rounded-md text-gray-800 dark:text-gray-200 min-h-[120px] border border-gray-200 dark:border-slate-600 shadow-inner">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                <span>Ready to process emails.</span>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Section -->
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 mb-6 border border-gray-200 dark:border-slate-700 gradient-border">
                        <h2 class="text-xl font-semibold mb-5 text-gray-900 dark:text-white flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                            </svg>
                            Actions
                        </h2>
                        <div class="grid grid-cols-2 gap-4">
                            <button
                                id="parseResumes"
                                class="bg-green-500 hover:bg-green-600 active:bg-green-700 text-white font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors flex items-center justify-center"
                            >
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Parse Resumes
                            </button>

                            <button
                                id="uploadResume"
                                class="bg-teal-500 hover:bg-teal-600 active:bg-teal-700 text-white font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors flex items-center justify-center"
                            >
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                Upload Resume
                            </button>
                            <div></div>
                            <button
                                id="startBot"
                                class="bg-purple-500 hover:bg-purple-600 active:bg-purple-700 text-white font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors col-span-2 flex items-center justify-center"
                            >
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Start Bot
                            </button>
                            <button
                                id="stopBot"
                                class="bg-red-500 hover:bg-red-600 active:bg-red-700 text-white font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors col-span-2 hidden flex items-center justify-center"
                            >
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"></path>
                                </svg>
                                Stop Bot
                            </button>
                        </div>
                    </div>

                    <!-- Bot Logs Section -->
                    <div id="botLogsContainer" class="mt-4 hidden">
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Bot Logs
                        </h3>
                        <div id="botLogs" class="bg-gray-900 text-gray-200 p-4 rounded-lg h-56 overflow-y-auto font-mono text-sm border border-gray-700 shadow-inner">
                            <!-- Logs will be added here -->
                            <div class="text-gray-400">Waiting for bot activity...</div>
                        </div>
                    </div>
                </div>





                <!-- Resume Upload Modal -->
                <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg w-full max-w-md">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <h2 class="text-xl font-bold text-gray-800 dark:text-white">Upload Resume</h2>
                                <button id="closeUploadModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <form id="resumeUploadForm" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="consultantName">
                                        Consultant Name
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="consultantName"
                                        name="consultant_name"
                                        type="text"
                                        placeholder="John Doe"
                                        required
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="resumeFile">
                                        Resume File (PDF or DOCX)
                                    </label>
                                    <input
                                        class="block w-full text-sm text-gray-500 dark:text-gray-400
                                        file:mr-4 file:py-2 file:px-4
                                        file:rounded file:border-0
                                        file:text-sm file:font-semibold
                                        file:bg-blue-50 file:text-blue-700 dark:file:bg-blue-900 dark:file:text-blue-100
                                        hover:file:bg-blue-100 dark:hover:file:bg-blue-800"
                                        id="resumeFile"
                                        name="file"
                                        type="file"
                                        accept=".pdf,.docx,.doc"
                                        required
                                    >
                                </div>

                                <div class="flex justify-end">
                                    <button
                                        type="submit"
                                        class="bg-teal-500 hover:bg-teal-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                    >
                                        Upload
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Edit Consultant Modal -->
                <div id="editConsultantModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg w-full max-w-md">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <h2 class="text-xl font-bold text-gray-800 dark:text-white">Edit Consultant</h2>
                                <button id="closeEditModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <form id="editConsultantForm">
                                <input type="hidden" id="editConsultantId">

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editName">
                                        Name
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editName"
                                        type="text"
                                        placeholder="John Doe"
                                        required
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editExperience">
                                        Years of Experience
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editExperience"
                                        type="number"
                                        min="0"
                                        placeholder="5"
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editLocation">
                                        Location
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editLocation"
                                        type="text"
                                        placeholder="New York, NY"
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editVisaStatus">
                                        Visa Status
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editVisaStatus"
                                        type="text"
                                        placeholder="H1B, GC, Citizen, etc."
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editAvailability">
                                        Availability
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editAvailability"
                                        type="text"
                                        placeholder="Immediate, 2 weeks, etc."
                                    >
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editRelocation">
                                        Relocation Preference
                                    </label>
                                    <select
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editRelocation"
                                    >
                                        <option value="">Select Preference</option>
                                        <option value="Yes">Yes - Willing to Relocate</option>
                                        <option value="Remote Only">Remote Only</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editSkills">
                                        Skills (comma separated)
                                    </label>
                                    <textarea
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editSkills"
                                        rows="4"
                                        placeholder="JavaScript, React, Node.js"
                                    ></textarea>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editSummary">
                                        Summary
                                    </label>
                                    <textarea
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-white leading-tight focus:outline-none focus:shadow-outline dark:bg-slate-700 dark:border-slate-600"
                                        id="editSummary"
                                        rows="3"
                                        placeholder="Experienced software developer..."
                                    ></textarea>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="editResumeFile">
                                        Upload New Resume (Optional)
                                    </label>
                                    <input
                                        class="block w-full text-sm text-gray-500 dark:text-gray-400
                                        file:mr-4 file:py-2 file:px-4
                                        file:rounded file:border-0
                                        file:text-sm file:font-semibold
                                        file:bg-blue-50 file:text-blue-700 dark:file:bg-blue-900 dark:file:text-blue-100
                                        hover:file:bg-blue-100 dark:hover:file:bg-blue-800"
                                        id="editResumeFile"
                                        name="file"
                                        type="file"
                                        accept=".pdf,.docx,.doc"
                                    >
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Leave empty to keep the current resume
                                    </p>
                                </div>

                                <div class="mb-4">
                                    <div id="currentResumePath" class="text-sm text-gray-600 dark:text-gray-400"></div>
                                </div>

                                <div class="flex justify-between">
                                    <button
                                        type="button"
                                        id="deleteConsultant"
                                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                    >
                                        Delete
                                    </button>
                                    <button
                                        type="submit"
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                    >
                                        Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <div id="status" class="hidden p-4 mb-6 bg-gray-100 rounded-lg"></div>

            <div id="consultantsContainer" class="hidden">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-5 space-y-3 md:space-y-0">
                    <div class="flex items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Parsed Consultants
                        </h2>
                        <button
                            id="refreshConsultants"
                            class="ml-3 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900 dark:hover:bg-indigo-800 text-indigo-700 dark:text-indigo-300 font-medium py-1 px-3 rounded-full text-sm flex items-center"
                            title="Refresh consultant data from JSON"
                        >
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>

                        <button
                            id="importHotlist"
                            class="ml-2 bg-purple-100 hover:bg-purple-200 dark:bg-purple-900 dark:hover:bg-purple-800 text-purple-700 dark:text-purple-300 font-medium py-1 px-3 rounded-full text-sm flex items-center"
                            title="Import consultant data from hotlist.csv"
                        >
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            Import Hotlist
                        </button>
                    </div>
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3 w-full md:w-auto">
                        <div class="relative flex-grow md:flex-grow-0 md:w-64">
                            <input
                                id="searchConsultants"
                                type="text"
                                placeholder="Search consultants..."
                                class="w-full px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 dark:text-white transition-colors pl-10"
                            >
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <select
                            id="sortConsultants"
                            class="px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 dark:text-white transition-colors"
                        >
                            <option value="name">Sort by Name</option>
                            <option value="skills">Sort by Skill Count</option>
                            <option value="experience">Sort by Experience</option>
                        </select>
                    </div>
                </div>



                <!-- Consultants Table -->
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700">
                    <div class="max-h-[500px] overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed">
                            <thead class="bg-gray-50 dark:bg-slate-700 sticky top-0 z-10">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/6">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Experience</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Visa Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Availability</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Relocation</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/3">Skills</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="consultantsList" class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- Consultants will be added here -->
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                        <div class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Loading consultants...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-slate-700 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Showing <span id="visibleConsultants" class="font-medium mx-1">0</span> of <span id="totalConsultantsCount" class="font-medium mx-1">0</span> consultants
                    </div>
                </div>

                <!-- Consultant Detail Modal -->
                <div id="consultantModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto gradient-border">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <h2 id="modalConsultantName" class="text-2xl font-bold text-gray-800"></h2>
                                <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <!-- Left Column -->
                                <div class="col-span-2">
                                    <div class="mb-6">
                                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Summary</h3>
                                        <p id="modalConsultantSummary" class="text-gray-600"></p>
                                    </div>

                                    <div class="mb-6">
                                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Skills</h3>
                                        <div id="modalConsultantSkills" class="flex flex-wrap gap-2"></div>
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div>
                                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Details</h3>
                                        <ul class="space-y-2">
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Experience:</span>
                                                <span id="modalConsultantExperience" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Visa Status:</span>
                                                <span id="modalConsultantVisa" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Availability:</span>
                                                <span id="modalConsultantAvailability" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Relocation:</span>
                                                <span id="modalConsultantRelocation" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Skill Count:</span>
                                                <span id="modalConsultantSkillCount" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">File Type:</span>
                                                <span id="modalConsultantFileType" class="font-medium"></span>
                                            </li>
                                            <li class="flex justify-between">
                                                <span class="text-gray-500">Word Count:</span>
                                                <span id="modalConsultantWordCount" class="font-medium"></span>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="mt-6">
                                        <a id="modalViewResume" href="#" target="_blank" class="block w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded text-center">
                                            View Resume
                                        </a>
                                        <a id="modalDownloadResume" href="#" target="_blank" class="block w-full mt-2 bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded text-center">
                                            Download Resume
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
                <!-- End Main Tab Content -->

                <!-- Consultants Tab Content -->
                <div id="content-consultants" class="tab-content hidden">
                    <div id="consultantsContainer">
                        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-5 space-y-3 md:space-y-0">
                            <div class="flex items-center">
                                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Consultant Database
                                </h2>
                                <button
                                    id="refreshConsultants2"
                                    class="ml-3 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900 dark:hover:bg-indigo-800 text-indigo-700 dark:text-indigo-300 font-medium py-1 px-3 rounded-full text-sm flex items-center"
                                    title="Refresh consultant data from JSON"
                                >
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Refresh
                                </button>
                                <button
                                    id="importHotlist2"
                                    class="ml-2 bg-purple-100 hover:bg-purple-200 dark:bg-purple-900 dark:hover:bg-purple-800 text-purple-700 dark:text-purple-300 font-medium py-1 px-3 rounded-full text-sm flex items-center"
                                    title="Import consultant data from hotlist.csv"
                                >
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                    </svg>
                                    Import Hotlist
                                </button>
                            </div>
                            <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3 w-full md:w-auto">
                                <div class="relative flex-grow md:flex-grow-0 md:w-64">
                                    <input
                                        id="searchConsultants2"
                                        type="text"
                                        placeholder="Search consultants..."
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 dark:text-white transition-colors pl-10"
                                    >
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <select
                                    id="sortConsultants2"
                                    class="px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-slate-700 dark:text-white transition-colors"
                                >
                                    <option value="name">Sort by Name</option>
                                    <option value="skills">Sort by Skill Count</option>
                                    <option value="experience">Sort by Experience</option>
                                </select>
                            </div>
                        </div>

                        <!-- Consultants Table -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700">
                            <div class="max-h-[600px] overflow-y-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed">
                                    <thead class="bg-gray-50 dark:bg-slate-700 sticky top-0 z-10">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/6">Name</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Experience</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Visa Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Location</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Relocation</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/3">Skills</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/12">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="consultantsList2" class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <!-- Consultants will be added here -->
                                        <tr>
                                            <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                                <div class="flex items-center justify-center">
                                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Loading consultants...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-slate-700 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Showing <span id="visibleConsultants2" class="font-medium mx-1">0</span> of <span id="totalConsultantsCount2" class="font-medium mx-1">0</span> consultants
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Consultants Tab Content -->

                <!-- Dashboard Tab Content -->
                <div id="content-dashboard" class="tab-content hidden">
                    <div class="space-y-6">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                            <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Email Statistics Dashboard
                        </h2>

                        <!-- Statistics Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Emails</p>
                                        <p id="totalEmails" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Successful Replies</p>
                                        <p id="successfulReplies" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                                        <svg class="w-6 h-6 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Failed Replies</p>
                                        <p id="failedReplies" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
                                        <p id="successRate" class="text-2xl font-bold text-gray-900 dark:text-white">0%</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Email Activity</h3>
                                <div id="recentActivity" class="space-y-3">
                                    <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                                        No recent activity
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Dashboard Tab Content -->

                <!-- Skills Tab Content -->
                <div id="content-skills" class="tab-content hidden">
                    <div class="space-y-6">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                            <svg class="w-6 h-6 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            Skills Database Management
                        </h2>

                        <!-- Skills Overview -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900">
                                        <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Skills</p>
                                        <p id="totalSkills" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 012 2v2h2a2 2 0 012 2v6a2 2 0 01-2 2H9z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Categories</p>
                                        <p id="skillCategories" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-slate-700">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                                        <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Top Skills</p>
                                        <p id="topSkillsCount" class="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Skills List -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700">
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">All Skills</h3>
                                    <button id="refreshSkills" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg text-sm flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <div id="skillsList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div class="text-center text-gray-500 dark:text-gray-400 py-8 col-span-full">
                                        Loading skills...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Skills Tab Content -->

            </div>
        </div>
    </div>

    <script>
        // API base URL
        const API_BASE_URL = 'http://localhost:5000';

        // Tab functionality
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            function showTab(tabId) {
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });

                // Remove active class from all buttons
                tabButtons.forEach(button => {
                    button.classList.remove('active', 'bg-blue-500', 'text-white');
                    button.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-slate-700', 'dark:hover:bg-slate-600', 'text-gray-700', 'dark:text-gray-300');
                });

                // Show selected tab content
                const selectedContent = document.getElementById(`content-${tabId}`);
                if (selectedContent) {
                    selectedContent.classList.remove('hidden');
                }

                // Add active class to selected button
                const selectedButton = document.getElementById(`tab-${tabId}`);
                if (selectedButton) {
                    selectedButton.classList.add('active', 'bg-blue-500', 'text-white');
                    selectedButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-slate-700', 'dark:hover:bg-slate-600', 'text-gray-700', 'dark:text-gray-300');
                }

                // Load tab-specific data
                loadTabData(tabId);
            }

            function loadTabData(tabId) {
                switch(tabId) {
                    case 'consultants':
                        loadConsultants();
                        break;
                    case 'dashboard':
                        loadDashboardData();
                        break;
                    case 'skills':
                        loadSkillsData();
                        break;
                }
            }

            // Add click event listeners to tab buttons
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.id.replace('tab-', '');
                    showTab(tabId);
                });
            });

            // Show main tab by default
            showTab('main');
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await axios.get(`${API_BASE_URL}/api/statistics`);
                const stats = response.data;

                document.getElementById('totalEmails').textContent = stats.total_emails || 0;
                document.getElementById('successfulReplies').textContent = stats.successful_replies || 0;
                document.getElementById('failedReplies').textContent = stats.failed_replies || 0;

                const successRate = stats.total_emails > 0 ?
                    Math.round((stats.successful_replies / stats.total_emails) * 100) : 0;
                document.getElementById('successRate').textContent = `${successRate}%`;

                // Load recent activity
                if (stats.recent_activity) {
                    const activityContainer = document.getElementById('recentActivity');
                    activityContainer.innerHTML = '';

                    if (stats.recent_activity.length === 0) {
                        activityContainer.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">No recent activity</div>';
                    } else {
                        stats.recent_activity.forEach(activity => {
                            const activityItem = document.createElement('div');
                            activityItem.className = 'flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700 rounded-lg';
                            activityItem.innerHTML = `
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-${activity.status === 'success' ? 'green' : 'red'}-500 rounded-full mr-3"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">${activity.subject}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">${activity.timestamp}</p>
                                    </div>
                                </div>
                                <span class="text-xs px-2 py-1 rounded-full bg-${activity.status === 'success' ? 'green' : 'red'}-100 text-${activity.status === 'success' ? 'green' : 'red'}-800">
                                    ${activity.status}
                                </span>
                            `;
                            activityContainer.appendChild(activityItem);
                        });
                    }
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // Set default values on error
                document.getElementById('totalEmails').textContent = '0';
                document.getElementById('successfulReplies').textContent = '0';
                document.getElementById('failedReplies').textContent = '0';
                document.getElementById('successRate').textContent = '0%';
            }
        }

        // Load skills data
        async function loadSkillsData() {
            try {
                const response = await axios.get(`${API_BASE_URL}/api/skills`);
                const skillsData = response.data;

                document.getElementById('totalSkills').textContent = skillsData.all_skills?.length || 0;
                document.getElementById('skillCategories').textContent = Object.keys(skillsData.categories || {}).length;
                document.getElementById('topSkillsCount').textContent = skillsData.top_skills?.length || 0;

                // Display skills list
                const skillsList = document.getElementById('skillsList');
                skillsList.innerHTML = '';

                if (skillsData.all_skills && skillsData.all_skills.length > 0) {
                    skillsData.all_skills.forEach(skill => {
                        const skillItem = document.createElement('div');
                        skillItem.className = 'bg-gray-50 dark:bg-slate-700 rounded-lg p-3 flex items-center justify-between';
                        skillItem.innerHTML = `
                            <span class="text-sm font-medium text-gray-900 dark:text-white">${skill.name || skill}</span>
                            <span class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                                ${skill.count || 1}
                            </span>
                        `;
                        skillsList.appendChild(skillItem);
                    });
                } else {
                    skillsList.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8 col-span-full">No skills found</div>';
                }
            } catch (error) {
                console.error('Error loading skills data:', error);
                document.getElementById('totalSkills').textContent = '0';
                document.getElementById('skillCategories').textContent = '0';
                document.getElementById('topSkillsCount').textContent = '0';
                document.getElementById('skillsList').innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8 col-span-full">Error loading skills</div>';
            }
        }

        // DOM elements
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const labelInput = document.getElementById('label');
        const refreshLabelsBtn = document.getElementById('refreshLabels');
        const saveConfigBtn = document.getElementById('saveConfig');
        const parseResumesBtn = document.getElementById('parseResumes');
        const startBotBtn = document.getElementById('startBot');
        const stopBotBtn = document.getElementById('stopBot');
        const botLogsContainer = document.getElementById('botLogsContainer');
        const botLogs = document.getElementById('botLogs');

        const uploadResumeBtn = document.getElementById('uploadResume');
        const uploadModal = document.getElementById('uploadModal');
        const closeUploadModalBtn = document.getElementById('closeUploadModal');
        const resumeUploadForm = document.getElementById('resumeUploadForm');
        const consultantNameInput = document.getElementById('consultantName');
        const resumeFileInput = document.getElementById('resumeFile');
        const hotlistImageInput = document.getElementById('hotlistImage');
        const uploadHotlistImageBtn = document.getElementById('uploadHotlistImage');
        const currentHotlistImageDiv = document.getElementById('currentHotlistImage');
        const hotlistImagePreview = document.getElementById('hotlistImagePreview');
        const editConsultantModal = document.getElementById('editConsultantModal');
        const closeEditModalBtn = document.getElementById('closeEditModal');
        const editConsultantForm = document.getElementById('editConsultantForm');
        const editConsultantIdInput = document.getElementById('editConsultantId');
        const editNameInput = document.getElementById('editName');
        const editExperienceInput = document.getElementById('editExperience');
        const editLocationInput = document.getElementById('editLocation');
        const editVisaStatusInput = document.getElementById('editVisaStatus');
        const editAvailabilityInput = document.getElementById('editAvailability');
        const editRelocationSelect = document.getElementById('editRelocation');
        const editSkillsInput = document.getElementById('editSkills');
        const editSummaryInput = document.getElementById('editSummary');
        const editResumeFileInput = document.getElementById('editResumeFile');
        const currentResumePathDiv = document.getElementById('currentResumePath');
        const deleteConsultantBtn = document.getElementById('deleteConsultant');
        const statusDiv = document.getElementById('status');
        const consultantsContainer = document.getElementById('consultantsContainer');
        const consultantsList = document.getElementById('consultantsList');
        const refreshConsultantsBtn = document.getElementById('refreshConsultants');
        const importHotlistBtn = document.getElementById('importHotlist');

        // Function to fetch Gmail labels
        async function fetchGmailLabels() {
            try {
                // Clear the select options first
                labelInput.innerHTML = '<option value="">Loading labels...</option>';

                const response = await axios.get(`${API_BASE_URL}/api/gmail-labels`);
                const data = response.data;

                // Clear the select options
                labelInput.innerHTML = '';

                // Add a default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Select a label';
                labelInput.appendChild(defaultOption);

                // Add options for each label
                if (data.labels && data.labels.length > 0) {
                    data.labels.forEach(label => {
                        const option = document.createElement('option');
                        option.value = label;
                        option.textContent = label;

                        // Select the current label if it matches
                        if (data.current_label === label) {
                            option.selected = true;
                        }

                        labelInput.appendChild(option);
                    });

                    showStatus(`Loaded ${data.labels.length} Gmail labels`, 'success');
                } else {
                    // Add a message if no labels were found
                    const noLabelsOption = document.createElement('option');
                    noLabelsOption.value = '';
                    noLabelsOption.textContent = 'No labels found';
                    noLabelsOption.disabled = true;
                    labelInput.appendChild(noLabelsOption);

                    showStatus('No Gmail labels found. Please check your credentials.', 'warning');
                }
            } catch (error) {
                console.error('Error fetching Gmail labels:', error);

                // Add an error option
                labelInput.innerHTML = '';
                const errorOption = document.createElement('option');
                errorOption.value = '';
                errorOption.textContent = 'Error loading labels';
                errorOption.disabled = true;
                labelInput.appendChild(errorOption);

                // Add a manual input option
                const manualOption = document.createElement('option');
                manualOption.value = 'manual';
                manualOption.textContent = 'Enter label manually';
                labelInput.appendChild(manualOption);

                showStatus('Error loading Gmail labels. Please check your credentials.', 'error');
            }
        }

        // Load config on page load
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                // Initialize tabs first
                initializeTabs();

                const response = await axios.get(`${API_BASE_URL}/api/config`);
                const config = response.data;

                emailInput.value = config.email || '';
                passwordInput.value = config.password || '';

                // Set the current label value temporarily
                const currentLabel = config.label || '';

                // Try to fetch Gmail labels if credentials are provided
                if (config.email && config.password) {
                    try {
                        await fetchGmailLabels();
                    } catch (labelError) {
                        console.error('Error fetching labels:', labelError);

                        // If labels couldn't be fetched, set the label input value directly
                        if (currentLabel) {
                            // Create an option for the current label
                            const option = document.createElement('option');
                            option.value = currentLabel;
                            option.textContent = currentLabel;
                            option.selected = true;

                            // Clear and add the option
                            labelInput.innerHTML = '';
                            labelInput.appendChild(option);
                        }
                    }
                } else {
                    // If no credentials, just set a placeholder
                    labelInput.innerHTML = '<option value="">Enter credentials first</option>';
                }

                // Set hotlist image preview if available
                if (config.hotlist_image) {
                    hotlistImagePreview.src = `${API_BASE_URL}/api/hotlist-image/${config.hotlist_image}`;
                    currentHotlistImageDiv.classList.remove('hidden');
                }

                // Load consultants if available
                await loadConsultants();

                // Initialize bot status check
                await updateBotStatus();
            } catch (error) {
                showStatus('Error loading configuration', 'error');
                console.error('Error loading config:', error);
            }
        });

        // Refresh Gmail labels
        refreshLabelsBtn.addEventListener('click', async () => {
            try {
                // Check if email and password are provided
                if (!emailInput.value || !passwordInput.value) {
                    showStatus('Please enter your Gmail email and app password first', 'warning');
                    return;
                }

                showStatus('Fetching Gmail labels...', 'info');
                await fetchGmailLabels();
            } catch (error) {
                showStatus('Error fetching Gmail labels', 'error');
                console.error('Error fetching Gmail labels:', error);
            }
        });

        // Handle label selection change
        labelInput.addEventListener('change', function() {
            // If "Enter label manually" is selected, show a prompt
            if (this.value === 'manual') {
                const manualLabel = prompt('Enter the Gmail label name:');
                if (manualLabel) {
                    // Create a new option for the manual label
                    const option = document.createElement('option');
                    option.value = manualLabel;
                    option.textContent = manualLabel + ' (manual)';
                    option.selected = true;

                    // Remove the "Enter manually" option
                    const manualOption = Array.from(this.options).find(opt => opt.value === 'manual');
                    if (manualOption) {
                        this.removeChild(manualOption);
                    }

                    // Add the new option
                    this.appendChild(option);
                } else {
                    // If the user cancels, revert to the first option
                    this.selectedIndex = 0;
                }
            }
        });

        // Save config
        saveConfigBtn.addEventListener('click', async () => {
            try {
                showStatus('Saving configuration...', 'info');

                const config = {
                    email: emailInput.value,
                    password: passwordInput.value,
                    label: labelInput.value || 'JobRequirements'
                };

                await axios.post(`${API_BASE_URL}/api/config`, config);

                showStatus('Configuration saved successfully', 'success');

                // Refresh labels after saving config if credentials are provided
                if (config.email && config.password) {
                    try {
                        await fetchGmailLabels();
                    } catch (labelError) {
                        console.error('Error fetching labels after saving config:', labelError);
                    }
                }
            } catch (error) {
                showStatus('Error saving configuration', 'error');
                console.error('Error saving config:', error);
            }
        });

        // Upload hotlist image
        uploadHotlistImageBtn.addEventListener('click', async () => {
            try {
                if (!hotlistImageInput.files || !hotlistImageInput.files[0]) {
                    showStatus('Please select an image file', 'error');
                    return;
                }

                showStatus('Uploading hotlist image...', 'info');

                // Create form data
                const formData = new FormData();
                formData.append('file', hotlistImageInput.files[0]);

                // Upload image
                const response = await axios.post(`${API_BASE_URL}/api/upload-hotlist-image`, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                // Update preview
                if (response.data.filename) {
                    hotlistImagePreview.src = `${API_BASE_URL}/api/hotlist-image/${response.data.filename}`;
                    currentHotlistImageDiv.classList.remove('hidden');
                }

                showStatus('Hotlist image uploaded successfully', 'success');

                // Reset file input
                hotlistImageInput.value = '';

            } catch (error) {
                showStatus('Error uploading hotlist image', 'error');
                console.error('Error uploading hotlist image:', error);
            }
        });

        // Parse resumes
        parseResumesBtn.addEventListener('click', async () => {
            try {
                showStatus('Parsing resumes...', 'info');

                const response = await axios.post(`${API_BASE_URL}/api/parse-resumes`);

                showStatus(`Successfully parsed ${response.data.consultants.length} resumes`, 'success');
                displayConsultants(response.data.consultants);
            } catch (error) {
                showStatus('Error parsing resumes', 'error');
                console.error('Error parsing resumes:', error);
            }
        });

        // Function to update bot status and logs
        let statusCheckInterval = null;

        async function updateBotStatus() {
            try {
                const response = await axios.get(`${API_BASE_URL}/api/status`);
                const { status, logs } = response.data;

                // Update UI based on status
                if (status === 'running') {
                    startBotBtn.classList.add('hidden');
                    stopBotBtn.classList.remove('hidden');
                    botLogsContainer.classList.remove('hidden');
                } else {
                    startBotBtn.classList.remove('hidden');
                    stopBotBtn.classList.add('hidden');

                    // If we were previously checking status, clear the interval
                    if (statusCheckInterval) {
                        clearInterval(statusCheckInterval);
                        statusCheckInterval = null;
                    }
                }

                // Update logs
                if (logs && logs.length > 0) {
                    // Clear existing logs
                    botLogs.innerHTML = '';

                    // Add each log entry
                    logs.forEach(log => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'py-1';

                        // Add appropriate color based on log level
                        let levelClass = '';
                        switch(log.level) {
                            case 'error':
                                levelClass = 'text-red-500';
                                break;
                            case 'warning':
                                levelClass = 'text-yellow-500';
                                break;
                            case 'success':
                                levelClass = 'text-green-500';
                                break;
                            case 'info':
                            default:
                                levelClass = 'text-blue-500';
                        }

                        logEntry.innerHTML = `
                            <span class="text-gray-400">[${log.timestamp}]</span>
                            <span class="${levelClass}">[${log.level.toUpperCase()}]</span>
                            <span>${log.message}</span>
                        `;

                        botLogs.appendChild(logEntry);
                    });

                    // Scroll to bottom
                    botLogs.scrollTop = botLogs.scrollHeight;
                }
            } catch (error) {
                console.error('Error checking bot status:', error);
            }
        }

        // Start bot
        startBotBtn.addEventListener('click', async () => {
            try {
                showStatus('Starting auto-reply bot...', 'info');

                // Show logs container
                botLogsContainer.classList.remove('hidden');
                botLogs.innerHTML = '<div class="py-1 text-gray-400">Starting bot...</div>';

                // Start the bot
                const response = await axios.post(`${API_BASE_URL}/api/start`);

                // Update UI
                startBotBtn.classList.add('hidden');
                stopBotBtn.classList.remove('hidden');

                // Start checking status periodically
                if (statusCheckInterval) {
                    clearInterval(statusCheckInterval);
                }

                // Update status immediately
                await updateBotStatus();

                // Then set interval to check every 2 seconds
                statusCheckInterval = setInterval(updateBotStatus, 2000);

                showStatus('Bot started successfully', 'success');
            } catch (error) {
                showStatus('Error starting auto-reply bot', 'error');
                console.error('Error starting bot:', error);
                botLogs.innerHTML += `<div class="py-1 text-red-500">Error: ${error.message}</div>`;
            }
        });

        // Stop bot
        stopBotBtn.addEventListener('click', async () => {
            try {
                showStatus('Stopping auto-reply bot...', 'warning');

                // Add log entry
                botLogs.innerHTML += '<div class="py-1 text-yellow-500">Stopping bot...</div>';

                // Stop the bot
                const response = await axios.post(`${API_BASE_URL}/api/stop`);

                // Update status immediately
                await updateBotStatus();

                showStatus('Bot stop requested. It will stop after current operation completes.', 'warning');
            } catch (error) {
                showStatus('Error stopping auto-reply bot', 'error');
                console.error('Error stopping bot:', error);
                botLogs.innerHTML += `<div class="py-1 text-red-500">Error stopping bot: ${error.message}</div>`;
            }
        });

        // Add event listeners for additional tab buttons
        document.addEventListener('DOMContentLoaded', () => {
            // Refresh consultants button in consultants tab
            const refreshConsultants2 = document.getElementById('refreshConsultants2');
            if (refreshConsultants2) {
                refreshConsultants2.addEventListener('click', loadConsultants);
            }

            // Import hotlist button in consultants tab
            const importHotlist2 = document.getElementById('importHotlist2');
            if (importHotlist2) {
                importHotlist2.addEventListener('click', async () => {
                    try {
                        showStatus('Importing hotlist data...', 'info');
                        const response = await axios.post(`${API_BASE_URL}/api/import-hotlist`);
                        showStatus(`Successfully imported ${response.data.count} consultants from hotlist`, 'success');
                        await loadConsultants();
                    } catch (error) {
                        showStatus('Error importing hotlist data', 'error');
                        console.error('Error importing hotlist:', error);
                    }
                });
            }

            // Search consultants in consultants tab
            const searchConsultants2 = document.getElementById('searchConsultants2');
            if (searchConsultants2) {
                searchConsultants2.addEventListener('input', (e) => {
                    filterConsultants(e.target.value);
                });
            }

            // Sort consultants in consultants tab
            const sortConsultants2 = document.getElementById('sortConsultants2');
            if (sortConsultants2) {
                sortConsultants2.addEventListener('change', (e) => {
                    sortConsultants(e.target.value);
                });
            }

            // Refresh skills button
            const refreshSkills = document.getElementById('refreshSkills');
            if (refreshSkills) {
                refreshSkills.addEventListener('click', loadSkillsData);
            }
        });





        // Upload Resume button
        uploadResumeBtn.addEventListener('click', () => {
            uploadModal.classList.remove('hidden');
        });

        // Close Upload modal
        closeUploadModalBtn.addEventListener('click', () => {
            uploadModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        uploadModal.addEventListener('click', (e) => {
            if (e.target === uploadModal) {
                uploadModal.classList.add('hidden');
            }
        });

        // Resume Upload form submit
        resumeUploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                const consultantName = consultantNameInput.value.trim();
                const resumeFile = resumeFileInput.files[0];

                if (!consultantName) {
                    showStatus('Please enter a consultant name', 'error');
                    return;
                }

                if (!resumeFile) {
                    showStatus('Please select a resume file', 'error');
                    return;
                }

                showStatus('Uploading resume...', 'info');

                // Create form data
                const formData = new FormData();
                formData.append('consultant_name', consultantName);
                formData.append('file', resumeFile);

                // Upload resume
                const response = await axios.post(`${API_BASE_URL}/api/upload-resume`, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                showStatus(`Successfully uploaded and parsed resume for ${consultantName}`, 'success');

                // Display the consultant
                if (response.data.consultant) {
                    displayConsultants([response.data.consultant]);
                }

                // Reset form
                resumeUploadForm.reset();

                // Close modal
                uploadModal.classList.add('hidden');

            } catch (error) {
                showStatus('Error uploading resume', 'error');
                console.error('Error uploading resume:', error);
            }
        });

        // Show Edit Consultant Modal
        function showEditConsultantModal(consultant, index) {
            // Set consultant ID (index)
            editConsultantIdInput.value = index;

            // Fill form with consultant data
            editNameInput.value = consultant.name || '';
            editExperienceInput.value = consultant.years_experience || '';
            editLocationInput.value = consultant.location || '';
            editVisaStatusInput.value = consultant.visa_status || '';
            editAvailabilityInput.value = consultant.availability || '';

            // Set relocation preference
            if (consultant.relocation) {
                // Find the matching option
                const options = Array.from(editRelocationSelect.options);
                const matchingOption = options.find(option =>
                    option.value && consultant.relocation.toLowerCase().includes(option.value.toLowerCase())
                );

                if (matchingOption) {
                    editRelocationSelect.value = matchingOption.value;
                } else {
                    // If no match, set to empty and add a custom option
                    const customOption = document.createElement('option');
                    customOption.value = consultant.relocation;
                    customOption.textContent = consultant.relocation;
                    editRelocationSelect.appendChild(customOption);
                    editRelocationSelect.value = consultant.relocation;
                }
            } else {
                editRelocationSelect.value = '';
            }

            editSkillsInput.value = consultant.skills ? consultant.skills.join(', ') : '';
            editSummaryInput.value = consultant.summary || '';

            // Reset file input
            editResumeFileInput.value = '';

            // Show current resume path if available
            if (consultant.resume_path) {
                const resumeFilename = consultant.resume_path.split('\\').pop().split('/').pop();
                currentResumePathDiv.innerHTML = `
                    <span class="font-semibold">Current Resume:</span> ${resumeFilename}
                    <a href="${API_BASE_URL}/api/view-resume/${encodeURIComponent(resumeFilename)}"
                       class="text-blue-500 hover:underline ml-2" target="_blank">
                       View
                    </a>
                `;
            } else {
                currentResumePathDiv.textContent = 'No resume currently attached';
            }

            // Show modal
            editConsultantModal.classList.remove('hidden');
        }

        // Close Edit Modal
        closeEditModalBtn.addEventListener('click', () => {
            editConsultantModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        editConsultantModal.addEventListener('click', (e) => {
            if (e.target === editConsultantModal) {
                editConsultantModal.classList.add('hidden');
            }
        });

        // Edit Consultant form submit
        editConsultantForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                const consultantId = parseInt(editConsultantIdInput.value);
                const name = editNameInput.value.trim();
                const resumeFile = editResumeFileInput.files[0];

                if (!name) {
                    showStatus('Please enter a consultant name', 'error');
                    return;
                }

                showStatus('Updating consultant...', 'info');

                // Parse skills from comma-separated string
                const skillsText = editSkillsInput.value.trim();
                const skills = skillsText ? skillsText.split(',').map(s => s.trim()).filter(s => s) : [];

                // Prepare the consultant data
                const consultantData = {
                    name: name,
                    skills: skills
                };

                // Add optional fields if provided
                if (editExperienceInput.value) {
                    consultantData.years_experience = parseInt(editExperienceInput.value);
                }

                if (editLocationInput.value) {
                    consultantData.location = editLocationInput.value.trim();
                }

                if (editVisaStatusInput.value) {
                    consultantData.visa_status = editVisaStatusInput.value.trim();
                }

                if (editAvailabilityInput.value) {
                    consultantData.availability = editAvailabilityInput.value.trim();
                }

                if (editRelocationSelect.value) {
                    consultantData.relocation = editRelocationSelect.value;
                }

                if (editSummaryInput.value) {
                    consultantData.summary = editSummaryInput.value.trim();
                }

                // Check if a new resume file was selected
                if (resumeFile) {
                    // If a new resume was uploaded, handle it separately
                    showStatus('Uploading new resume...', 'info');

                    try {
                        // First update the consultant data without the resume
                        await axios.put(`${API_BASE_URL}/api/consultants/${consultantId}`, consultantData);

                        // Then handle the resume upload separately
                        // Create form data for file upload
                        const formData = new FormData();
                        formData.append('consultant_name', name);
                        formData.append('file', resumeFile);

                        // Upload the new resume
                        await axios.post(`${API_BASE_URL}/api/upload-resume`, formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        });

                        showStatus(`Successfully updated consultant ${name} with new resume`, 'success');
                    } catch (uploadError) {
                        console.error('Error during resume upload:', uploadError);
                        showStatus('Error uploading new resume, but consultant data was updated', 'warning');
                    }
                } else {
                    // No new resume, just update the consultant data
                    await axios.put(`${API_BASE_URL}/api/consultants/${consultantId}`, consultantData);
                    showStatus(`Successfully updated consultant ${name}`, 'success');
                }

                // Reload consultants
                await loadConsultants();

                // Close modal
                editConsultantModal.classList.add('hidden');

            } catch (error) {
                showStatus('Error updating consultant', 'error');
                console.error('Error updating consultant:', error);
            }
        });

        // Delete Consultant button
        deleteConsultantBtn.addEventListener('click', async () => {
            try {
                const consultantId = parseInt(editConsultantIdInput.value);
                const name = editNameInput.value.trim();

                // Confirm deletion
                if (!confirm(`Are you sure you want to delete consultant "${name}"?`)) {
                    return;
                }

                showStatus('Deleting consultant...', 'info');

                // Delete consultant
                const response = await axios.delete(`${API_BASE_URL}/api/consultants/${consultantId}`);

                showStatus(`Successfully deleted consultant ${name}`, 'success');

                // Reload consultants
                await loadConsultants();

                // Close modal
                editConsultantModal.classList.add('hidden');

            } catch (error) {
                showStatus('Error deleting consultant', 'error');
                console.error('Error deleting consultant:', error);
            }
        });

        // Load consultants
        async function loadConsultants() {
            try {
                const response = await axios.get(`${API_BASE_URL}/api/consultants`);
                console.log('API Response:', response.data); // Debug log

                // Handle the correct API response format
                let consultants = [];
                if (response.data && response.data.consultants && Array.isArray(response.data.consultants)) {
                    consultants = response.data.consultants;
                } else if (response.data && Array.isArray(response.data)) {
                    consultants = response.data;
                }

                console.log('Consultants to display:', consultants.length); // Debug log

                if (consultants.length > 0) {
                    displayConsultants(consultants);
                } else {
                    console.log('No consultants found to display');
                    // Show empty state
                    consultantsContainer.classList.add('hidden');
                }
            } catch (error) {
                console.error('Error loading consultants:', error);
                showStatus('Error loading consultant data', 'error');
            }
        }

        // Display consultants with enhanced UI
        function displayConsultants(consultants) {
            if (consultants.length === 0) {
                consultantsContainer.classList.add('hidden');
                return;
            }

            // Clear existing list
            consultantsList.innerHTML = '';

            // Add event listeners for search and sort
            const searchInput = document.getElementById('searchConsultants');
            const sortSelect = document.getElementById('sortConsultants');

            searchInput.addEventListener('input', () => {
                filterAndSortConsultants(consultants, searchInput.value, sortSelect.value);
            });

            sortSelect.addEventListener('change', () => {
                filterAndSortConsultants(consultants, searchInput.value, sortSelect.value);
            });

            // Display all consultants initially
            filterAndSortConsultants(consultants, '', 'name');

            // Set up modal functionality
            const modal = document.getElementById('consultantModal');
            const closeModalBtn = document.getElementById('closeModal');

            closeModalBtn.addEventListener('click', () => {
                modal.classList.add('hidden');
            });

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });

            consultantsContainer.classList.remove('hidden');
        }

        // Filter and sort consultants based on search term and sort criteria - optimized for performance
        function filterAndSortConsultants(consultants, searchTerm, sortBy) {
            // Cache for performance optimization
            if (!window._lastConsultants || window._lastConsultants !== consultants) {
                window._lastConsultants = consultants;

                // Pre-compute lowercase searchable text for each consultant
                consultants.forEach(consultant => {
                    // Create a combined searchable text from all relevant fields
                    const searchableFields = [
                        consultant.name || '',
                        consultant.summary || '',
                        consultant.location || '',
                        consultant.visa_status || ''
                    ];

                    // Add skills to searchable text
                    if (consultant.skills) {
                        if (Array.isArray(consultant.skills)) {
                            consultant._skillsText = consultant.skills.join(' ').toLowerCase();
                        } else if (typeof consultant.skills === 'string') {
                            consultant._skillsText = consultant.skills.toLowerCase();
                            // Convert string skills to array for consistent handling
                            consultant.skills_list = consultant.skills.split(',').map(s => s.trim()).filter(s => s);
                        } else {
                            consultant._skillsText = '';
                        }
                        searchableFields.push(consultant._skillsText);
                    } else {
                        consultant._skillsText = '';
                        consultant.skills_list = [];
                    }

                    // Create a single searchable text string
                    consultant._searchableText = searchableFields.join(' ').toLowerCase();
                });
            }

            // Filter consultants with optimized approach
            let filteredConsultants = consultants;

            if (searchTerm) {
                // Split search term into words for better matching
                searchTerm = searchTerm.toLowerCase();
                const searchTerms = searchTerm.split(/\s+/).filter(term => term.length > 0);

                if (searchTerms.length > 0) {
                    filteredConsultants = consultants.filter(consultant => {
                        // Check if all search terms are found in the searchable text
                        return searchTerms.every(term => consultant._searchableText.includes(term));
                    });
                }
            }

            // Sort consultants with memoization for expensive operations
            const sortKey = sortBy + '_' + (window._lastSortKey || '');
            if (!window._lastSortKey || window._lastSortKey !== sortKey) {
                window._lastSortKey = sortKey;

                // Apply sorting
                filteredConsultants.sort((a, b) => {
                    if (sortBy === 'name') {
                        return (a.name || '').localeCompare(b.name || '');
                    } else if (sortBy === 'skills') {
                        // Handle both string and array skills formats
                        let skillsA = 0;
                        if (Array.isArray(a.skills)) {
                            skillsA = a.skills.length;
                        } else if (a.skills_list && Array.isArray(a.skills_list)) {
                            skillsA = a.skills_list.length;
                        } else if (typeof a.skills === 'string') {
                            skillsA = a.skills.split(',').filter(s => s.trim()).length;
                        }

                        let skillsB = 0;
                        if (Array.isArray(b.skills)) {
                            skillsB = b.skills.length;
                        } else if (b.skills_list && Array.isArray(b.skills_list)) {
                            skillsB = b.skills_list.length;
                        } else if (typeof b.skills === 'string') {
                            skillsB = b.skills.split(',').filter(s => s.trim()).length;
                        }

                        return skillsB - skillsA;
                    } else if (sortBy === 'experience') {
                        const expA = a.years_experience || 0;
                        const expB = b.years_experience || 0;
                        return expB - expA;
                    }
                    return 0;
                });
            }

            // Update the display with debouncing for better performance
            if (window._renderTimeout) {
                clearTimeout(window._renderTimeout);
            }

            window._renderTimeout = setTimeout(() => {
                renderConsultantsList(filteredConsultants);

                // Update visible count for both tabs
                const visibleConsultants1 = document.getElementById('visibleConsultants');
                const totalConsultantsCount1 = document.getElementById('totalConsultantsCount');
                const visibleConsultants2 = document.getElementById('visibleConsultants2');
                const totalConsultantsCount2 = document.getElementById('totalConsultantsCount2');

                if (visibleConsultants1) visibleConsultants1.textContent = filteredConsultants.length;
                if (totalConsultantsCount1) totalConsultantsCount1.textContent = consultants.length;
                if (visibleConsultants2) visibleConsultants2.textContent = filteredConsultants.length;
                if (totalConsultantsCount2) totalConsultantsCount2.textContent = consultants.length;
            }, 50); // Small delay for debouncing
        }

        // Render the consultants list - optimized for performance
        function renderConsultantsList(consultants) {
            // Use document fragment for better performance
            const fragment = document.createDocumentFragment();

            // Cache common class names
            const cellClass = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
            const nameCellClass = 'px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900';
            const skillBadgeClass = 'px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs';
            const moreBadgeClass = 'px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs';

            // Reuse template for better performance
            if (!window._rowTemplate) {
                // Create template elements once
                window._rowTemplate = {
                    row: document.createElement('tr'),
                    nameCell: document.createElement('td'),
                    expCell: document.createElement('td'),
                    visaCell: document.createElement('td'),
                    availabilityCell: document.createElement('td'),
                    relocationCell: document.createElement('td'),
                    skillsCell: document.createElement('td'),
                    skillsContainer: document.createElement('div'),
                    actionsCell: document.createElement('td'),
                    actionsContainer: document.createElement('div'),
                    viewButton: document.createElement('button'),
                    editButton: document.createElement('button')
                };

                // Set static classes
                window._rowTemplate.row.className = 'hover:bg-gray-50';
                window._rowTemplate.nameCell.className = nameCellClass;
                window._rowTemplate.expCell.className = cellClass;
                window._rowTemplate.visaCell.className = cellClass;
                window._rowTemplate.availabilityCell.className = cellClass;
                window._rowTemplate.relocationCell.className = cellClass;
                window._rowTemplate.skillsCell.className = 'px-6 py-4 text-sm text-gray-500';
                window._rowTemplate.skillsContainer.className = 'flex flex-wrap gap-1';
                window._rowTemplate.actionsCell.className = cellClass;
                window._rowTemplate.actionsContainer.className = 'flex flex-col space-y-2';
                window._rowTemplate.viewButton.className = 'text-blue-600 hover:text-blue-900';
                window._rowTemplate.viewButton.textContent = 'View Details';
                window._rowTemplate.editButton.className = 'text-indigo-600 hover:text-indigo-900';
                window._rowTemplate.editButton.textContent = 'Edit';
            }

            // Clear existing content for both tables
            const consultantsList1 = document.getElementById('consultantsList');
            const consultantsList2 = document.getElementById('consultantsList2');

            if (consultantsList1) consultantsList1.innerHTML = '';
            if (consultantsList2) consultantsList2.innerHTML = '';

            // Batch process consultants for better performance
            const batchSize = 50;
            const totalConsultants = consultants.length;

            function processBatch(startIndex) {
                const endIndex = Math.min(startIndex + batchSize, totalConsultants);

                for (let i = startIndex; i < endIndex; i++) {
                    const consultant = consultants[i];

                    // Clone template elements
                    const row = window._rowTemplate.row.cloneNode(false);
                    const nameCell = window._rowTemplate.nameCell.cloneNode(false);
                    const expCell = window._rowTemplate.expCell.cloneNode(false);
                    const visaCell = window._rowTemplate.visaCell.cloneNode(false);
                    const availabilityCell = window._rowTemplate.availabilityCell.cloneNode(false);
                    const relocationCell = window._rowTemplate.relocationCell.cloneNode(false);
                    const skillsCell = window._rowTemplate.skillsCell.cloneNode(false);
                    const skillsContainer = window._rowTemplate.skillsContainer.cloneNode(false);
                    const actionsCell = window._rowTemplate.actionsCell.cloneNode(false);
                    const actionsContainer = window._rowTemplate.actionsContainer.cloneNode(false);

                    // Set content
                    nameCell.textContent = consultant.name;

                    // Always show experience as a number (default to 5 if not specified)
                    const experience = typeof consultant.years_experience === 'number' && consultant.years_experience > 0
                        ? consultant.years_experience
                        : 5;
                    expCell.textContent = `${experience} years`;

                    visaCell.textContent = consultant.visa_status || 'N/A';
                    availabilityCell.textContent = consultant.availability || 'N/A';

                    // Set relocation with appropriate styling
                    if (consultant.relocation) {
                        if (consultant.relocation.toLowerCase().includes('yes')) {
                            relocationCell.innerHTML = '<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Yes</span>';
                        } else if (consultant.relocation.toLowerCase().includes('remote')) {
                            relocationCell.innerHTML = '<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Remote Only</span>';
                        } else {
                            relocationCell.textContent = consultant.relocation;
                        }
                    } else {
                        relocationCell.textContent = 'N/A';
                    }

                    // Add skills (limit to 5) - handle both string and array formats
                    let skillsArray = [];
                    if (Array.isArray(consultant.skills)) {
                        skillsArray = consultant.skills;
                    } else if (consultant.skills_list && Array.isArray(consultant.skills_list)) {
                        skillsArray = consultant.skills_list;
                    } else if (typeof consultant.skills === 'string') {
                        skillsArray = consultant.skills.split(',').map(s => s.trim()).filter(s => s);
                    }

                    const displaySkills = skillsArray.slice(0, 5);
                    const remainingCount = skillsArray.length - 5;

                    // Use innerHTML for better performance with many skills
                    let skillsHTML = '';
                    for (let j = 0; j < displaySkills.length; j++) {
                        skillsHTML += `<span class="${skillBadgeClass}">${displaySkills[j]}</span> `;
                    }

                    if (remainingCount > 0) {
                        skillsHTML += `<span class="${moreBadgeClass}">+${remainingCount} more</span>`;
                    }

                    skillsContainer.innerHTML = skillsHTML;
                    skillsCell.appendChild(skillsContainer);

                    // Add action buttons
                    const viewButton = window._rowTemplate.viewButton.cloneNode(true);
                    viewButton.addEventListener('click', () => showConsultantDetails(consultant));
                    actionsContainer.appendChild(viewButton);

                    // Add resume button if available
                    if (consultant.resume_path) {
                        const resumeFilename = consultant.resume_path.split('\\').pop().split('/').pop();
                        const viewResumeButton = document.createElement('a');
                        viewResumeButton.className = 'text-green-600 hover:text-green-900';
                        viewResumeButton.textContent = 'View Resume';
                        viewResumeButton.href = `${API_BASE_URL}/api/view-resume/${encodeURIComponent(resumeFilename)}`;
                        viewResumeButton.target = '_blank';
                        actionsContainer.appendChild(viewResumeButton);
                    }

                    // Add edit button
                    const editButton = window._rowTemplate.editButton.cloneNode(true);
                    editButton.dataset.index = i;
                    editButton.addEventListener('click', () => showEditConsultantModal(consultant, i));
                    actionsContainer.appendChild(editButton);

                    actionsCell.appendChild(actionsContainer);

                    // Add cells to row
                    row.appendChild(nameCell);
                    row.appendChild(expCell);
                    row.appendChild(visaCell);
                    row.appendChild(availabilityCell);
                    row.appendChild(relocationCell);
                    row.appendChild(skillsCell);
                    row.appendChild(actionsCell);

                    // Add row to fragment
                    fragment.appendChild(row);
                }

                // If there are more consultants to process, schedule the next batch
                if (endIndex < totalConsultants) {
                    setTimeout(() => {
                        processBatch(endIndex);
                    }, 0);
                } else {
                    // All batches processed, append the fragment to both tables
                    if (consultantsList1) consultantsList1.appendChild(fragment.cloneNode(true));
                    if (consultantsList2) consultantsList2.appendChild(fragment);
                }
            }

            // Start processing the first batch
            processBatch(0);
        }

        // Show consultant details in modal
        function showConsultantDetails(consultant) {
            const modal = document.getElementById('consultantModal');

            // Set consultant name
            document.getElementById('modalConsultantName').textContent = consultant.name;

            // Set consultant summary
            document.getElementById('modalConsultantSummary').textContent =
                consultant.summary || 'No summary available for this consultant.';

            // Set consultant skills - handle both string and array formats
            const skillsContainer = document.getElementById('modalConsultantSkills');
            skillsContainer.innerHTML = '';

            let skillsArray = [];
            if (Array.isArray(consultant.skills)) {
                skillsArray = consultant.skills;
            } else if (consultant.skills_list && Array.isArray(consultant.skills_list)) {
                skillsArray = consultant.skills_list;
            } else if (typeof consultant.skills === 'string') {
                skillsArray = consultant.skills.split(',').map(s => s.trim()).filter(s => s);
            }

            skillsArray.forEach(skill => {
                const skillBadge = document.createElement('span');
                skillBadge.className = 'px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs';
                skillBadge.textContent = skill;
                skillsContainer.appendChild(skillBadge);
            });

            // Set consultant details - always show experience as a number
            const experience = typeof consultant.years_experience === 'number' && consultant.years_experience > 0
                ? consultant.years_experience
                : 5;
            document.getElementById('modalConsultantExperience').textContent = `${experience} years`;

            document.getElementById('modalConsultantVisa').textContent =
                consultant.visa_status || 'Not specified';

            document.getElementById('modalConsultantAvailability').textContent =
                consultant.availability || 'Not specified';

            // Set relocation with appropriate styling
            const relocationElement = document.getElementById('modalConsultantRelocation');
            if (consultant.relocation) {
                if (consultant.relocation.toLowerCase().includes('yes')) {
                    relocationElement.innerHTML = '<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Yes</span>';
                } else if (consultant.relocation.toLowerCase().includes('remote')) {
                    relocationElement.innerHTML = '<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Remote Only</span>';
                } else {
                    relocationElement.textContent = consultant.relocation;
                }
            } else {
                relocationElement.textContent = 'Not specified';
            }

            document.getElementById('modalConsultantSkillCount').textContent =
                skillsArray.length;

            document.getElementById('modalConsultantFileType').textContent =
                consultant.file_type || 'Unknown';

            document.getElementById('modalConsultantWordCount').textContent =
                consultant.word_count ? consultant.word_count.toLocaleString() : 'Unknown';

            // Set resume links
            const viewResumeLink = document.getElementById('modalViewResume');
            const downloadResumeLink = document.getElementById('modalDownloadResume');

            if (consultant.resume_path) {
                const resumeFilename = consultant.resume_path.split('\\').pop().split('/').pop();

                // Set view link
                viewResumeLink.href = `${API_BASE_URL}/api/view-resume/${encodeURIComponent(resumeFilename)}`;
                viewResumeLink.classList.remove('hidden');

                // Set download link
                downloadResumeLink.href = `${API_BASE_URL}/api/resumes/${encodeURIComponent(resumeFilename)}`;
                downloadResumeLink.classList.remove('hidden');
            } else {
                viewResumeLink.classList.add('hidden');
                downloadResumeLink.classList.add('hidden');
            }

            // Show modal
            modal.classList.remove('hidden');
        }

        // Consultant statistics function removed

        // Show status message
        function showStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.classList.remove('hidden', 'bg-green-100', 'bg-red-100', 'bg-blue-100');

            switch (type) {
                case 'success':
                    statusDiv.classList.add('bg-green-100', 'text-green-800');
                    break;
                case 'error':
                    statusDiv.classList.add('bg-red-100', 'text-red-800');
                    break;
                default:
                    statusDiv.classList.add('bg-blue-100', 'text-blue-800');
            }
        }

        // Refresh consultants button
        refreshConsultantsBtn.addEventListener('click', async () => {
            try {
                showStatus('Refreshing consultant data...', 'info');
                await loadConsultants();
                showStatus('Consultant data refreshed successfully', 'success');
            } catch (error) {
                showStatus('Error refreshing consultant data', 'error');
                console.error('Error refreshing consultant data:', error);
            }
        });

        // Import Hotlist function
        async function importHotlist() {
            try {
                showStatus('Importing consultant data from hotlist.csv...', 'info');

                const response = await axios.post(`${API_BASE_URL}/api/import-hotlist`);

                if (response.data.success) {
                    showStatus(`Successfully imported ${response.data.count} consultants from hotlist.csv`, 'success');
                    // Refresh the consultant display
                    await loadConsultants();
                } else {
                    showStatus('Failed to import hotlist data', 'error');
                }
            } catch (error) {
                showStatus('Error importing hotlist data', 'error');
                console.error('Error importing hotlist:', error);
            }
        }

        // Import Hotlist button
        importHotlistBtn.addEventListener('click', importHotlist);

        // Accessibility features
        document.addEventListener('DOMContentLoaded', function() {
            // Add accessibility toolbar
            const toolbar = document.createElement('div');
            toolbar.className = 'accessibility-toolbar';
            toolbar.innerHTML = `
                <button id="highContrastToggle" class="accessibility-button" title="Toggle high contrast">
                    <i class="fas fa-adjust"></i>
                </button>
                <button id="largeTextToggle" class="accessibility-button" title="Toggle large text">
                    <i class="fas fa-text-height"></i>
                </button>
                <button id="reduceMotionToggle" class="accessibility-button" title="Reduce motion">
                    <i class="fas fa-running"></i>
                </button>
                <button id="readAloudToggle" class="accessibility-button" title="Read aloud">
                    <i class="fas fa-volume-up"></i>
                </button>
            `;
            document.body.appendChild(toolbar);

            // High contrast toggle
            const highContrastToggle = document.getElementById('highContrastToggle');
            highContrastToggle.addEventListener('click', function() {
                document.body.classList.toggle('high-contrast');
                this.classList.toggle('active');
            });

            // Large text toggle
            const largeTextToggle = document.getElementById('largeTextToggle');
            largeTextToggle.addEventListener('click', function() {
                document.body.classList.toggle('large-text');
                this.classList.toggle('active');
            });

            // Reduce motion toggle
            const reduceMotionToggle = document.getElementById('reduceMotionToggle');
            reduceMotionToggle.addEventListener('click', function() {
                document.body.classList.toggle('reduce-motion');
                this.classList.toggle('active');

                if (document.body.classList.contains('reduce-motion')) {
                    document.body.style.setProperty('--transition-duration', '0.001ms');
                } else {
                    document.body.style.removeProperty('--transition-duration');
                }
            });

            // Read aloud functionality
            const readAloudToggle = document.getElementById('readAloudToggle');
            let speechSynthesis = window.speechSynthesis;
            let speaking = false;

            readAloudToggle.addEventListener('click', function() {
                if (speaking) {
                    speechSynthesis.cancel();
                    speaking = false;
                    this.classList.remove('active');
                } else {
                    // Get all headings and paragraphs
                    const elements = document.querySelectorAll('h1, h2, h3, h4, p, li, button:not(.accessibility-button)');
                    const texts = Array.from(elements).map(el => el.textContent.trim()).filter(text => text.length > 0);

                    // Create utterance
                    const utterance = new SpeechSynthesisUtterance(texts.join('. '));
                    utterance.lang = 'en-US';
                    utterance.rate = 1;
                    utterance.pitch = 1;

                    // Start speaking
                    speechSynthesis.speak(utterance);
                    speaking = true;
                    this.classList.add('active');

                    // When finished
                    utterance.onend = function() {
                        speaking = false;
                        readAloudToggle.classList.remove('active');
                    };
                }
            });
        });
    </script>
</body>
</html>
